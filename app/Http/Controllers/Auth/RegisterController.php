<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\EventStatusHandler;
use App\Models\Invite\Invite;
use App\Models\Organization\TeamInvite;
use App\Models\Organization\Organization;
use App\Providers\RouteServiceProvider;
use App\User;
use Illuminate\Foundation\Auth\RegistersUsers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Facades\DB;
use Lunaweb\RecaptchaV3\Facades\RecaptchaV3;
use \Exception;
use Inertia\Inertia;


class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except(["invite", "setPassword", "saveShopifyPassword"]);
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @param  array  $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        $validator = Validator::make($data, [
            'fname' => ['required', 'string', 'max:75', 'regex:/^[a-zA-Z]+$/u'],
            'lname' => ['required', 'string', 'max:75', 'regex:/^[a-zA-Z]+$/u'],
            'phone' => 'nullable|regex:/^([0-9\s\-\+\(\)]*)$/|min:10|max:20',
            'email' => ['required', 'string', 'email', 'max:255'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            //  'g-recaptcha-response' => [App::environment('testing') ? 'nullable' : 'required']
        ], [
            'fname.required' => 'First name cannot be empty',
            'fname.regex' => 'Please Enter Valid First Name',
            'fname.min' => 'First name should not be less than 3 characters',
            'fname.max' => 'First name should not be greater than 255 characters',
            'lname.required' => 'Last name cannot be empty',
            'lname.regex' => 'Please Enter Valid Last Name',
            'lname.min' => 'Last name should not be less than 3 characters',
            'lname.max' => 'Last name should not be greater than 255 characters',
            'email.required' => 'Email cannot be empty',
            'email.max' => 'Email should not be greater than 255 characters',
            'phone.max' => 'Phone number should not be greater than 20 digits',
            'phone.min' => 'Phone number should not be less than 10 digits'
        ]);
        if ($validator->fails())
            return $validator;
        $validator->after(function ($validator) {
            $data = $validator->getData();
            if (User::where('email', $data["email"])->whereNotNull('password')->count() > 0)
                $validator->errors()->add('email', 'The email has already been taken.');
        });
        return $validator;
    }

    /**
     * Create a new user instance after a valid registration.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function createAccount(Request $request)
    {
        $this->validator($request->all())->validate();
        try {
            if (App::environment('testing')) {
                $score = RecaptchaV3::verify($request->get('g-recaptcha-response'), 'register');
                if ($score < 0.5) {
                    return back()->withErrors('main', 'You are most likely a bot');
                }
            }
        } catch (\Exception $e) {

            Log::error($e);
        }

        DB::beginTransaction();
        try {
            $data = $request->only(['fname', 'lname', 'phone', 'email', 'password', 'password_confirmation', 'ip', 'g-recaptcha-response', 'is_invitation']);

            $this->validator($data)->validate();
            if (!isset($data['phone'])) {
                return back()->withErrors(['phone' => 'Phone number is required.']);
            }
            //check if user is already register with google or not
            $existingUser = User::where('email', $data['email'])->whereNotNull('google_id')->whereNull('password')->first();
            if ($existingUser) {
                return Inertia::render('auth/ManualLinking', [
                    'existingUser' => $existingUser->only(['id', 'email', 'fname', 'lname', 'google_id']),
                    'formData' => $data,
                    'csrf_token' => csrf_token()
                ]);
            }

            $user = new User();
            $user->fname = $data['fname'];
            $user->lname = $data['lname'];
            $user->email = $data['email'];
            $user->phone = $data['phone'];
            $user->password = Hash::make($data['password']);
            $user->save();

            DB::commit();

            auth()->login($user, true);
            $user->sendEmailVerificationNotification();

            // Check if this is an invitation registration or if user has team invitations
            $isInvitationRegistration = $request->boolean('is_invitation');
            $teamInvitation = $this->checkForTeamInvitation($user->email);

            if ($isInvitationRegistration || $teamInvitation) {
                // Store invitation flag in session for post-verification handling
                session(['is_invitation_registration' => true]);
                return $this->handleTeamInvitationRegistration($user, $teamInvitation);
            }

            return redirect()->route('home');
        } catch (Exception $e) {
            Log::error($e);
            DB::rollBack();
            return back()->withErrors('message', 'Something went wrong. Please try again later.');
        }
    }

    /**
     * Redirect the user to the Google authentication page.
     *
     * @return \Illuminate\Http\Response
     */
    public function googleRedirect()
    {
        return Socialite::driver('google')->redirect();
    }

    /**
     * Obtain the user information from Google.
     *
     * @return \Illuminate\Http\Response
     */
    public function handleProviderCallback()
    {
        try {
            $user = Socialite::driver('google')->user();
        } catch (\Exception $e) {
            return redirect('/login');
        }
        //check if user is register with google or not
        if (session()->has('user_id')) {
            $existingUser1 = User::where('id', session('user_id'))->where('email', $user->email)->whereNotNull('google_id')->first();
            if ($existingUser1) {
                return $this->saveAccountPassword();
            } else {
                return back()->withErrors('message', 'Select the correct email address');
            }
        }
        // check if they're an existing user
        $existingUser = User::where('email', $user->email)->whereNull('google_id')->first();
        if ($existingUser) {
            return view('auth.google_linking', compact(['existingUser', 'user']));
        } else {
            $existingUser = User::where('email', $user->email)->first();
            if ($existingUser) {
                auth()->login($existingUser, true);
                return redirect()->route('home');
            } else {
                // create a new user
                $newUser = new User;
                $newUser->fname = $user->name;
                $newUser->email = $user->email;
                $newUser->google_id = $user->id;
                $newUser->email_verified_at = now();
                $newUser->save();
                auth()->login($newUser, true);

                // Check if user is registering from a team invitation
                $teamInvitation = $this->checkForTeamInvitation($newUser->email);
                if ($teamInvitation) {
                    return $this->handleTeamInvitationRegistration($newUser, $teamInvitation);
                }
            }
        }

        // Check if existing user has team invitations
        $teamInvitation = $this->checkForTeamInvitation(auth()->user()->email);
        if ($teamInvitation) {
            return $this->handleTeamInvitationRegistration(auth()->user(), $teamInvitation);
        }

        return redirect()->route('onboarding');
    }

    /**
     * link google with manual account.
     *
     * @return \Illuminate\Http\Response
     */

    public function linkUser(Request $request)
    {
        DB::beginTransaction();
        try {
            $user = User::findOrFail($request->id);
            if ($request->has('google_id')) {
                $user->google_id = $request->google_id;
                $user->email_verified_at = now();
                $user->save();
                DB::commit();
                auth()->login($user, true);

                // Check if user has team invitations
                $teamInvitation = $this->checkForTeamInvitation($user->email);
                if ($teamInvitation) {
                    return $this->handleTeamInvitationRegistration($user, $teamInvitation);
                }

                return redirect()->route('home');
            }

            if ($request->has('password')) {

                if ($user->google_id != null) {
                    session(['password' => $request->password, 'user_id' => $request->id]);
                    return $this->googleRedirect();
                }
            }
        } catch (Exception $e) {
            DB::rollBack();
            return back()->withErrors('message', 'Something went wrong. Please try again later.');
        }
    }

    private function saveAccountPassword()
    {
        DB::beginTransaction();
        try {
            $user = User::findOrFail(session('user_id'));
            $user->password = Hash::make(session('password'));
            $user->email_verified_at = now();
            $user->save();
            session()->forget(['user_id', 'password']);
            DB::commit();
            auth()->login($user, true);

            // Check if user has team invitations
            $teamInvitation = $this->checkForTeamInvitation($user->email);
            if ($teamInvitation) {
                return $this->handleTeamInvitationRegistration($user, $teamInvitation);
            }

            return redirect()->route('home');
        } catch (Exception $e) {
            DB::rollBack();
            return back()->withErrors('message', 'Something went wrong. Please try again later.');
        }
    }

    public function invite($token)
    {
        Auth::logout();
        $receiver = Invite::where("token", $token)->first();
        if ($receiver) {
            return Redirect::route("register")->withInput(["email" => $receiver->email, "fname" => $receiver->fname, "lname" => $receiver->lname, "phone" => $receiver->phone])->withSuccess("Please sign in to accept invitation.");
        } else {
            return Redirect::route("register")->withErrors(["main" => "This invitation does not exist or might be revoked."]);
        }
    }

    public function setPassword()
    {
        return view('auth.set_shopify_password');
    }

    public function saveShopifyPassword(Request $request)
    {
        $validator = $request->validate([
            'password' => ['required', 'min:8'],
            'sync_product' => ['required', 'string']
        ]);
        try {

            $user = User::find(auth()->id());
            $user->password = Hash::make($request->password);
            $user->save();
            if ($request->sync_product == "yes") {
                $event = new EventStatusHandler();
                $event->set_data([
                    'organization_id' => auth()->user()->organization_id,
                    'listener_name' => 'FetchShopifyProduct'
                ])->store();
                event(new \App\Events\PostBillingEvent(auth()->user()->organization_id, auth()->id()));
            }
            return redirect()->route('onboarding');
        } catch (\Exception $e) {
            return redirect()->route('dashboard')->withErrors(['main' => 'something went wrong.Please set the password from user setting page.']);
        }
    }

    /**
     * Check if user has pending team invitations
     *
     * @param string $email
     * @return TeamInvite|null
     */
    private function checkForTeamInvitation($email)
    {
        return TeamInvite::withoutGlobalScopes()
            ->where('email', $email)
            ->first();
    }

    /**
     * Handle registration for users with team invitations
     *
     * @param User $user
     * @param TeamInvite $teamInvitation
     * @return \Illuminate\Http\RedirectResponse
     */
    private function handleTeamInvitationRegistration(User $user, TeamInvite $teamInvitation)
    {
        try {
            // Set the organization session for the invited user
            session(['organization_id' => $teamInvitation->organization_id]);

            // The UserLoginAt listener will handle the organization assignment
            // and permission transfer when the Login event is fired

            // Redirect directly to dashboard, skipping onboarding
            return redirect()->route('dashboard')->with('success', 'Welcome! You have been added to the organization.');

        } catch (\Exception $e) {
            Log::error('Error handling team invitation registration: ' . $e->getMessage());
            // Fallback to normal flow if something goes wrong
            return redirect()->route('home');
        }
    }
}
