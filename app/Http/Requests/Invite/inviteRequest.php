<?php

namespace App\Http\Requests\Invite;

use Illuminate\Foundation\Http\FormRequest;

class inviteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'organization_id' => 'prohibited',
            'email' => 'required|email|unique:team_invites,email,' . $this->route('invite') . ',id,organization_id,' . auth()->user()->id,
            'first_name' => 'nullable|max:35',
            'last_name' => 'nullable|max:35',
        ];
    }

}
