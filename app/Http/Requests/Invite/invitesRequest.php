<?php

namespace App\Http\Requests\Invite;

use Illuminate\Foundation\Http\FormRequest;

class invitesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'invitations' => 'required|array|min:1|max:30',
            'invitations.*.email' => 'required|email|distinct|unique:team_invites,email,' . $this->route('invite') . ',id,organization_id,' . auth()->user()->organization_id,
            'invitations.*.first_name' => 'nullable|string|max:35',
            'invitations.*.last_name' => 'nullable|string|max:35',
            'invitations.*.permission_ids' => 'required|array|min:1',
            'invitations.*.permission_ids.*' => 'required|integer|exists:permissions,id',
        ];
    }

    public function messages(){
        return [
            'invitations.*.email.unique' => 'This email is already invited to your organization.',
            'invitations.*.email.required' => 'Email is required for each invitation.',
            'invitations.*.email.email' => 'Please provide a valid email address.',
            'invitations.*.permission_ids.required' => 'At least one permission must be selected.',
            'invitations.*.permission_ids.*.exists' => 'Selected permission is invalid.',
        ];
    }
}
