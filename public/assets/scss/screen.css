@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");
:root {
  --color-primary: #740898;
  --color-secondary: #000;
  --color-tertiary: #94a3b8;
}

:root {
  --color-primary: #740898;
  --color-secondary: #000;
  --color-tertiary: #94a3b8;
}

@font-face {
  font-family: "icomoon";
  src: url("../../assets/fonts/icomoon.eot?yl0gnj");
  src: url("../../assets/fonts/icomoon.eot?yl0gnj#iefix") format("embedded-opentype"), url("../../assets/fonts/icomoon.ttf?yl0gnj") format("truetype"), url("../../assets/fonts/icomoon.woff?yl0gnj") format("woff"), url("../../assets/fonts/icomoon.svg?yl0gnj#icomoon") format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: block;
}
[class^=icon-],
[class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "icomoon" !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.bg-white {
  background-color: #fff;
}

.bg-white-smoke {
  background-color: #F8F8F8 !important;
}

.bg-light300 {
  background-color: #F2F2F2;
}

.bg-black {
  background-color: #252525 !important;
}

.bg-light {
  background-color: #cbd5e1;
}

.bg-light-blue {
  background: #F2F8FF;
}

.bg-primaryLog {
  background: #f8f9ff;
}

.bg-primary {
  background-color: #740898 !important;
}

.bg-Success {
  background-color: #28A745;
}

.bg-Warning {
  background-color: #FFC107;
}

.bg-light-yellow {
  background-color: #FDD44B !important;
}

.bg-blue {
  background-color: #740898;
}

.bg-white-smoke {
  background-color: #F8F8F8;
}

.clr-white {
  color: #fff;
}

.clr-black {
  color: #252525;
}

.clr-light {
  color: #cbd5e1;
}

.clr-success {
  color: #28A745;
}

.clr-blue {
  color: #740898 !important;
}

.clr-grey {
  color: #6C757D !important;
}

.clr-dark-black {
  color: #1A1919 !important;
}

.text-primary {
  color: var(--color-primary) !important;
}

.mt-80 {
  margin-top: 5rem;
}

.mt-60 {
  margin-top: 3.75rem;
}

.mt-40 {
  margin-top: 2.5rem;
}

.mb-40 {
  margin-bottom: 2.5rem;
}

.mt-32 {
  margin-top: 2rem;
}

.mb-24 {
  margin-bottom: 1.5rem;
}

.mt-28 {
  margin-top: 1.75rem;
}

.mt-14 {
  margin-top: 0.875rem;
}

.ml-40 {
  margin-left: 2.5rem;
}

.pb-60 {
  padding-bottom: 3.75rem !important;
}

.pb-100 {
  padding-bottom: 6.25rem !important;
}

.pl-12 {
  padding-left: 12px !important;
}

.fs-12 {
  font-size: 0.75rem;
}

.fs-14 {
  font-size: 0.875rem;
}

.fs-16 {
  font-size: 1rem !important;
}

.fs-18 {
  font-size: 1.125rem;
}

.fs-20 {
  font-size: 1.25rem !important;
}

.fs-24 {
  font-size: 1.5rem !important;
}

.fs-28 {
  font-size: 1.75rem;
}

.fs-32 {
  font-size: 2rem !important;
}

.fw-400 {
  font-weight: 400 !important;
}

.fw-450 {
  font-weight: 450 !important;
}

.fw-500 {
  font-weight: 500 !important;
}

.fw-600 {
  font-weight: 600 !important;
}

.fw-700 {
  font-weight: 700 !important;
}

.w-300 {
  width: 330px !important;
}

.w-19 {
  width: 19%;
}

.w-95 {
  width: 95%;
}

.min-vh-100 {
  min-height: 800px !important;
}

.bbr-2 {
  border-bottom: 2px solid #000;
}

.h-120 {
  height: 7.5rem !important;
}

.h-40 {
  height: 2.5rem !important;
}

.h-36 {
  height: 2.25rem !important;
}

.text-decotation-underline {
  text-decoration: underline !important;
}

.pointer {
  cursor: pointer;
}

.btrr-30 {
  border-top-right-radius: 30px;
}

.bbrr-30 {
  border-bottom-right-radius: 30px;
}

.btn {
  height: 2.25rem;
  text-decoration: none;
  font-size: 0.875rem;
  line-height: 0.8rem;
  border-radius: 0.25rem;
  font-weight: 700;
  padding: 0.75rem 1.5rem;
  min-width: 6.438rem;
  text-transform: capitalize;
}
.btn:disabled {
  background-color: #E6E6E6;
  border: 1px solid #E6E6E6;
  color: #cbd5e1;
}

.btn-primary {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: #fff;
}
.btn-primary:hover, .btn-primary:focus {
  background-color: transparent;
  border-color: var(--color-primary);
  text-decoration: none;
  color: var(--color-primary);
}

.btn-outline-primary {
  color: var(--color-primary);
  border-color: var(--color-primary);
}
.btn-outline-primary:hover {
  text-decoration: none;
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: #fff;
}

.btn-danger {
  background-color: #DC3545;
  border-color: #DC3545;
  color: #fff;
}
.btn-danger:hover {
  text-decoration: none;
  color: #fff;
}

.btn-outline-danger {
  color: #DC3545;
}
.btn-outline-danger:hover {
  text-decoration: none;
  background-color: #DC3545;
  border-color: #DC3545;
  color: #fff;
}

.btn-md {
  height: 2.25rem;
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  border: 0;
  color: var(--color-secondary);
  font-size: 0.875rem;
  line-height: 1rem;
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  text-transform: capitalize;
}
.btn-md:hover {
  color: var(--color-secondary);
  background-color: var(--color-primary);
  text-decoration: none;
  border-color: var(--color-primary);
}
.btn-md:disabled {
  background-color: #E6E6E6;
  border: 1px solid #E6E6E6;
  color: #cbd5e1;
}
.btn-md:focus {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-secondary);
}

.btn-sm {
  height: 1.875rem;
  font-size: 0.875rem;
  line-height: 1rem;
  border-radius: 0.25rem;
  padding: 0.43rem 0.75rem;
  text-decoration: none;
}
.btn-sm:disabled {
  background-color: #E6E6E6;
  border: 1px solid #E6E6E6;
  color: #cbd5e1;
}

.btn-outline-dark {
  background-color: #fff;
  border: 1px solid #252525;
  color: #252525;
}
.btn-outline-dark:hover {
  color: #fff;
  background-color: #252525;
  text-decoration: none;
  border-color: #252525;
}
.btn-outline-dark:disabled {
  background-color: #E6E6E6;
  border: 1px solid #E6E6E6;
  color: #cbd5e1;
}
.btn-outline-dark:focus {
  background-color: #252525;
  border-color: #252525;
  color: #fff;
}

.search img {
  position: absolute;
  top: 0;
  left: 20px;
  bottom: 0;
  margin: auto;
}
.search input {
  height: 2.25rem;
  padding-left: 3.125rem;
}

.form-control {
  font-weight: 500;
}

.form-control:focus {
  color: #252525;
  background-color: #fff;
  border-color: #1A1919;
  outline: 0;
  box-shadow: none;
}

html body {
  font-family: "Roboto", sans-serif;
  font-size: 1rem;
  font-weight: 450;
  line-height: 18px;
}
html body a {
  text-decoration: underline;
  outline: none !important;
}
html body a:hover {
  text-decoration: underline;
}
html body h1,
html body .h1 {
  font-family: "Roboto", sans-serif;
  color: var(--color-secondary);
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1.75rem;
}
html body h2,
html body .h2 {
  font-family: "Roboto", sans-serif;
  font-size: 1.25rem;
  color: var(--color-secondary);
  font-weight: 700;
  line-height: 1.5rem;
}
html body h3,
html body .h3 {
  font-size: 1rem;
  font-weight: 500;
  color: var(--color-secondary);
  line-height: 1.5rem;
}
html body h4,
html body .h4 {
  font-size: 0.875rem;
  color: var(--color-secondary);
  font-weight: 700;
  line-height: 1.5rem;
}
html body h5,
html body .h5 {
  font-size: 0.75rem;
  color: var(--color-secondary);
  font-weight: 500;
  line-height: 1.125rem;
}
html body h6,
html body .h6 {
  font-size: 0.625rem;
  color: var(--color-secondary);
  font-weight: 500;
  line-height: 1rem;
}
html body p {
  font-family: "Roboto", sans-serif;
  color: #252525;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5rem;
}
html body .p1 {
  font-family: "Roboto", sans-serif;
  font-weight: 400;
  color: var(--color-secondary);
  font-size: 0.75rem;
  line-height: 1.125rem;
}

small {
  font-size: 0.75rem;
}

.border-top-design {
  border-top: 2px solid #dee2e6 !important;
}

.circle {
  min-width: 40px;
  max-width: 40px;
  min-height: 40px;
  max-height: 40px;
  border: 2px solid #fff;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50px;
  line-height: 2.1;
}

.circle-sm {
  min-width: 34px;
  max-width: 34px;
  min-height: 34px;
  max-height: 34px;
  border: 2px solid #fff;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50px;
  line-height: 2.1;
}

.table > thead > * > * {
  height: 48px;
  padding: 12px 12px;
  background-color: var(--bs-table-bg);
  border-bottom-width: 1px solid grey;
  box-shadow: none;
  font-size: 0.875rem;
  line-height: 18px;
  color: #252525;
  font-weight: 500;
  vertical-align: middle;
}

.table > tbody > tr > td {
  color: #191D23;
  border-bottom-width: 0px;
  box-shadow: none;
  font-size: 0.875rem;
  line-height: 18px;
  cursor: pointer;
  font-weight: 400;
}

.table thead th {
  background-color: #F8F8F8;
  min-height: 44px;
}
.table thead th:first-child {
  padding-left: 10px;
}
.table thead th:last-child {
  padding-right: 10px;
}
.table tbody tr td {
  border-bottom-width: 0;
  vertical-align: middle;
  height: 44px;
}
.table tbody tr td:first-child {
  padding-left: 10px;
}
.table tbody tr td:last-child {
  padding-right: 10px;
}
.table tbody tr.ui-sortable-handle:hover td {
  cursor: move;
}

.table-image-width {
  max-width: 40px;
  min-width: 40px;
  max-height: 40px;
  min-height: 40px;
  border-radius: 4px;
  object-fit: cover;
}

thead,
tr {
  border-color: inherit;
  border-style: unset !important;
  border-width: 0;
  border-top: 0;
  border-bottom: 1px solid #E7EAEE !important;
}

.table > :not(:last-child) > :last-child > * {
  border-bottom-color: #CACACA !important;
}

.table-card {
  flex: 1 1 auto;
  padding: 0rem 0.8rem;
}

.table > :not(caption) > * > * {
  padding: 0rem 0.4rem;
}

.table > tbody > tr:hover {
  background-color: #F8F8F8 !important;
}
.table > tbody > tr:focus {
  background-color: #F8F8F8 !important;
}

.table-head-border {
  border-radius: 4px 4px 0px 0px !important;
}

.dropdown-toggle::after {
  display: none;
}

.dropdown-menu {
  background: #ffffff;
  box-shadow: 0px 1px 20px rgba(0, 0, 0, 0.07);
  border: 0;
}
.dropdown-menu a {
  padding: 10px 24px;
  display: block;
}
.dropdown-menu a:last-child {
  margin-bottom: 0;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

.formStyle ::placeholder {
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.125rem;
  color: #5B5B5B;
}
.formStyle label {
  font-weight: 500;
  font-size: 14px;
  line-height: 16px;
  color: #1A1919;
}
.formStyle .form-control:disabled,
.formStyle .form-control[readonly] {
  color: #6C757D;
}
.formStyle input {
  border: 1px solid #E6E6E6;
  background-color: #F8F8F8;
  color: #252525;
  line-height: 1.25rem;
  height: 2.25rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 400;
}
.formStyle input:focus {
  outline: 0;
  border: 1px solid #740898;
  color: #252525;
  box-shadow: none;
}
.formStyle ::-webkit-input-placeholder {
  /* Edge */
  font-weight: 500;
}
.formStyle :-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-weight: 500;
}
.formStyle ::placeholder {
  font-weight: 400;
  color: #6C757D;
}
.formStyle select {
  border: 1px solid #E6E6E6;
  color: #252525;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  border-radius: 4px;
  width: 100%;
  height: 2.25rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url("../images/down_black.svg");
  padding: 0.375rem 1.8rem 0.375rem 0.75rem;
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 20px;
}
.formStyle select:focus {
  outline: 0;
  border: 1px solid #740898;
  color: #252525;
  box-shadow: none;
}
.formStyle textarea {
  border: 1px solid #E6E6E6;
  background-color: #F8F8F8;
  font-weight: 400;
  border-radius: 4px;
}
.formStyle textarea:focus {
  outline: 0;
  border: 1px solid #740898;
  box-shadow: none;
}
.formStyle .form-check {
  line-height: 23px;
}
.formStyle .form-check label {
  font-size: 0.875rem;
  color: #252525;
}
.formStyle .form-check:focus {
  outline: 0;
  box-shadow: none;
}
.formStyle .form-check-input:checked {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: #fff;
}
.formStyle .form-check-input:focus {
  border-color: none;
  outline: 0;
  box-shadow: none;
}
.formStyle .form-switch .form-check-input {
  width: 40px;
  height: 18px;
}
.formStyle .form-control-sm {
  height: 30px;
}

.comboTreeInputBox::placeholder {
  color: #252525;
  font-size: 0.875rem;
}

.mdi-chevron-down {
  opacity: 0;
}

.select-small {
  height: 30px;
}

.form-check .form-check-input {
  float: none;
  margin-left: -1.5em;
}

.form-switch .form-check-input {
  margin-top: 1px;
}

.nav-item.active-link {
  border-left: 3px solid #740898;
  font-weight: 600 !important;
}

.active-product-link {
  color: var(--color-primary) !important;
  font-weight: 600 !important;
}

.product-link {
  color: var(--color-primary) !important;
}

.active-link-product {
  color: var(--color-primary) !important;
}

.nav-item .nav-link {
  margin-bottom: 0px;
  display: flex;
  align-items: center;
  height: 2.5rem;
  color: #252525;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.25rem;
  background-color: #fff;
  color: #000;
  padding: 0.5rem 1.3rem;
  background: 0 0;
  border-radius: 0.5rem 0.5rem 0rem 0rem;
}
.nav-item .nav-link:hover {
  text-decoration: none;
  background-color: rgb(248, 249, 250);
  color: var(--color-primary);
}

#pills-tab .nav-item .nav-link {
  margin-bottom: 0px;
  display: flex;
  align-items: center;
  height: 2.5rem;
  color: #252525;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.25rem;
  background-color: #fff;
  color: #000;
  font-weight: 600;
  padding: 0.5rem 1.3rem;
  background: 0 0;
  border-radius: 0.5rem 0.5rem 0rem 0rem;
}
#pills-tab .nav-item .nav-link:hover {
  text-decoration: none;
  background-color: rgb(248, 249, 250);
  color: var(--color-primary);
}

.icon {
  font-size: 1.25rem;
}

.list-group-item.active {
  z-index: 2;
  color: #fff;
  background-color: #740898;
  border-color: #740898;
}

.modal-header {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1rem;
  border-bottom: 1px solid #dee2e6;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}

.modal-footer {
  display: flex;
  flex-wrap: wrap;
  flex-shrink: 0;
  align-items: center;
  justify-content: flex-end;
  padding: 0px;
  border-top: 1px solid #dee2e6;
  border-bottom-right-radius: calc(0.3rem - 1px);
  border-bottom-left-radius: calc(0.3rem - 1px);
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: var(--color-primary) !important;
  font-weight: 600;
  font-size: 0.875rem;
  line-height: 1.25rem;
  background-color: #fff;
  border-bottom: 2px solid var(--color-primary);
}

::-webkit-scrollbar {
  width: 0.4rem;
  height: 9px;
}

::-webkit-scrollbar-track {
  -webkit-box-shadow: none;
}

::-webkit-scrollbar-thumb {
  background-color: #C9C9CF;
  outline: none;
  border-radius: 83px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #C9C9CF;
}

.success p {
  color: #252525;
}

.modal-backdrop.show {
  background: var(--color-secondary);
  opacity: 0.7;
}

#logo_sidebar {
  width: 36px;
  height: 36px;
}

#logo_sidebar_product {
  width: 29px;
  height: 34px;
  margin-left: 3px;
}

.right-side {
  margin-left: 15.438rem;
}

.fa-bars-css {
  display: none;
}

.submenu {
  position: absolute;
  width: 11.8rem;
  background: #F8F8F8;
  z-index: 99;
  top: 0px;
  left: 3.6rem;
  height: 99vh;
}

#submenu_product {
  padding-top: 9.7rem !important;
}

/* sidebar billing time*/
.billing-timer {
  width: 100%;
  bottom: 0px;
}

.billing-custom-css {
  width: 106%;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 16px;
  padding: 14px 10px 14px 10px;
  background: #fff3f4;
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.16);
}
.billing-custom-css_product {
  left: -37px;
  bottom: -166px;
}
.billing-custom-css_main {
  left: 12px;
  bottom: -166px;
}

.billing-custom-css-main-menu {
  width: 90% !important;
}

.progress {
  height: 0.313rem;
  width: 157px;
}

.status {
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 12px;
  line-height: 0.875rem;
  padding: 0.5rem 1.5rem;
  height: 1.875rem;
}
.status-publish {
  background-color: #e7ecff;
  color: #740898;
}
.status-synced {
  background-color: #d7f8de;
  color: #064E3B;
}
.status-draft {
  background-color: #eeeeef;
  color: #6C757D;
}
.status-success {
  background-color: #d7f8de;
  color: #28A745;
}
.status-warning {
  background-color: #fff5d6;
  color: #FFC107;
}
.status-danger {
  background-color: #ffefef;
  color: #DC3545;
}

.notification-status-css {
  padding: 0.3rem 1rem !important;
  height: 1rem !important;
  border-radius: 4px;
}

.check-box-product {
  border: 1px solid #CACACA !important;
  background-color: #f2f2f3 !important;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
  margin-right: -1px;
  width: 95px !important;
  border-right: 0px;
  padding: 7px 8px;
}

.border-left-top-bottom {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.circle-sm.circle-error {
  /* background: #FFEFEF; */
  border: 6px solid #ffefef;
  margin-bottom: 2px;
  background: #dc3545;
  min-width: 17px;
  max-width: 17px;
  min-height: 17px;
  max-height: 17px;
  display: inline-block;
  line-height: 2.1;
}

.circle-sm.circle-success {
  border: 6px solid #d7f8de;
  margin-bottom: 2px;
  background: #28a745;
  min-width: 17px;
  max-width: 17px;
  min-height: 17px;
  max-height: 17px;
  display: inline-block;
  line-height: 2.1;
}

.circle-sm {
  border-radius: 50%;
}

.tox-tinymce {
  width: 100% !important;
  height: 500px !important;
}

.score-label {
  width: 2.375rem;
  height: 0.563rem;
  border-radius: 1rem;
}
.score-label-publish {
  background-color: #740898;
}
.score-label-success {
  background-color: #28A745;
}
.score-label-draft {
  background-color: #6C757D;
}
.score-label-grey {
  background-color: #eeeeef;
}
.score-label-warning {
  background-color: #FFC107;
}
.score-label-danger {
  background-color: #DC3545;
}

.field-score-chart {
  height: 110px;
  width: 110px;
}

.media-score-chart {
  height: 166px;
  width: 166px;
}

.field-score-chart-tab {
  height: 166px;
  width: 166px;
}

.popover {
  max-width: 350px;
  max-height: 2.5rem;
  display: flex;
  align-items: center;
  width: auto;
}

.is-invalid {
  border: 1px solid #DC3545 !important;
}

.notification {
  min-width: 8px;
  max-width: 8px;
  min-height: 8px;
  max-height: 8px;
  border-radius: 50%;
}
.notification-danger {
  background-color: #DC3545;
}
.notification-primary {
  background-color: #740898;
}
.notification-dark {
  background-color: #C9C9CF;
}

.progress-cart-css {
  min-width: 20px;
  max-width: 20px;
  min-height: 20px;
  max-height: 20px;
  border-radius: 4px;
}

:root {
  --color-primary: #740898;
  --color-secondary: #000;
  --color-tertiary: #94a3b8;
}

.icon-delete:before {
  content: "\e900";
}

.icon-add:before {
  content: "\e901";
}

.icon-arrow-left:before {
  content: "\e902";
}

.icon-arrow-right:before {
  content: "\e903";
}

.icon-billing:before {
  content: "\e904";
}

.icon-category:before {
  content: "\e905";
}

.icon-dots:before {
  content: "\e906";
}

.icon-down_black:before {
  content: "\e907";
}

.icon-edit:before {
  content: "\e908";
}

.icon-gallery-export:before {
  content: "\e90a";
}

.icon-help:before {
  content: "\e90b";
}

.icon-invite-vendors:before {
  content: "\e90c";
}

.icon-logout:before {
  content: "\e90d";
}

.icon-notification:before {
  content: "\e90e";
}

.icon-plus .path1:before {
  content: "\e90f";
}

.icon-plus .path2:before {
  content: "\e910";
}

.icon-product:before {
  content: "\e911";
}

.icon-retailer:before {
  content: "\e912";
}

.icon-rotate:before {
  content: "\e913";
}

.icon-setting:before {
  content: "\e914";
}

.icon-teams:before {
  content: "\e915";
}

.icon-vendors:before {
  content: "\e916";
}

.icon-bell-regular:before {
  content: "\e917";
}

:root {
  --color-primary: #740898;
  --color-secondary: #000;
  --color-tertiary: #94a3b8;
}

.maindashboard {
  min-height: 100%;
}

.main-sidebar {
  position: fixed !important;
  bottom: 0;
  float: none;
  left: 0;
  overflow-y: auto !important;
  top: 0;
  overflow-x: hidden;
  z-index: 10;
  width: 15.438rem;
  transition: margin-left 0.3s ease-in-out, width 0.3s ease-in-out;
  height: auto;
}

.brand-link {
  padding: 32px 32px 0 32px;
  display: block;
}

.hamburgericon {
  display: none;
}

.tabs-style-dashboard {
  height: 266px;
  overflow-y: auto;
}

.maindashboard {
  padding: 22px 0;
}

.contentwrapper {
  margin-left: 265px;
  padding: 0 40px 0 40px;
}

.mainheader {
  margin-left: 265px;
  padding: 32px 40px 40px 40px;
}

.contentheader .mainheading {
  margin-bottom: 34px;
}

.content {
  margin-top: 34px;
}

btn-check:focus + .btn,
.btn:focus {
  outline: 0;
  box-shadow: none;
}

.form-check {
  display: block;
  min-height: 24px;
  padding-left: 1.5em;
  margin-bottom: 0px;
}

.form-check-input:focus {
  border-color: #1A1919;
  outline: 0;
  box-shadow: none;
}

.fixedactionbtns {
  border-top: 1px solid #252525;
  background-color: #fff;
  position: fixed;
  bottom: 0;
  right: 0;
  padding: 15px 30px;
  z-index: 6;
  width: calc(100% - 250px);
}

.viewform label {
  color: #252525;
  line-height: 24px;
}

.border-left {
  position: relative;
  margin-top: -50px;
  padding-left: 50px;
}
.border-left h4 {
  margin-bottom: 24px;
}
.border-left:before {
  content: "";
  position: absolute;
  left: 0;
  width: 1px;
  top: 0%;
  height: 100%;
  background: #252525;
}

.dropdown-style {
  min-width: 91px;
  max-width: 92px;
}

.edit-style {
  cursor: pointer;
}

.dropdown-toggle-style {
  height: 16px;
  margin-top: -12px;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1rem;
  clear: both;
  font-weight: 450;
  color: var(--color-secondary);
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}
.dropdown-item:hover {
  background-color: #fff;
  text-decoration: none;
}

.dropdown-menu a {
  padding: 16px;
  display: block;
}

.dropdown-item.active,
.dropdown-item:active {
  color: var(--color-secondary);
  text-decoration: none;
  background-color: #fff;
}

.scroll-icon {
  cursor: pointer;
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  right: 10px;
}

.input-group .btn {
  position: relative;
  width: 45px;
  height: 100%;
  z-index: 2;
}

.search {
  position: absolute;
  font-weight: 700;
  padding: 0.75rem 1.5rem;
  background-color: #252525;
  border-radius: 4px;
  border: 1px solid black;
  height: 100%;
  right: -2px;
  z-index: 100;
}
.search img {
  position: absolute;
  top: 0px;
  left: 15px;
  bottom: 0;
  margin: auto;
  z-index: 20;
}
.search_btn_close {
  position: absolute !important;
  right: 20px;
  top: -2px;
}

.filter-status {
  width: 100px;
}

.filter-status-hover {
  background-color: #fff;
  min-width: 110px;
  max-width: 110px;
}
.filter-status-hover:hover {
  border: 1px solid #740898 !important;
}
.filter-status-hover:focus {
  border: 1px solid #740898 !important;
}

.filter-input {
  min-width: 60px;
  max-width: 100px;
  background-color: #fff !important;
  border: 1px solid #e6e6e6 !important;
}
.filter-input:hover {
  border: 1px solid #740898 !important;
}
.filter-input:focus {
  border: 1px solid #740898 !important;
}
.filter-input::placeholder {
  color: #252525 !important;
  font-size: 0.875rem;
  line-height: 1rem;
  font-weight: 400;
}

.select-css {
  width: 100px !important;
}

.delete-btn-image {
  width: 20px;
  height: 22px;
}

.background-image-css {
  width: 35px;
  height: 35px;
  background-position: center;
  background-size: cover;
  border-radius: 4px;
}

.edit-product-header {
  min-height: 5.5rem;
  min-height: 5.5rem;
  min-width: 5.5rem;
  max-width: 5.5rem;
}

.clone_input_field {
  width: 96.5% !important;
}

.add_field_button {
  border: none;
  background: #fff;
  margin-top: 4px;
  font-size: 0.75rem;
  color: #252525;
  font-weight: 700;
  text-decoration: unset;
}

.remove_field {
  float: right;
  font-size: 24px;
  margin-top: 6px;
  margin-right: 6px;
  font-weight: 400;
  padding: 0px;
  border: none;
  background: none;
  color: red;
}
.remove_field:hover {
  float: right;
  font-size: 24px;
  margin-top: 6px;
  /* margin-right: 5px; */
  font-weight: 400;
  color: red;
}

.input-width-css {
  width: 96.5% !important;
}

.save-input {
  margin-bottom: 6px;
  margin-left: 10px;
}

.card-header {
  padding: 0.5rem 1rem;
  margin-bottom: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.range-slider {
  margin: 0px;
}

.range-slider {
  width: 100%;
}

.range-slider__range {
  width: calc(100% - 73px);
  height: 10px !important;
  border-radius: 12px !important;
  background: #d7dcdf;
  outline: none;
  padding: 0;
  margin: 0;
}

input[type=color] {
  max-width: 35px;
  padding: 5px !important;
}

.range-slider__range::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #2c3e50;
  cursor: pointer;
  transition: background 0.15s ease-in-out;
}

.range-slider__range::-webkit-slider-thumb:hover {
  background: #2c4bff;
}

.range-slider__range:active::-webkit-slider-thumb {
  background: #2c4bff;
}

.range-slider__range::-moz-range-thumb {
  width: 20px;
  height: 20px !important;
  border: 0;
  border-radius: 50%;
  background: #6C757D;
  cursor: pointer;
}

.range-slider__value {
  display: inline-block;
  position: relative;
  width: 60px;
  color: #fff;
  line-height: 20px;
  text-align: center;
  border-radius: 3px;
  background: #2c3e50;
  padding: 5px 10px;
  margin-left: 8px;
}
.range-slider__value::after {
  position: absolute;
  top: 8px;
  left: -7px;
  width: 0;
  height: 0;
  border-top: 7px solid transparent;
  border-right: 7px solid #2c3e50;
  border-bottom: 7px solid transparent;
  content: "";
}

::-moz-range-track {
  background: #d7dcdf;
  border: 0;
}

.folder {
  width: 15rem;
  height: 2.75rem;
  border: 1px solid #adadad;
  padding: 0.75rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
}
.folder > a {
  color: black !important;
  font-weight: 400;
}
.folder:hover a {
  color: #740898 !important;
}
.folder:hover {
  border: 1px solid #740898;
}
.folder:hover * {
  color: #740898;
}

#v-pills-tab-dashboard {
  min-width: 300px;
  max-width: 300px;
}
#v-pills-tab-dashboard .nav-link {
  color: #252525;
  font-weight: 700;
  font-size: 1rem;
  line-height: 1.125rem;
  display: flex;
  align-items: center;
  min-height: 66px;
  padding: 1rem 3rem;
}

#v-pills-tab-dashboard.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff !important;
  font-weight: 700;
  font-size: 1rem;
  background-color: #740898;
  padding: 1rem 3rem;
  border: 1px solid #E6E6E6;
}

.add-vendor {
  width: 96px;
  height: 96px;
}

.add-vendors-btn {
  padding: 0.375rem 0.75rem;
}

.vendor-img {
  width: 160px;
  height: 160px;
  object-fit: contain;
}

.tooltip1 {
  position: relative;
  display: inline-block;
}

.tooltip1 .tooltiptext {
  visibility: hidden;
  width: 140px;
  background-color: #555;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 100%;
  left: 50%;
  margin-left: -75px;
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip1 .tooltiptext::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #555 transparent transparent transparent;
}

.tooltip1:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

.general-info-css {
  padding-left: 0px !important;
}

.card-img {
  min-width: 160px;
  max-width: 160px;
  min-height: 160px;
  max-height: 160px;
  object-fit: cover;
}

.arrow-btn {
  min-width: 50px !important;
  padding-left: 2px !important;
  padding-right: 2px !important;
}

.plans-images {
  min-width: 6.5rem;
  max-width: 6.5rem;
  min-height: 7.5rem;
  max-height: 7.5rem;
}

.plans-text {
  font-size: 2.5rem;
  line-height: 3rem;
  font-weight: 700;
}

.plans-cards-ul {
  list-style-type: none;
}
.plans-cards-ul > li {
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.125rem;
  margin-bottom: 0.25rem;
  display: flex;
}

.plans-cards-width {
  max-width: 23.438rem;
}

.StripeElement {
  box-sizing: border-box;
  height: 38px;
  padding: 10px 12px;
  border-radius: 4px;
  background-color: #f8f8f8;
  border: 1px solid #e5e5e5;
  -webkit-transition: box-shadow 150ms ease;
  transition: box-shadow 150ms ease;
}

.StripeElement--focus {
  background-color: white !important;
  border: 1px solid #000000;
}

.StripeElement--invalid {
  border-color: #fa755a;
}

.StripeElement--webkit-autofill {
  background-color: #fefde5 !important;
}

.store-select {
  border: 1px solid #E6E6E6;
  color: #252525;
  font-size: 0.875rem;
  font-weight: 400;
  border-radius: 4px;
  width: 100%;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url("../images/down_black.svg");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 20px;
}
.store-select:focus {
  outline: 0;
  border: 1px solid #740898;
  color: #252525;
  box-shadow: none;
}

.onBoarding-rightSide {
  background-color: #f4f7ff;
  height: 100vh;
}

.progress-bar-css1 {
  height: 1rem !important;
  width: 100%;
  border-radius: 50px;
}

#loading_btn {
  display: none;
}

.back-btn-css {
  position: absolute;
  color: #000;
  top: 10%;
  left: 17%;
}

.btn-outline-dashed-primary {
  border: 2px dashed #E6E6E6;
  padding: 27px 0px 39px 0px;
}
.btn-outline-dashed-primary:focus {
  border: 2px dashed #740898;
  box-shadow: none;
  color: #740898;
}
.btn-outline-dashed-primary:hover {
  border: 2px dashed #740898;
  box-shadow: none;
  color: #740898;
}

.options1 {
  margin-top: 24px;
  min-width: 244px;
  font-size: 14px;
  line-height: 24px;
  font-weight: 700;
  height: 50px;
  border: 1px solid #6C757D;
  border-radius: 4px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
.options1:hover {
  cursor: pointer;
  border: 1px solid #740898;
  color: #740898;
}

.submit-btn {
  height: 36px;
  margin-top: 40px;
  color: white !important;
  border-radius: 4px;
}

.image-css {
  transform: translate(15%, 50%);
}

/*---------------------------------------- TABS --------------------------------*/
/* Hide all steps by default: */
.tab {
  display: none;
}

.next-Btn {
  background-color: #fff;
  color: #000;
  height: 36px;
  border: none;
  padding: 10px 0px 20px;
  font-size: 17px;
  cursor: pointer;
}
.next-Btn:focus {
  outline: none;
  outline: none;
}
.next-Btn:hover {
  opacity: 0.8;
  color: #740898 !important;
}

.auth-btn {
  width: 23px;
  margin-top: -8px;
  opacity: 0.7;
}

.shopify-img {
  width: 23px;
  height: 23px;
  border-radius: 50px;
  background-color: #fff;
  padding: 2px;
}

.shopify-btn-hover:hover {
  background-color: #5e8e3e !important;
  color: #fff;
}

.ml-118 {
  margin-left: -118px;
}

.auth-screen-width {
  max-width: 500px;
}

.media-image {
  min-width: 25px;
  max-width: 25px;
  margin-top: 6px;
}

.dashboard-title {
  margin-right: -3px;
  height: 104px;
}

.select-css {
  min-width: 139px;
}

.custom-options-css {
  border: 1px solid #e2e2e3;
  border-radius: 8px;
  min-width: 350px;
  max-width: 350px;
  min-height: 104px;
  max-height: 104px;
}
.custom-options-css:hover {
  border: 1px solid rgba(44, 75, 255, 0.4705882353);
  background-color: rgba(242, 242, 243, 0.5294117647);
  cursor: pointer;
}

.large-button-css {
  width: 150px;
}

.box__input {
  margin-top: 50px;
}

.box {
  height: 220px;
  border: 2px dashed #a5a5a5;
  cursor: pointer !important;
  border-radius: 8px;
}

.table-style-css {
  max-width: 83.9vw;
}

.list-group-item {
  position: relative;
  display: block;
  padding-left: 2rem;
  padding-right: 0rem;
  font-weight: 400;
  color: #6C757D;
  text-decoration: none;
  background-color: #fff;
}

.selected-input-css-border {
  border-radius: 0px 4px 4px 0px !important;
}

.search-btn-css {
  position: absolute;
  right: 10px;
  top: -3px;
}

.mapping_footer {
  position: fixed;
  bottom: 0px;
  right: 0px;
  margin: auto;
  height: 100px;
  width: calc(100% - 246px);
  z-index: 10;
  box-shadow: 15px 5px 15px 2px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.9);
}

.margin-start-card {
  margin-left: 26px;
}

.bulk-custom-border {
  border: 1px solid #dee2e6;
  height: 38px;
}

input[type=checkbox]:checked + .bulk-checked {
  border: 1px solid rgba(44, 75, 255, 0.4705882353);
  background: rgba(242, 242, 243, 0.5294117647);
}

.bulk-input-css {
  position: absolute;
  left: 17px;
  top: 1px;
}

#section1 {
  max-height: 226px;
  overflow-y: auto;
}

/*
#section2{
    max-height: 500px;
    overflow-y: auto;
}
*/
.product-name {
  padding: 0px 0px;
}

.dropdown-toggle-attribute::after {
  display: inline-block;
  margin-left: 1.255em;
  vertical-align: 0.2em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
  font-size: 1.4rem;
}

.dropdown-menu-attribute a {
  padding: 0px 10px !important;
  display: block;
  font-weight: 400;
}

.dropdown-menu-attribute {
  min-width: 6rem !important;
}

.scroll-btn {
  display: none;
  position: fixed;
  bottom: 30px;
  right: -10px;
  width: 40px;
  height: 40px;
  border-radius: 50px;
  text-align: center;
  line-height: 40px !important;
  transform: rotate(270deg) translate(-50%, -50%);
  box-shadow: 0px 5px 12px 2px rgba(0, 0, 0, 0.4588235294);
  cursor: pointer;
  opacity: 0.7;
}
.scroll-btn:hover {
  opacity: 1;
  background-color: #740898;
  box-shadow: 0px 5px 12px 2px rgba(0, 0, 0, 0.4588235294);
  color: #fff;
}

.sidebar-notification {
  position: absolute;
  left: 24px;
  top: 6px;
}

.bulkEditCheckbox1 {
  width: 20px !important;
}

.bulk-checkbox {
  margin-top: 5px;
}

.notification-text-width {
  max-width: 70%;
}

.range-slider__range::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #740898;
  cursor: pointer;
  margin-top: -5px;
}

.form-range::-moz-range-thumb {
  background-color: #740898 !important;
}

.variant-input-weight {
  width: 100px !important;
  height: 36px !important;
}

.notification-css {
  list-style-type: none;
}

.review-text {
  padding: 0px 72px;
}

.dashboardCard {
  padding: 1rem 2rem;
}
.dashboardCard_image {
  max-width: 4rem;
  min-width: 4rem;
  min-height: 4rem;
  max-height: 4rem;
}
.dashboardCard:hover {
  border-color: #740898;
}

:root {
  --color-primary: #740898;
  --color-secondary: #000;
  --color-tertiary: #94a3b8;
}

.SumoSelect > .CaptionCont > label > i {
  background-image: url("../images/down_black.svg") !important;
  background-position: center center;
  width: 16px;
  height: 16px;
  display: block;
  position: absolute;
  top: 2px !important;
  left: -2px !important;
  right: 0;
  bottom: 0;
  margin: auto;
  background-repeat: no-repeat;
  opacity: 1 !important;
}

.SumoSelect > .CaptionCont {
  color: #252525 !important;
  font-weight: 400;
  font-size: 0.875rem;
  height: 2.25rem !important;
  line-height: 1rem;
}
.SumoSelect > .CaptionCont:hover {
  border: 1px solid #740898 !important;
}
.SumoSelect > .CaptionCont:focus {
  border: 1px solid #740898 !important;
}

.SumoSelect.open > .optWrapper {
  top: 36px !important;
  display: block;
}

#AttributeSet {
  overflow: hidden;
}

#Category {
  overflow: hidden;
}

.tox-statusbar {
  display: none !important;
}

.tagger + .tagger {
  margin-top: 10px;
}

.custom-border-css {
  border: 1px solid #E6E6E6 !important;
  background: #F8F8F8;
  margin-right: 1px;
  border-radius: 0px;
}

.tox .tox-statusbar {
  align-items: center;
  background-color: #fff;
  border-top: 1px solid #e3e3e3;
  color: rgba(34, 47, 62, 0.7);
  display: flex;
  flex: 0 0 auto;
  font-size: 14px;
  font-weight: 400;
  height: 25px;
  overflow: hidden;
  padding: 2px 8px;
  position: relative;
  display: block !important;
  text-transform: none;
}
.tox .tox-edit-area__iframe {
  background-color: #f8f8f8 !important;
  border: 0;
  box-sizing: border-box;
  flex: 1;
  height: 100%;
  position: absolute;
  width: 100%;
}
.tox .tox-notification--in {
  opacity: 0 !important;
  height: 0px !important;
  width: 0px !important;
  display: none;
}

.tox-statusbar__branding {
  display: none !important;
}

.tox-notification--warning {
  background-color: #fff5cc;
  border-color: #fff0b3;
  height: 0px !important;
  width: 0px !important;
  color: #222f3e;
}

.tox-notifications-container {
  position: absolute;
  left: 1198px !important;
  top: 433px !important;
  max-height: 383px;
}

.active2 {
  border: 2px solid #2c4bff !important;
  opacity: 1;
}

.gj-textbox-md {
  background: #f8f8f8;
  border: 1px solid #e5e5e5;
  display: block;
  font-family: Roboto, sans-serif;
  font-size: 0.875rem;
  line-height: 0;
  padding: 4px 12px;
  width: 100%;
  text-align: left;
  color: #000000;
}

.gj-datepicker-md [role=right-icon] {
  top: 7px;
}

.comboTreeArrowBtn {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url("../images/down_black.svg");
  background-repeat: no-repeat;
  background-position: right 8px center !important;
  background-color: #fff !important;
  background-size: 20px;
  right: 1px !important;
  bottom: 1px !important;
  top: 1px !important;
  border: 0 !important;
}

.comboTreeArrowBtn:hover {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url("../images/down_black.svg") !important;
  background-repeat: no-repeat !important;
  background-color: #fff !important;
  background-position: right 8px center !important;
  background-size: 20px;
  border: 0 !important;
}

.comboTreeArrowBtn:active {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url("../images/down_black.svg") !important;
  background-repeat: no-repeat !important;
  background-color: #fff !important;
  background-position: right 8px center !important;
  background-size: 20px;
  border: 0 !important;
}

.mdi:before,
.mdi-set {
  display: inline-block;
  font: normal normal normal 26px/1 "Material Design Icons" !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.mdi-chevron-down-circle-outline::before {
  content: "\f0b27";
  font-size: 20px !important;
}

.mdi-chevron-right-circle-outline::before {
  content: "\f0b2b";
  font-size: 20px !important;
}

.jsTree {
  width: 100%;
}

.jsTree .itemParent {
  /* div. more down under */
  transition: all 0.3s ease-in;
  padding: 6px 0px;
  display: flex;
}

.jsTree .itemParent:hover {
  background-color: #d1d1d1;
}

.jsTree .itemParent .contenteditable {
  margin: 0px;
  flex-grow: 1;
}

.jsTree .itemParent p {
  margin: 0px 6px;
  max-width: 300px;
  padding: 2px 0px;
}

#color-change-delete {
  fill: red;
}

.jsTree .itemParent .afterIcon {
  display: inline-block;
  flex-shrink: 0;
  width: 19px;
  height: 19px;
  margin: 0px 4px;
  background: url("../../../img/tree-images/delete.svg");
  background-size: 12px 12px;
  background-repeat: no-repeat;
  background-position: center center;
  cursor: pointer;
  transition: opacity 0.3s ease-out;
  background-size: 18px 16px;
  opacity: 1;
}

.jsTree .itemParent:hover .afterIcon {
  opacity: 1;
}

.jsTree .itemParent .afterIconEdit {
  display: inline-block;
  flex-shrink: 0;
  width: 19px;
  height: 19px;
  margin: 0px 4px;
  background: url("../../../img/tree-images/edit.svg");
  background-size: 12px 12px;
  background-repeat: no-repeat;
  background-position: center center;
  cursor: pointer;
  transition: opacity 0.3s ease-out;
  background-size: 18px 25px;
  opacity: 1;
}

.jsTree .childGroup {
  /* ul */
  padding: 0px 0px 0px 12px;
  margin: 0;
}

.jsTree .item {
  /* li */
  list-style: none;
  padding: 0;
  margin: 0;
  transition: all 0.3s ease-in;
}

.jsTree .itemParent .preIcon {
  display: inline-block;
  flex-shrink: 0;
  width: 19px;
  height: 19px;
  margin: 0px 4px;
  background-size: 14px 14px !important;
  background-repeat: no-repeat !important;
  background-position: center center !important;
}

.jsTree .itemParent .preIcon.arrowDown {
  cursor: pointer;
  background: url("../../../img/tree-images/arrowdown-black.svg");
  transition: transform 0.3s ease-out;
  margin-top: 4px;
}

.jsTree .itemParent .preIcon.arrowDown.arrowRotate {
  transform: rotate(-90deg);
}

.jsTreeContextMenu {
  width: max-content;
  display: none;
  position: fixed;
  border-radius: 1px;
  overflow: hidden;
  background: white;
  border: 1px solid #106fab;
  box-sizing: border-box;
}

.jsTreeContextMenu p {
  margin: 0;
  padding: 4px 8px;
  transition: all 0.3s ease-in;
  background: white;
}

.jsTreeContextMenu p:hover {
  background: #eee;
}

.firstTree {
  padding: 0px 0px 0px 18px;
}

.selectize-input {
  height: 36px !important;
  border: 1px solid #E6E6E6 !important;
  padding: 0px 12px !important;
  font-size: 0.875rem !important;
  color: #252525;
  font-weight: 400;
  line-height: 37px !important;
  background: #F8F8F8 !important;
}

.selectize-control.single .selectize-input:after {
  content: " ";
  display: block;
  position: absolute;
  top: 50%;
  right: 12px !important;
  margin-top: -3px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 6px 6px 0 6px !important;
  border-color: #252525 transparent transparent transparent !important;
}

.pagination-container {
  display: flex;
  padding-left: 0;
}
.pagination-container button {
  position: relative;
  color: #0d6efd;
  text-decoration: none;
  background-color: #fff;
  border: 1px solid #dee2e6;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  font-family: "Roboto", sans-serif;
  padding: 0.375rem 0.75rem;
  display: block;
}
.pagination-container button:disabled {
  color: #6c757d;
  pointer-events: none;
  background-color: #fff;
  border-color: #dee2e6;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
  border: 1px solid #dee2e6;
}
.pagination-container button:hover {
  z-index: 2;
  color: #0a58ca;
  background-color: #e9ecef;
  border-color: #dee2e6;
}
.pagination-container span.numbered-page {
  position: relative;
  color: #0d6efd;
  text-decoration: none;
  background-color: #fff;
  border: 1px solid #dee2e6;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  font-family: "Roboto", sans-serif;
  padding: 0.375rem 0.75rem;
  display: block;
  cursor: pointer;
}
.pagination-container span.numbered-page:hover {
  z-index: 2;
  color: #0a58ca;
  background-color: #e9ecef;
  border-color: #dee2e6;
}
.pagination-container span.numbered-page.active {
  z-index: 3;
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.list-group .list-group-item[aria-disabled=true] {
  cursor: not-allowed;
  background: #e6e6e6;
  color: #6c757d;
  border: 1px solid #e6e6e6;
}
.list-group .list-group-item[aria-disabled=true]:active {
  pointer-events: none;
}
.list-group .list-group-item[aria-disabled=true]:focus {
  pointer-events: none;
}

.update-store-btn.disabled {
  cursor: not-allowed;
}
.update-store-btn.disabled #Update-btn[aria-disabled=true] {
  background-color: #e6e6e6;
  border: 1px solid #e6e6e6;
  pointer-events: none;
  color: #6c757d;
}

.import_tabs {
  border-bottom: 1px solid #dee2e6 !important;
}
.import_tabs .tab-list {
  display: flex;
  list-style: none;
  font-size: 18px;
  padding: 0;
  height: 100%;
  margin: 0;
}

.tabs {
  width: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 0.5rem 1.3rem;
  font-weight: 400;
  font-size: 0.875rem;
  cursor: pointer;
}

.tabs.active-tabs {
  color: #2c4bff;
  font-weight: 600;
}

.active-tabs::before {
  content: "";
  display: block;
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% + 2px);
  height: 4px;
  background: #2c4bff;
}

.content {
  display: none;
  padding: 10px;
}

.active-content {
  display: flex;
}

.succesfully-mapped {
  border: 1px solid #ddd;
  border-left: 8px solid #28a745;
}

span.treeselect-input__clear {
  position: absolute;
  bottom: -35px;
  display: none;
}
span.treeselect-input__clear svg {
  stroke: #c5c7cb;
  width: 17px;
  min-width: 17px;
  height: 20px;
  fill: #000 !important;
}

.treeselect-input__arrow svg {
  stroke: #7f7f7f;
  width: 25px;
  min-width: 25px;
  height: 25px;
  stroke-width: 0;
  fill: #000 !important;
}

.selected-item-container {
  display: block;
  margin: 10px 0 20px;
}
.selected-item-container .treeselect-input__tags-element {
  padding: 5px;
  position: relative;
  background: #e3e3e3;
  border: 1px solid #e3e3e3;
  font-size: 12px;
  color: #252525;
  font-weight: 400;
  align-items: center;
  width: auto;
  display: inline-flex;
  margin-right: 5px;
  margin-bottom: 5px;
  border-radius: 10px;
}
.selected-item-container .treeselect-input__tags-element:hover {
  background: #e3e3e3;
  border: 1px solid #2c4bff;
}

.SumoSelect select.disabled + .CaptionCont {
  background: #e6e6e6;
  color: #6c757d;
  border: 1px solid #e6e6e6;
  cursor: not-allowed;
}
.SumoSelect select.disabled + .CaptionCont span {
  cursor: not-allowed;
  color: #6c757d;
}

.accordion-button {
  padding: 0.2rem 1.25rem;
}
.accordion-button:after {
  flex-shrink: 0;
  width: 1.25rem;
  height: 1.25rem;
  margin-left: auto;
  content: "+";
  background-image: none;
  background-repeat: no-repeat;
  background-size: 1.25rem;
  transition: transform 0.2s ease-in-out;
}

.accordion-button:not(.collapsed):after {
  background-image: none;
  transform: rotate(0deg);
  content: "-";
}

.accordion-body .table tbody tr:hover {
  background-color: transparent !important;
}
.accordion-body .table tbody tr td .inventory-quantity {
  height: 1.5rem;
}

.bulkedit {
  font-family: "Roboto", sans-serif;
  position: relative;
  margin-right: auto;
  margin-left: auto;
}
.bulkedit .bulkedit-content {
  height: calc(100vh - 56px);
  overflow: auto;
}
.bulkedit .tox-tinymce {
  height: 230px !important;
  width: 200% !important;
}
.bulkedit .apimio-react-select__menu {
  z-index: 99999;
}
.bulkedit .apimio-react-select__value-container {
  height: 36px;
  padding: 2px 8px;
  align-items: flex-start;
  overflow: auto;
}
.bulkedit .apimio-react-select__control {
  height: 36px;
  padding: 0px 8px;
  align-items: flex-start;
}
.bulkedit table {
  width: 100%;
  padding: 1em;
  margin: 20px 0;
  border-collapse: collapse;
  box-shadow: none;
}
.bulkedit table thead {
  font-size: 16px;
  border: none;
}
.bulkedit table thead tr th {
  text-align: left;
  padding: 8px;
  color: #252525;
  border-bottom: 0px solid #e6e6e6;
  background-color: #fff;
}
.bulkedit table thead tr th:first-child {
  position: sticky;
  left: 0px;
  color: #252525;
  z-index: 999;
  background-color: #fff;
}
.bulkedit table thead tr th:nth-child(2) {
  position: sticky;
  left: 60px;
  color: #252525;
  z-index: 999;
  background-color: #fff;
}
.bulkedit table thead tr th:nth-child(4) {
  position: sticky;
  left: 120px;
  color: #252525;
  z-index: 999;
  background-color: #fff;
}
.bulkedit table tr {
  height: 50px;
  border: none;
  border-bottom: 0px solid #e6e6e6 !important;
  background: #fff;
}
.bulkedit table tr td {
  text-align: left;
  padding: 0px 8px;
  border-bottom: 0px solid #e6e6e6;
}
@media (max-width: 768px) {
  .bulkedit table tr td {
    padding: 0px 3px;
  }
}
.bulkedit table tr td:first-child {
  position: sticky;
  left: 0px;
  background-color: #fff;
  color: #252525;
  z-index: 999;
  background-color: #fff;
}
.bulkedit table tr td:nth-child(2) {
  position: sticky;
  left: 60px;
  color: #252525;
  z-index: 999;
  background-color: #fff;
}
.bulkedit table tr td:nth-child(4) {
  position: sticky;
  left: 120px;
  color: #252525;
  z-index: 999;
  background-color: #fff;
}
.bulkedit table tr td .inputgroup input {
  border: 1px solid #e6e6e6;
  background-color: #f2f2f2;
  color: #252525;
  line-height: 1.25rem;
  height: 2.25rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 400;
}
.bulkedit table tr td input[type=text],
.bulkedit table tr td input[type=number] {
  border: 1px solid #e6e6e6;
  background-color: #f2f2f2;
  color: #252525;
  line-height: 1.25rem;
  height: 2.25rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 400;
}
.bulkedit table tr td .react-tagsinput-input {
  width: 100px;
}
.bulkedit table tr td .css-3w2yfm-ValueContainer {
  max-height: 48px;
  overflow: auto;
}
.bulkedit table tr td .values-container .inline-block {
  max-height: 48px;
  overflow: auto;
}
.bulkedit table tr td .inputgroup {
  display: flex;
  justify-content: center;
}
.bulkedit table tr td textarea {
  border: 1px solid #e6e6e6;
  background-color: #f8f8f8;
  color: #252525;
  line-height: 1.25rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 400;
}
.bulkedit table tr td select {
  border: 1px solid #e6e6e6;
  background-color: #f8f8f8;
  color: #252525;
  line-height: 1.25rem;
  height: 2.25rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 400;
}
.bulkedit table tr.active {
  border: 1px solid #e6e6e6 !important;
}
.bulkedit table tr.active td {
  border-bottom: 0px !important;
}
.bulkedit table tr.variant-row td:first-child {
  position: relative;
  left: 0px;
}
.bulkedit table tr.variant-row td:nth-child(2) {
  position: relative;
  left: 0;
}
.bulkedit table tr.variant-row td {
  left: 0px;
  padding: 0px;
}
.bulkedit table tr.variant-row td .variant-item {
  border: 1px solid #e6e6e6;
}
.bulkedit table tr.variant-row td input[type=text],
.bulkedit table tr.variant-row td input[type=number] {
  background-color: #f8f8f8;
  border: 1px solid #e6e6e6;
  background-clip: padding-box;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  font-weight: 500;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
}
.bulkedit table tr.variant-row td input[type=text].weight-input,
.bulkedit table tr.variant-row td input[type=number].weight-input {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
.bulkedit table tr.variant-row tr:nth-child(odd) {
  border: none;
}
.bulkedit table tr.variant-row tr:nth-child(odd) td {
  text-align: left;
  padding: 8px;
  border-bottom: 0px solid #e6e6e6;
}
.bulkedit table tr.variant-row tr:nth-child(odd) td input[type=text],
.bulkedit table tr.variant-row tr:nth-child(odd) td input[type=number] {
  background-color: #f8f8f8;
  border: 1px solid #e6e6e6;
  color: #252525;
  line-height: 1.25rem;
  height: 2.25rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 400;
}
.bulkedit table tr.variant-row tr:nth-child(odd) td .css-3w2yfm-ValueContainer {
  max-height: 48px;
  overflow: auto;
}
.bulkedit table tr.variant-row tr:nth-child(odd) td .values-container .inline-block {
  max-height: 48px;
  overflow: auto;
}
.bulkedit table tr.variant-row tr:nth-child(odd) td .inputgroup {
  display: flex;
  justify-content: center;
}
.bulkedit table tr.variant-row tr:nth-child(odd) td textarea {
  border: 1px solid #e6e6e6;
  background-color: #f8f8f8;
  color: #252525;
  line-height: 1.25rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 400;
}
.bulkedit table tr.variant-row tr:nth-child(odd) td select {
  border: 1px solid #e6e6e6;
  background-color: #f8f8f8;
  color: #252525;
  line-height: 1.25rem;
  height: 2.25rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 400;
}

.variant-table table thead {
  border-bottom: 0px solid #ccc;
}
.variant-table table thead th {
  border-bottom: 0px solid #ccc;
}

.bulkedit table .apimio-react-select__multi-value {
  background-color: #dce4f9;
}
.bulkedit table .apimio-react-select__indicators .apimio-react-select__indicator {
  padding: 0px 8px;
}
.bulkedit table .apimio-react-select__indicators .apimio-react-select__clear-indicator {
  display: none;
}
.bulkedit table .apimio-react-select__indicators .apimio-react-select__indicator-separator {
  display: none;
}
.bulkedit table tr.variant-row table {
  margin-top: 0;
}
.bulkedit table tr.variant-row table thead {
  border-bottom: 0px solid #ccc !important;
}
.bulkedit table tr.variant-row table thead th {
  background: #fff;
}
.bulkedit table tr.variant-row table thead th:first-child {
  position: relative;
  left: 0px;
  color: #252525;
  z-index: 999;
  background-color: #fff;
}
.bulkedit table tr.variant-row table thead th:nth-child(2) {
  position: relative;
  left: 0px;
  color: #252525;
  z-index: 999;
  background-color: #fff;
}
.bulkedit table tr.variant-row table thead th:nth-child(3) {
  position: relative;
  left: 0px;
  color: #252525;
  z-index: 999;
  background-color: #fff;
}
.bulkedit table tr.variant-row table thead th:nth-child(4) {
  position: relative;
  left: 0px;
  color: #252525;
  z-index: 999;
  background-color: #fff;
}
.bulkedit table tr.variant-row table tbody tr:nth-child(odd) {
  border: none;
  background-color: #fff;
}
.bulkedit table tr.variant-row table tbody tr:nth-child(odd) td {
  text-align: left;
  padding: 0 8px;
  border-bottom: 0px solid #ccc;
}
.bulkedit table tr.variant-row table tbody tr:nth-child(odd) td:first-child {
  position: relative;
  left: 0px;
  background: #fff;
  color: #6c757d;
  z-index: 999;
}
.bulkedit table tr.variant-row table tbody tr:nth-child(odd) td:nth-child(2) {
  background-color: #fff;
  color: #6c757d;
  position: relative;
  left: 0px;
  z-index: 999;
}
.bulkedit table tr.variant-row table tbody tr:nth-child(odd) td:nth-child(3) {
  background-color: #fff;
  color: #6c757d;
  position: relative;
  left: 0px;
  z-index: 999;
}
.bulkedit table tr.variant-row table tbody tr:nth-child(odd) td:nth-child(4) {
  background-color: #fff;
  color: #6c757d;
  position: relative;
  left: 0px;
  z-index: 999;
}
.bulkedit table tr.variant-row table tbody tr:nth-child(odd) td input[type=text],
.bulkedit table tr.variant-row table tbody tr:nth-child(odd) td input[type=number] {
  background-color: #f8f8f8;
  border: 1px solid #e6e6e6;
  color: #252525;
  line-height: 1.25rem;
  height: 2.25rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 400;
}
.bulkedit table tr.variant-row table tbody tr:nth-child(even) {
  border: none;
  background-color: #fff;
}
.bulkedit table tr.variant-row table tbody tr:nth-child(even) td {
  text-align: left;
  padding: 8px;
  border-bottom: 0px solid #ccc;
}
.bulkedit table tr.variant-row table tbody tr:nth-child(even) td:first-child {
  position: relative;
  left: 0px;
  background: #fff;
  color: #222;
  z-index: 999;
}
.bulkedit table tr.variant-row table tbody tr:nth-child(even) td:nth-child(2) {
  background-color: #fff;
  color: #222;
  position: relative;
  left: 0px;
  z-index: 999;
}
.bulkedit table tr.variant-row table tbody tr:nth-child(even) td:nth-child(4) {
  background-color: #fff;
  color: #222;
  position: relative;
  left: 0px;
  z-index: 999;
}
.bulkedit table tr.variant-row table tbody tr:nth-child(even) td input[type=text],
.bulkedit table tr.variant-row table tbody tr:nth-child(even) td input[type=number] {
  background-color: #f8f8f8;
  border: 1px solid #e6e6e6;
  color: #222;
  line-height: 1.25rem;
  height: 2.25rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 400;
}

button {
  outline: 0 !important;
}

.loader {
  z-index: 9999;
}

.confirmation-modal {
  z-index: 9999;
}

select optgroup {
  background: rgba(221, 221, 221, 0.9333333333);
}
select option {
  background: #fff;
}

.multiplevalues {
  position: relative;
}
.multiplevalues .value-inner {
  display: none;
  position: absolute;
  top: 40px;
  left: 0;
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #ccc;
  background: #fff;
  z-index: 999;
}
.multiplevalues .value-inner input {
  margin-bottom: 10px;
}
.multiplevalues .value-inner .inputgroup {
  margin-bottom: 10px;
}

.multiplevalues.active .value-inner {
  display: block;
}

input[readonly] {
  color: #6c757d;
  background-color: #e9ecef;
}
input[readonly]:focus {
  border: none;
}

.tox .tox-notification--warn,
.tox .tox-notification--warning {
  display: none;
}

.product-header.fixed-header {
  position: fixed;
  z-index: 5;
  background: white;
  top: 0;
  right: 0;
  padding: 0 20px;
  background-color: #fff;
  z-index: 999;
}

.tooltip-custom .tooltip-content {
  display: none;
}
.tooltip-custom:hover .tooltip-content {
  display: block;
}

@keyframes fadeOutLeft {
  0% {
    width: 100%;
    opacity: 1;
  }
  50% {
    width: 50%;
    opacity: 0.5;
  }
  100% {
    width: 0%;
    opacity: 0;
  }
}
.mapping-item-isremoving {
  animation-duration: 0.3s;
  animation-fill-mode: both;
  animation-name: fadeOutLeft;
  z-index: 99;
  background-color: rgba(255, 5, 5, 0.2);
}

.merge-advance .dropdown-menu {
  max-height: 300px;
  overflow: auto;
  border: 1px solid #ccc;
  border-top: none;
  transform: none !important;
  top: 36px !important;
}

.close_row {
  transition: all 0.3s ease-in;
}

.inventory .nav-tabs .nav-link.active {
  background-color: #2c4bff;
  color: #fff;
}
.inventory .nav-tabs .nav-link {
  color: #000;
}
.inventory .store-variants {
  border: 1px solid #ced4da;
  border-radius: 4px;
}

.tagger .tagger {
  display: none;
}

.SumoUnder {
  opacity: 0;
  position: absolute;
  z-index: -1;
}

.react-tagsinput {
  height: 36px;
  overflow-y: auto !important;
}

.react-tagsinput-remove {
  text-decoration: none;
}

.react-tagsinput-tag {
  background-color: #dce4f9 !important;
  border-radius: 2px;
  border: 1px solid #dce4f9 !important;
  color: #222 !important;
  display: inline-block;
  font-family: sans-serif;
  font-size: 13px;
  font-weight: 400;
  margin-bottom: 5px;
  margin-right: 5px;
  padding: 5px;
}

.background-image-css {
  cursor: pointer; /* Change cursor to pointer to indicate it's clickable */
}

.overlay {
  display: none; /* Hidden by default */
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6); /* Semi-transparent background */
  justify-content: center;
  align-items: center;
  z-index: 1000; /* Ensure it's above other content */
  padding-top: 2%;
  overflow: hidden;
}

.overlay-content {
  max-width: 60%;
  max-height: 90vh;
  position: relative;
  margin: 0 auto;
  border-radius: 10px;
}
.overlay-content img {
  width: 100%;
  max-width: 100%;
  max-height: 90vh;
  object-fit: cover;
}
.overlay-content .close-btn {
  z-index: 999;
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(0, 0, 0, 0.6);
  font-size: 30px;
  cursor: pointer;
  border: 1px solid rgba(74, 78, 77, 0.1);
  border-radius: 50%;
  padding: 5px;
  font-weight: 400;
  color: rgb(239, 240, 245);
}

.custom-list-grid {
  list-style: none;
  padding-left: 0;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px; /* Adjust the gap as needed */
}

.custom-list-grid li {
  margin-bottom: 10px; /* Adjust as needed */
}

.dropdown-menu {
  background: #ffffff;
  box-shadow: 0px 1px 20px rgba(0, 0, 0, 0.07);
  border: 0;
  transform: none !important;
  inset: unset !important;
}

.mapping-item {
  transition: all 0.3s ease-out;
  left: 0%;
}

.delete-row {
  left: 100%;
  transition: all 0.3s ease-out;
}

.loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #09f;
  animation: spin 1s ease infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.list-group.editing {
  overflow: hidden !important;
}

input[name=temp_name]:disabled {
  cursor: not-allowed;
  background: #c7c7c7;
  color: #7a7a7a;
}

.bottom-fullimpo {
  bottom: 100% !important;
}

:root {
  --color-primary: #740898;
  --color-secondary: #000;
  --color-tertiary: #94a3b8;
}

/*Gallery*/
/* Gallery Section  */
/*.folder-title {*/
/*    display: none !important;*/
/*}*/
/*.page-title {*/
/*    display: none !important;*/
/*}*/
.collection {
  display: grid;
  grid-auto-columns: max-content;
  grid-auto-flow: dense;
  /*grid-auto-rows: minmax(18px, auto);*/
  grid-gap: 15px;
  gap: 5px;
  grid-template-columns: repeat(6, 1fr);
  /*height: 103px;*/
  /*overflow: hidden;*/
  /*width: 615px;*/
}

.share-collection {
  display: grid;
  grid-auto-columns: max-content;
  grid-auto-flow: dense;
  /*grid-auto-rows: minmax(18px, auto);*/
  grid-gap: 15px;
  gap: 5px;
  grid-template-columns: repeat(7, 1fr);
  /*height: 103px;*/
  /*overflow: hidden;*/
  /*width: 615px;*/
}

.progress-bar-properties {
  height: 8px;
  border-radius: 50px;
  margin-bottom: 5px;
}

.card-collection {
  display: grid;
  grid-auto-columns: max-content;
  grid-template-columns: repeat(6, 1fr);
  grid-gap: 15px;
}

.card-toggle:hover {
  box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px !important;
  transition: transform 0.8s ease-out;
  transform: scale(1.03);
  /*transform: translateY(-5px);*/
}

.hide_title {
  display: none;
}

.folder_head {
  font-family: "Roboto", sans-serif;
  color: black !important;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 2rem;
  text-decoration: none;
}

.folder_head:hover {
  text-decoration: none;
}

.path-head {
  font-size: 24px;
  line-height: 30px;
  color: #A5A5A5;
  text-decoration: none;
}

.modal-aside,
.modal.right .modal-aside {
  width: 346px;
  position: fixed;
  height: 100vh;
  right: 0;
  top: 0;
  -webkit-transform: translate3d(0%, 0, 0);
  -ms-transform: translate3d(0%, 0, 0);
  -o-transform: translate3d(0%, 0, 0);
  transform: translate3d(0%, 0, 0);
}

.modal_content,
.modal.right .modal_content {
  height: 100%;
  overflow-y: auto;
}

.modal_body,
.modal.right .modal_body {
  padding: 15px 15px 80px;
}

/*Right*/
.modal.right.fade .modal-aside {
  /*right: -320px;*/
  -webkit-transition: opacity 0.3s linear, right 0.3s ease-out;
  -moz-transition: opacity 0.3s linear, right 0.3s ease-out;
  -o-transition: opacity 0.3s linear, right 0.3s ease-out;
  transition: opacity 0.3s linear, right 0.3s ease-out;
}

.modal.right.fade.in .modal-aside {
  right: 0;
}

/* ----- MODAL STYLE ----- */
.modal_content {
  border-radius: 0;
  border: none;
}

.modal_header {
  border-bottom-color: #EEEEEE;
  background-color: #FAFAFA;
}

.folder:hover {
  border-color: #2C4BFF !important;
}

.folder:hover .folder-icon {
  color: #2C4BFF !important;
}

.folder:hover .folder_name {
  color: #2C4BFF !important;
}

.link-card {
  border-radius: 7px;
  box-shadow: rgba(9, 30, 66, 0.25) 0px 4px 8px -2px, rgba(9, 30, 66, 0.08) 0px 0px 0px 1px;
}

.link-card:hover {
  /*moz-transition: all .4s ease-in-out;*/
  /*-o-transition: all .4s ease-in-out;*/
  /*-webkit-transition: all .4s ease-in-out;*/
  /*transition: all .4s ease-in-out;*/
  background: linear-gradient(0deg, rgb(44, 75, 255) 0%, rgb(2, 126, 251) 100%);
}

.link-card:hover .link-card-text {
  color: white;
}

.unlink_btn {
  background-color: white !important;
}

/*.link-card:hover  .unlink_btn {*/
/*    background-color: white; !important;*/
/*}*/
.custom-margin {
  margin-bottom: -7px;
}

.asset_description {
  width: 52rem !important;
}

.score-label1 {
  width: 14px !important;
  height: 14px !important;
}

/* Gallery Section */
@media (max-width: 821px) {
  .asset_description {
    width: 30rem !important;
    margin-bottom: 0 !important;
  }
  .asset_header {
    flex-direction: column;
  }
  .btn_section {
    margin-bottom: 8px !important;
  }
  .collection {
    grid-template-columns: repeat(3, 1fr);
    gap: 0;
  }
  .score-label1 {
    width: 10px !important;
    height: 10px !important;
  }
  .image-score {
    flex-direction: column;
    width: 80%;
  }
  .image-score-good {
    /*justify-content: space-evenly;*/
  }
  .card-collection {
    grid-template-columns: repeat(4, 1fr) !important;
  }
  .card-img-top {
    height: 105px !important;
  }
  .card-body {
    padding: 8px 8px 1px 8px !important;
  }
}
.overflow-hidden1 {
  overflow: hidden;
}

.drop-in {
  animation: drop-in 1.6s ease 200ms backwards;
}

@keyframes drop-in {
  from {
    opacity: 0;
    transform: translateY(-100px);
  }
  to {
    opacity: 1;
    transform: translate(0px);
  }
}
/* Loader css */
/* Absolute Center Spinner */
.loading {
  position: fixed;
  z-index: 999;
  height: 2em;
  width: 2em;
  overflow: show;
  margin: auto;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

/* Transparent Overlay */
.loading:before {
  content: "";
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(rgba(20, 20, 20, 0.8), rgba(0, 0, 0, 0.8));
  background: -webkit-radial-gradient(rgba(20, 20, 20, 0.8), rgba(0, 0, 0, 0.8));
}

/* :not(:required) hides these rules from IE9 and below */
.loading:not(:required) {
  /* hide "loading..." text */
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

.loading:not(:required):after {
  content: "";
  display: block;
  font-size: 10px;
  width: 1em;
  height: 1em;
  margin-top: -0.5em;
  -webkit-animation: spinner 150ms infinite linear;
  -moz-animation: spinner 150ms infinite linear;
  -ms-animation: spinner 150ms infinite linear;
  -o-animation: spinner 150ms infinite linear;
  animation: spinner 150ms infinite linear;
  border-radius: 0.5em;
  -webkit-box-shadow: rgba(255, 255, 255, 0.75) 1.5em 0 0 0, rgba(255, 255, 255, 0.75) 1.1em 1.1em 0 0, rgba(255, 255, 255, 0.75) 0 1.5em 0 0, rgba(255, 255, 255, 0.75) -1.1em 1.1em 0 0, rgba(255, 255, 255, 0.75) -1.5em 0 0 0, rgba(255, 255, 255, 0.75) -1.1em -1.1em 0 0, rgba(255, 255, 255, 0.75) 0 -1.5em 0 0, rgba(255, 255, 255, 0.75) 1.1em -1.1em 0 0;
  box-shadow: rgba(255, 255, 255, 0.75) 1.5em 0 0 0, rgba(255, 255, 255, 0.75) 1.1em 1.1em 0 0, rgba(255, 255, 255, 0.75) 0 1.5em 0 0, rgba(255, 255, 255, 0.75) -1.1em 1.1em 0 0, rgba(255, 255, 255, 0.75) -1.5em 0 0 0, rgba(255, 255, 255, 0.75) -1.1em -1.1em 0 0, rgba(255, 255, 255, 0.75) 0 -1.5em 0 0, rgba(255, 255, 255, 0.75) 1.1em -1.1em 0 0;
}

/* Animation */
@-webkit-keyframes spinner {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-moz-keyframes spinner {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-moz-keyframes spinner {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes spinner {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
:root {
  --color-primary: #740898;
  --color-secondary: #000;
  --color-tertiary: #94a3b8;
}

@media only screen and (max-width:767px) {
  html {
    font-size: 0.875rem;
  }
  .contentwrapper {
    padding: 0 15px 0 15px;
  }
  .mainheader {
    padding: 32px 15px 40px 15px;
  }
  .withbutton .btn {
    margin: 0 !important;
    width: 100%;
    margin-top: 16px !important;
    display: block;
  }
}
@media (max-width: 1418px) {
  #importmapping label {
    font-size: 12px;
  }
  .score-text {
    margin-right: 1rem !important;
  }
  .score-label {
    margin-right: 0.5rem !important;
  }
}
@media (max-width: 1356px) {
  .score-text {
    margin-right: 0.4rem !important;
  }
}
@media only screen and (max-width: 575px) {
  .main-sidebar {
    width: 51px;
    border-right: 1px solid #E6E6E6;
    overflow-y: hidden !important;
  }
  .mobile-menu {
    width: 170px;
    border-right: 1px solid #E6E6E6;
    background: #fff;
    overflow-y: hidden !important;
  }
  .right-side {
    margin-left: 51px;
  }
  .dashboard-title {
    margin-right: -3px;
    height: 190px !important;
  }
}
@media only screen and (min-width:576px) and (max-width:767px) {
  .mainsidebar {
    width: 170px;
  }
  .right-side {
    margin-left: 170px;
  }
  .main-sidebar {
    width: 170px;
  }
  .folder {
    width: 11.5rem;
  }
}
@media only screen and (max-width:991px) {
  .main-sidebar {
    width: 51px;
    border-right: 1px solid #E6E6E6;
    overflow-y: hidden !important;
  }
  .mobile-menu {
    width: 170px;
    border-right: 1px solid #E6E6E6;
    background: #fff;
    overflow-y: hidden !important;
  }
  .right-side {
    margin-left: 51px;
  }
  html {
    font-size: 0.875rem;
  }
  .billing-custom-css {
    width: 100%;
    padding: 10px 5px;
    background: #fff3f4;
    box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.16);
  }
  .billing-custom-css_product {
    left: -41px;
    bottom: -166px;
  }
  .billing-custom-css_main {
    left: 12px;
    bottom: -166px;
  }
  .circle {
    width: 2.5rem;
    height: 2.5rem;
    border: 2px solid #fff;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50px;
    line-height: 2.1;
  }
  .submenu {
    width: 8.8rem;
  }
  .options1 {
    margin-top: 24px;
    min-width: 170px;
    font-size: 14px;
    line-height: 24px;
    font-weight: 700;
    height: 50px;
    border: 1px solid #252525;
    border-radius: 4px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
  .mapping_footer {
    width: calc(100% - 54px);
  }
  .margin-start-card {
    margin-left: 0px;
  }
  .variant-input-weight {
    width: 100px !important;
  }
}
@media only screen and (min-width:768px) and (max-width:991px) {
  html {
    font-size: 16px;
  }
  .folder {
    width: 12rem;
  }
  .card-width-lg {
    width: 255px !important;
  }
  .dashboard-title {
    margin-right: -3px;
    height: 120px !important;
  }
  .custom-options-css {
    border-radius: 8px;
    min-width: 235px;
    min-height: 104px;
    max-height: 104px;
  }
  .table-style-css {
    max-width: 816px;
  }
  .bulkEditCheckbox1 {
    min-width: 30px !important;
    max-width: 30px !important;
  }
  .status {
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 12px;
    line-height: 0.875rem;
    padding: 0.3rem 1rem;
    height: 1.875rem;
  }
  .btn {
    height: 2.25rem;
    text-decoration: none;
    font-size: 0.875rem;
    line-height: 0.8rem;
    border-radius: 0.25rem;
    font-weight: 700;
    padding: 0.75rem 1.5rem;
    min-width: 5.438rem;
    text-transform: capitalize;
  }
  .variant-input-weight {
    width: 100px !important;
  }
  .remove_field {
    margin-right: -6px;
  }
  .asset_description {
    width: 27rem !important;
  }
}
@media only screen and (min-width: 1500px) and (max-width:1900px) {
  .remove_field {
    margin-right: -6px;
  }
}
@media only screen and (min-width:992px) and (max-width:1199px) {
  .folder {
    width: 12rem;
  }
  .main-sidebar {
    width: 14.438rem;
  }
  .right-side {
    margin-left: 14.438rem;
  }
  .billing-custom-css_product {
    left: -44px;
    bottom: -166px;
  }
  .dashboard-title {
    margin-right: -3px;
    height: 120px !important;
  }
  .custom-options-css {
    border-radius: 8px;
    min-width: 320px;
    max-width: 328px;
    min-height: 104px;
    max-height: 104px;
  }
  .back-btn-css {
    position: absolute;
    color: #000;
    top: 10%;
    left: 0%;
  }
  .mapping_footer {
    width: calc(100% - 227px);
  }
  .table-style-css {
    max-width: calc(100vw - 40px);
  }
  .billing-custom-css {
    width: 106%;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 16px;
    padding: 14px 10px 14px 10px;
    background: #fff3f4;
    box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.16);
  }
  .billing-custom-css_product {
    left: -37px;
    bottom: -140x;
  }
  .billing-custom-css_main {
    left: 12px;
    bottom: -140x;
  }
  #table-scroll2 {
    max-width: 77vw;
  }
  #main-table {
    width: 100%;
  }
  .bulkEditCheckbox1 {
    width: 30px !important;
  }
  .status {
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 12px;
    line-height: 0.875rem;
    padding: 0.3rem 1rem;
    height: 1.875rem;
  }
  .btn {
    height: 2.25rem;
    text-decoration: none;
    font-size: 0.875rem;
    line-height: 0.8rem;
    border-radius: 0.25rem;
    font-weight: 700;
    padding: 0.75rem 1.5rem;
  }
  .asset_description {
    width: 25rem !important;
  }
  .variant-input-weight {
    width: 100px !important;
  }
  .input-width-css {
    width: 96.4% !important;
  }
  .remove_field {
    margin-right: -6px;
  }
  .review-text {
    padding: 0px 0px;
  }
}
@media only screen and (min-width: 1200px) and (max-width:1500px) {
  html {
    font-size: 0.875rem;
  }
  .folder {
    width: 13rem;
  }
  .card-width-lg {
    width: 310px !important;
  }
  .options1 {
    margin-top: 24px;
    min-width: 208px;
    font-size: 14px;
    line-height: 24px;
    font-weight: 700;
    height: 50px;
    border: 1px solid #252525;
    border-radius: 4px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
  .table-style-css {
    max-width: 1056px;
  }
  .margin-start-card {
    margin-left: 0px;
  }
  .score-label {
    min-width: 1rem;
    max-width: 2rem;
    height: 0.563rem;
    border-radius: 1rem;
  }
  .product-name {
    padding: 0px 10px !important;
  }
  #table-scroll2 {
    max-width: calc(100vw - 267px);
  }
  #main-table {
    width: 100%;
  }
  .custom-options-css {
    border: 1px solid #e2e2e3;
    border-radius: 8px;
    min-width: 330px;
    max-width: 300px;
    min-height: 104px;
    max-height: 104px;
  }
  .variant-input-weight {
    width: 95px !important;
  }
  .asset_description {
    width: 43rem !important;
  }
  .remove_field {
    float: right;
    font-size: 20px;
    margin-top: 6px;
    margin-right: -6px;
    font-weight: 400;
    padding: 0px;
    border: none;
    background: none;
    color: red;
  }
  .remove_field:hover {
    float: right;
    font-size: 20px;
    margin-top: 6px;
    margin-right: 0px;
    font-weight: 400;
    color: red;
  }
  .mapping_footer {
    position: fixed;
    bottom: 0px;
    right: 0px;
    margin: auto;
    height: 100px;
    width: calc(100% - 211px);
    background-color: #f9fbff;
    border: 1px solid #bfbfbf;
  }
  .review-text {
    padding: 0px 40px;
  }
}
@media only screen and (max-width:1199px) {
  .checkbox-border {
    margin-bottom: 12px;
  }
}
@media only screen and (min-width: 1440px) and (max-width:1580px) {
  .card-width-lg {
    width: 362px !important;
  }
}
@media (min-width: 1200px) and (max-width: 1300px) {
  html {
    font-size: 0.938rem;
  }
}
@media (max-width: 768px) {
  .hamburgericon {
    display: block;
  }
  .main-sidebar {
    width: 51px;
    border-right: 1px solid #E6E6E6;
    overflow-y: hidden !important;
  }
  .btn {
    padding: 0.75rem 0.5rem;
  }
  .mobile-menu {
    width: 170px;
    border-right: 1px solid #E6E6E6;
    background: #fff;
    overflow-y: hidden !important;
  }
  .right-side {
    margin-left: 51px;
  }
  .contentwrapper,
  .mainheader {
    margin-left: 0;
  }
  .fixedactionbtns {
    width: 100%;
    left: 0;
  }
  .dashboard-title {
    margin-right: -3px;
    height: 154px;
  }
}

/*# sourceMappingURL=screen.css.map */
