@import "variables";
@font-face {
    font-family: "icomoon";
    src: url("../../assets/fonts/icomoon.eot?yl0gnj");
    src: url("../../assets/fonts/icomoon.eot?yl0gnj#iefix") format("embedded-opentype"),
        url("../../assets/fonts/icomoon.ttf?yl0gnj") format("truetype"), url("../../assets/fonts/icomoon.woff?yl0gnj") format("woff"),
        url("../../assets/fonts/icomoon.svg?yl0gnj#icomoon") format("svg");
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

[class^="icon-"],
[class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: "icomoon" !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

// Background & colors
.bg-white {
    background-color: $white;
}
.bg-white-smoke {
    background-color: $white-smoke !important;
}
.bg-light300 {
    background-color: $light300;
}
.bg-black {
    background-color: $black !important;
}
.bg-light {
    background-color: $light;
}
.bg-light-blue {
    background: $light-blue;
}
.bg-primaryLog {
    background: #f8f9ff;
}
.bg-primary {
    background-color: $blue !important;
}
.bg-Success {
    background-color: $success;
}
.bg-Warning {
    background-color: $warning;
}
.bg-light-yellow {
    background-color: $light-yellow !important;
}
.bg-blue {
    background-color: $blue;
}
.bg-white-smoke {
    background-color: $white-smoke;
}
// colors

.clr-white {
    color: $white;
}
.clr-black {
    color: $black;
}
.clr-light {
    color: $light;
}
.clr-success {
    color: $success;
}
.clr-blue {
    color: $blue !important;
}
.clr-grey {
    color: $grey !important;
}
.clr-dark-black {
    color: $dark-black !important;
}
.text-primary {
    color: $primary !important;
}

//margins
.mt-80 {
    margin-top: toRem(80);
}
.mt-60 {
    margin-top: toRem(60);
}
.mt-40 {
    margin-top: toRem(40);
}
.mb-40 {
    margin-bottom: toRem(40);
}
.mt-32 {
    margin-top: toRem(32);
}
.mb-24 {
    margin-bottom: 1.5rem;
}
.mt-28 {
    margin-top: toRem(28);
}
.mt-14 {
    margin-top: toRem(14);
}
.ml-40 {
    margin-left: toRem(40);
}

//paddings
.pb-60 {
    padding-bottom: toRem(60) !important;
}
.pb-100 {
    padding-bottom: toRem(100) !important;
}
//left
.pl-12 {
    padding-left: 12px !important;
}

//font sizes
.fs-12 {
    font-size: 0.75rem;
}
.fs-14 {
    font-size: 0.875rem;
}
.fs-16 {
    font-size: 1rem !important;
}
.fs-18 {
    font-size: toRem(18);
}
.fs-20 {
    font-size: toRem(20) !important;
}
.fs-24 {
    font-size: toRem(24) !important;
}
.fs-28 {
    font-size: toRem(28);
}
.fs-32 {
    font-size: toRem(32) !important;
}

//font-weights
.fw-400 {
    font-weight: 400 !important;
}
.fw-450 {
    font-weight: 450 !important;
}
.fw-500 {
    font-weight: 500 !important;
}
.fw-600 {
    font-weight: 600 !important;
}
.fw-700 {
    font-weight: 700 !important;
}
// width classes
.w-300 {
    width: 330px !important;
}
.w-19 {
    width: 19%;
}
.w-95 {
    width: 95%;
}
.min-vh-100 {
    // min-height: 99.7vh !important;
    min-height: 800px !important;
}

// border
// bottom
.bbr-2 {
    border-bottom: 2px solid #000;
}

// height class
.h-120 {
    height: 7.5rem !important;
}
.h-40 {
    height: 2.5rem !important;
}
.h-36 {
    height: 2.25rem !important;
}

.text-decotation-underline {
    text-decoration: underline !important;
}

.pointer {
    cursor: pointer;
}
.btrr-30 {
    border-top-right-radius: 30px;
}

.bbrr-30 {
    border-bottom-right-radius: 30px;
}
.btn {
    height: 2.25rem;
    text-decoration: none;
    font-size: 0.875rem;
    line-height: 0.8rem;
    border-radius: 0.25rem;
    font-weight: 700;
    padding: 0.75rem 1.5rem;
    min-width: 6.438rem;
    text-transform: capitalize;
    &:disabled {
        background-color: $gains-boro;
        border: 1px solid $gains-boro;
        color: $light;
    }
}

// primary btn
.btn-primary {
    background-color: $primary;
    border-color: $primary;
    color: $white;
    &:hover,
    &:focus {
        background-color: transparent;
        border-color: $primary;
        text-decoration: none;
        color: $primary;
    }
    
}
.btn-outline-primary {
    color: $primary;
    border-color: $primary;
    &:hover {
        text-decoration: none;
        background-color: $primary;
        border-color: $primary;
        color: $white;
    }
}

// btn danger
.btn-danger {
    background-color: $red;
    border-color: $red;
    color: $white;
    &:hover {
        text-decoration: none;
        color: $white;
    }
}
.btn-outline-danger {
    color: $red;
    &:hover {
        text-decoration: none;
        background-color: $red;
        border-color: $red;
        color: $white;
    }
}

//  medium button
.btn-md {
    height: 2.25rem;
    background-color: $primary;
    border-color: $primary;
    border: 0;
    color: $secondary;
    font-size: 0.875rem; // 1rem => 16px
    line-height: 1rem; // 1.5rem => 24px
    border-radius: 0.25rem;
    padding: 0.5rem 1rem;
    text-transform: capitalize;
    &:hover {
        color: $secondary;
        background-color: $primary;
        text-decoration: none;
        border-color: $primary;
    }
    &:disabled {
        background-color: $gains-boro;
        border: 1px solid $gains-boro;
        color: $light;
    }
    &:focus {
        background-color: $primary;
        border-color: $primary;
        color: $secondary;
    }
}

.btn-sm {
    height: 1.875rem;
    font-size: 0.875rem;
    line-height: 1rem;
    border-radius: 0.25rem;
    padding: 0.43rem 0.75rem;
    text-decoration: none;
    &:disabled {
        background-color: $gains-boro;
        border: 1px solid $gains-boro;
        color: $light;
    }
}

// btn outline dark
.btn-outline-dark {
    background-color: $white;
    border: 1px solid $black;
    color: $black;
    &:hover {
        color: $white;
        background-color: $black;
        text-decoration: none;
        border-color: $black;
    }
    &:disabled {
        background-color: $gains-boro;
        border: 1px solid $gains-boro;
        color: $light;
    }
    &:focus {
        background-color: $black;
        border-color: $black;
        color: $white;
    }
}

.search {
    img {
        position: absolute;
        top: 0;
        left: 20px;
        bottom: 0;
        margin: auto;
    }
    input {
        height: 2.25rem;
        padding-left: 3.125rem;
    }
}
.form-control {
    font-weight: 500;
}
.form-control:focus {
    color: $black;
    background-color: #fff;
    border-color: $dark-black;
    outline: 0;
    box-shadow: none;
}

// Common Components
html {
    body {
        font-family: $primary-family;
        font-size: $primary-fs;
        font-weight: 450;
        line-height: 18px;
        a {
            text-decoration: underline;
            outline: none !important;
            &:hover {
                text-decoration: underline;
            }
        }

        // Headings
        h1,
        .h1 {
            font-family: $primary-family;
            color: $secondary;
            font-size: $h1-font-size;
            font-weight: 700;
            line-height: $line-28;
        }
        h2,
        .h2 {
            font-family: $primary-family;
            font-size: $h2-font-size;
            color: $secondary;
            font-weight: 700;
            line-height: $line-24;
        }
        h3,
        .h3 {
            font-size: $h3-font-size;
            font-weight: 500;
            color: $secondary;
            line-height: $line-24;
        }
        h4,
        .h4 {
            font-size: $h4-font-size;
            color: $secondary;
            font-weight: 700;
            line-height: $line-24;
        }
        h5,
        .h5 {
            font-size: $h5-font-size;
            color: $secondary;
            font-weight: 500;
            line-height: $line-18;
        }
        h6,
        .h6 {
            font-size: $h6-font-size;
            color: $secondary;
            font-weight: 500;
            line-height: $line-16;
        }
        p {
            font-family: $primary-family;
            color: $black;
            font-weight: 400;
            font-size: 1rem;
            line-height: $line-24;
        }
        .p1 {
            font-family: $primary-family;
            font-weight: 400;
            color: $secondary;
            font-size: 0.75rem;
            line-height: $line-18;
        }
    }
}
small {
    font-size: 0.75rem;
}

// table start
.border-top-design {
    border-top: 2px solid #dee2e6 !important;
}
.circle {
    min-width: 40px;
    max-width: 40px;
    min-height: 40px;
    max-height: 40px;
    border: 2px solid #fff;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50px;
    line-height: 2.1;
}
.circle-sm {
    min-width: 34px;
    max-width: 34px;
    min-height: 34px;
    max-height: 34px;
    border: 2px solid #fff;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50px;
    line-height: 2.1;
}
//table

.table > thead > * > * {
    height: 48px;
    padding: 12px 12px;
    background-color: var(--bs-table-bg);
    border-bottom-width: 1px solid grey;
    box-shadow: none;
    font-size: 0.875rem;
    line-height: 18px;
    color: $black;
    font-weight: 500;
    vertical-align: middle;
}
.table > tbody > tr > td {
    // padding: 1rem 1rem 1rem 0.5rem;
    color: $neutral800;
    border-bottom-width: 0px;
    box-shadow: none;
    font-size: 0.875rem;
    line-height: 18px;
    cursor: pointer;
    font-weight: 400;
}

.table {
    thead {
        th {
            background-color: $white-smoke;
            min-height: 44px;
            &:first-child {
                padding-left: 10px;
            }
            &:last-child {
                padding-right: 10px;
            }
        }
    }
    tbody {
        tr {
            td {
                border-bottom-width: 0;
                vertical-align: middle;
                height: 44px;
                &:first-child {
                    padding-left: 10px;
                }
                &:last-child {
                    padding-right: 10px;
                }
            }
        }
        tr.ui-sortable-handle:hover {
            td {
                cursor: move;
            }
        }
    }
}

.table-image-width {
    max-width: 40px;
    min-width: 40px;
    max-height: 40px;
    min-height: 40px;
    border-radius: 4px;
    object-fit: cover;
}
thead,
tr {
    border-color: inherit;
    border-style: unset !important;
    border-width: 0;
    border-top: 0;
    border-bottom: 1px solid $neutral100 !important;
}

.table > :not(:last-child) > :last-child > * {
    border-bottom-color: $silver !important;
}

// tab table end
.table-card {
    flex: 1 1 auto;
    padding: 0rem 0.8rem;
}
.table > :not(caption) > * > * {
    padding: 0rem 0.4rem;
}

.table > tbody > tr {
    &:hover {
        background-color: $white-smoke !important;
    }
    &:focus {
        background-color: $white-smoke !important;
    }
}
.table-head-border {
    border-radius: 4px 4px 0px 0px !important;
}
// table end

.dropdown-toggle {
    &::after {
        display: none;
    }
}
.dropdown-menu {
    background: #ffffff;
    box-shadow: 0px 1px 20px rgba(0, 0, 0, 0.07);
    border: 0;
    a {
        padding: 10px 24px;
        display: block;
        &:last-child {
            margin-bottom: 0;
        }
    }
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox */
input[type="number"] {
    -moz-appearance: textfield;
}

.formStyle {
    ::placeholder {
        font-weight: 400;
        font-size: $secondary-fs;
        line-height: $line-18;
        color: $dim-gray;
    }
    label {
        font-weight: 500;
        font-size: 14px;
        line-height: 16px;
        color: $dark-black;
    }
    .form-control:disabled,
    .form-control[readonly] {
        color: $grey;
        // background-color: $white;
    }
    input {
        border: 1px solid $gains-boro;
        background-color: $white-smoke;
        color: $black;
        line-height: 1.25rem;
        height: 2.25rem;
        padding: 0.5rem 0.75rem;
        border-radius: 4px;
        font-size: 0.875rem;
        font-weight: 400;
        &:focus {
            outline: 0;
            border: 1px solid $blue;
            color: $black;
            box-shadow: none;
        }
    }
    ::-webkit-input-placeholder {
        /* Edge */
        font-weight: 500;
    }

    :-ms-input-placeholder {
        /* Internet Explorer 10-11 */
        font-weight: 500;
    }

    ::placeholder {
        font-weight: 400;
        color: $grey;
    }
    select {
        border: 1px solid $gains-boro;
        color: $black;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
        font-weight: 400;
        border-radius: 4px;
        width: 100%;
        height: 2.25rem;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background-image: url("../images/down_black.svg");
        padding: 0.375rem 1.8rem 0.375rem 0.75rem;
        background-repeat: no-repeat;
        background-position: right 8px center;
        background-size: 20px;
        &:focus {
            outline: 0;
            border: 1px solid $blue;
            color: $black;
            box-shadow: none;
        }
    }

    textarea {
        border: 1px solid $gains-boro;
        background-color: $white-smoke;
        font-weight: 400;
        border-radius: 4px;
        &:focus {
            outline: 0;
            border: 1px solid $blue;
            box-shadow: none;
        }
    }
    .form-check {
        line-height: 23px;
        label {
            font-size: $secondary-fs;
            color: $black;
        }
        &:focus {
            outline: 0;
            box-shadow: none;
        }
    }
    .form-check-input:checked {
        background-color: $primary;
        border-color: $primary;
        color: $white;
    }
    .form-check-input:focus {
        border-color: none;
        outline: 0;
        box-shadow: none;
    }
    .form-switch .form-check-input {
        width: 40px;
        height: 18px;
        //   background-image: url(../images/circle.svg);
    }
    .form-control-sm {
        height: 30px;
    }
}

.comboTreeInputBox::placeholder {
    color: #252525;
    font-size: 0.875rem;
}

.mdi-chevron-down {
    opacity: 0;
}
.select-small {
    height: 30px;
}

// switch css
.form-check .form-check-input {
    float: none;
    margin-left: -1.5em;
}
.form-switch .form-check-input {
    margin-top: 1px;
}

// tabs style
.nav-item.active-link {
    border-left: 3px solid $blue;
    font-weight: 600 !important;
}
.active-product-link {
    color: $primary !important;
    font-weight: 600 !important;
}
.product-link {
    color: $primary !important;
}
.active-link-product {
    color: $primary !important;
}
// sidebar
.nav-item .nav-link {
    margin-bottom: 0px;
    display: flex;
    align-items: center;
    height: 2.5rem;
    color: $black;
    font-weight: 400;
    font-size: 0.875rem;
    line-height: 1.25rem;
    background-color: $white;
    color: #000;
    padding: 0.5rem 1.3rem;
    background: 0 0;
    border-radius: 0.5rem 0.5rem 0rem 0rem;
    &:hover {
        text-decoration: none;
        background-color: rgb(248, 249, 250);
        color: $primary;
    }
}

// product page
#pills-tab .nav-item .nav-link {
    margin-bottom: 0px;
    display: flex;
    align-items: center;
    height: 2.5rem;
    color: $black;
    font-weight: 400;
    font-size: 0.875rem;
    line-height: 1.25rem;
    background-color: $white;
    color: #000;
    font-weight: 600;
    padding: 0.5rem 1.3rem;
    background: 0 0;
    border-radius: 0.5rem 0.5rem 0rem 0rem;
    &:hover {
        text-decoration: none;
        background-color: rgb(248, 249, 250);
        color: $primary;
    }
}

.icon {
    font-size: 1.25rem;
}
// list group active
.list-group-item.active {
    z-index: 2;
    color: #fff;
    background-color: $blue;
    border-color: $blue;
}
// modal
.modal-header {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1rem;
    border-bottom: 1px solid #dee2e6;
    border-top-left-radius: calc(0.3rem - 1px);
    border-top-right-radius: calc(0.3rem - 1px);
}

.modal-footer {
    display: flex;
    flex-wrap: wrap;
    flex-shrink: 0;
    align-items: center;
    justify-content: flex-end;
    padding: 0px;
    border-top: 1px solid #dee2e6;
    border-bottom-right-radius: calc(0.3rem - 1px);
    border-bottom-left-radius: calc(0.3rem - 1px);
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
    color: $primary !important;
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1.25rem;
    background-color: $white;
    border-bottom: 2px solid $primary;
}

// scrollbar css
::-webkit-scrollbar {
    width: 0.4rem;
    height: 9px;
}

::-webkit-scrollbar-track {
    -webkit-box-shadow: none;
}
::-webkit-scrollbar-thumb {
    background-color: $light-grey;
    outline: none;
    border-radius: 83px;
}
::-webkit-scrollbar-thumb:hover {
    background-color: $light-grey;
}
.success {
    p {
        color: $black;
    }
}
.modal-backdrop.show {
    background: $secondary;
    opacity: 0.7;
}

// logo sidebar css
#logo_sidebar {
    width: 36px;
    height: 36px;
}
#logo_sidebar_product {
    width: 29px;
    height: 34px;
    margin-left: 3px;
}

.right-side {
    margin-left: 15.438rem;
}
.fa-bars-css {
    display: none;
}
.submenu {
    position: absolute;
    width: 11.8rem;
    background: $white-smoke;
    z-index: 99;
    top: 0px;
    left: 3.6rem;
    height: 99vh;
}
#submenu_product {
    padding-top: 9.7rem !important;
}
/* sidebar billing time*/
.billing-timer {
    width: 100%;
    bottom: 0px;
}
.billing-custom-css {
    width: 106%;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 16px;
    padding: 14px 10px 14px 10px;
    background: #fff3f4;
    box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.16);
    &_product {
        left: -37px;
        bottom: -166px;
    }
    &_main {
        left: 12px;
        bottom: -166px;
    }
}
.billing-custom-css-main-menu {
    width: 90% !important;
}
.progress {
    height: 0.313rem;
    width: 157px;
}
.status {
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 12px;
    line-height: 0.875rem;
    padding: 0.5rem 1.5rem;
    height: 1.875rem;
    &-publish {
        background-color: #e7ecff;
        color: $blue;
    }
    &-synced {
        background-color: #d7f8de;
        color: $dark-green;
    }
    &-draft {
        background-color: #eeeeef;
        color: $grey;
    }
    &-success {
        background-color: #d7f8de;
        color: $success;
    }
    &-warning {
        background-color: #fff5d6;
        color: $warning;
    }
    &-danger {
        background-color: #ffefef;
        color: $red;
    }
}
.notification-status-css {
    padding: 0.3rem 1rem !important;
    height: 1rem !important;
    border-radius: 4px;
}
.check-box-product {
    border: 1px solid $silver !important;
    background-color: #f2f2f3 !important;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
    margin-right: -1px;
    width: 95px !important;
    border-right: 0px;
    padding: 7px 8px;
}
.border-left-top-bottom {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}
// circle css
.circle-sm.circle-error {
    /* background: #FFEFEF; */
    border: 6px solid #ffefef;
    margin-bottom: 2px;
    background: #dc3545;
    min-width: 17px;
    max-width: 17px;
    min-height: 17px;
    max-height: 17px;
    display: inline-block;
    line-height: 2.1;
}
.circle-sm.circle-success {
    border: 6px solid #d7f8de;
    margin-bottom: 2px;
    background: #28a745;
    min-width: 17px;
    max-width: 17px;
    min-height: 17px;
    max-height: 17px;
    display: inline-block;
    line-height: 2.1;
}
.circle-sm {
    border-radius: 50%;
}

.tox-tinymce {
    width: 100% !important;
    height: 500px !important;
}
// score label
.score-label {
    width: 2.375rem;
    height: 0.563rem;
    border-radius: 1rem;
    &-publish {
        background-color: $blue;
    }
    &-success {
        background-color: $success;
    }
    &-draft {
        background-color: $grey;
    }
    &-grey {
        background-color: #eeeeef;
    }
    &-warning {
        background-color: $warning;
    }
    &-danger {
        background-color: $red;
    }
}
.field-score-chart {
    height: 110px;
    width: 110px;
}
.media-score-chart {
    height: 166px;
    width: 166px;
}
.field-score-chart-tab {
    height: 166px;
    width: 166px;
}
// popover
.popover {
    max-width: 350px;
    max-height: 2.5rem;
    display: flex;
    align-items: center;
    width: auto;
}
.is-invalid {
    border: 1px solid $red !important;
}
.notification {
    min-width: 8px;
    max-width: 8px;
    min-height: 8px;
    max-height: 8px;
    border-radius: 50%;
    &-danger {
        background-color: $red;
    }
    &-primary {
        background-color: $blue;
    }
    &-dark {
        background-color: $light-grey;
    }
}
.progress-cart-css {
    min-width: 20px;
    max-width: 20px;
    min-height: 20px;
    max-height: 20px;
    border-radius: 4px;
}
