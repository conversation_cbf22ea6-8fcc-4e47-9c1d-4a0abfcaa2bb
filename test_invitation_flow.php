<?php

/**
 * Test script to verify team invitation flow
 * Run with: php test_invitation_flow.php
 */

require_once 'vendor/autoload.php';

use App\Models\Organization\TeamInvite;
use App\Models\Organization\Organization;
use App\User;
use Illuminate\Support\Facades\DB;

// Initialize Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Team Invitation Flow Test ===\n\n";

try {
    // Test 1: Check if team invitations exist
    echo "1. Checking existing team invitations...\n";
    $invitations = TeamInvite::withoutGlobalScopes()->get();
    echo "   Found " . $invitations->count() . " team invitations\n";

    if ($invitations->count() > 0) {
        $invitation = $invitations->first();
        echo "   Sample invitation: {$invitation->email} -> Organization {$invitation->organization_id}\n";

        // Test 2: Check if user exists for this email
        echo "\n2. Checking if user exists for invitation email...\n";
        $user = User::where('email', $invitation->email)->first();
        if ($user) {
            echo "   User exists: {$user->fname} {$user->lname} (ID: {$user->id})\n";

            // Test 3: Check organization membership
            echo "\n3. Checking organization membership...\n";
            $memberships = $user->organizations()->get();
            echo "   User is member of " . $memberships->count() . " organizations\n";
            foreach ($memberships as $org) {
                echo "   - Organization: {$org->name} (ID: {$org->id})\n";
            }

            // Test 4: Check permissions
            echo "\n4. Checking invitation permissions...\n";
            $permissions = $invitation->permissions()->get();
            echo "   Invitation has " . $permissions->count() . " permissions:\n";
            foreach ($permissions as $permission) {
                echo "   - {$permission->name} ({$permission->handle})\n";
            }
        } else {
            echo "   No user found for email: {$invitation->email}\n";
        }
    }

    // Test 5: Check available permissions
    echo "\n5. Checking available permissions...\n";
    $allPermissions = \App\Models\Organization\Permission::all();
    echo "   Total permissions available: " . $allPermissions->count() . "\n";
    foreach ($allPermissions as $permission) {
        echo "   - {$permission->name} ({$permission->handle})\n";
    }

    // Test 6: Create a test invitation to verify the flow
    echo "\n6. Creating a test team invitation...\n";
    $testEmail = '<EMAIL>';

    // Check if test invitation already exists
    $existingInvitation = TeamInvite::withoutGlobalScopes()->where('email', $testEmail)->first();
    if ($existingInvitation) {
        echo "   Test invitation already exists for: {$testEmail}\n";
        echo "   Organization ID: {$existingInvitation->organization_id}\n";
    } else {
        echo "   No test invitation found for: {$testEmail}\n";
        echo "   You can create one using the OnboardingSeven component\n";
    }

    // Test 7: Generate invitation URL
    echo "\n7. Testing invitation URL generation...\n";
    $invitationUrl = route('register', [
        'email' => $testEmail,
        'fname' => 'Test',
        'lname' => 'User'
    ]);
    echo "   Sample invitation URL: {$invitationUrl}\n";

    echo "\n=== Test completed successfully ===\n";
    echo "\n=== Testing Instructions ===\n";
    echo "1. Create a team invitation using OnboardingSeven component\n";
    echo "2. Use the invitation email link to register\n";
    echo "3. Verify that the user is redirected to dashboard after email verification\n";
    echo "4. Check that the user is automatically added to the inviting organization\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
