# ![Apimio App](public/media/logo.png)

[![GitHub license](https://img.shields.io/github/license/gothinkster/laravel-realworld-example-app.svg)](https://raw.githubusercontent.com/gothinkster/laravel-realworld-example-app/master/LICENSE)

> ### Drive more sales with better product experience management 
> A<PERSON><PERSON> makes every part of your product journey more efficient, getting started with optimized product data on your online stores has never been faster and easier.

This repo is in progress

----------

# Getting started

## Installation

Please check the official laravel installation guide for server requirements before you start. [Official Documentation](https://laravel.com/docs/5.4/installation#installation)

Installation guide.

Clone the repository

    <NAME_EMAIL>:apimio/apimio.git

Switch to the repo folder

    cd apimio

Install all the dependencies using composer

    composer install

Copy the example env file and make the required configuration changes in the .env file

    cp .env.example .env

Generate a new application key

    php artisan key:generate

Run the database migrations (**Set the database connection in .env before migrating and create database**)

    php artisan migrate

Seed database

    php artisan db:seed

Start the local development server

    php artisan serve

You can now access the server at http://localhost:8000

Run queues for big tasks

    php artisan queue:work

## Ziggy setup
php artisan ziggy:generate


## Database seeding

***Note*** : It's recommended to have a clean database before seeding. You can refresh your migrations at any point to clean the database by running the following command

    php artisan migrate:refresh

***Note*** : You can also refresh and seed with one command

    php artisan migrate:refresh --seed

# Code overview

## Folders

- `app` - Contains all the Eloquent models
- `app/Http/Controllers/Api` - Contains all the controllers
- `app/Http/Middleware` - Contains the auth middleware
- `config` - Contains all the application configuration files
- `database/factories` - Contains the model factory for all the models
- `database/migrations` - Contains all the database migrations
- `database/seeds` - Contains the database seeder
- `routes` - Contains all the routes defined in web.php file

## Environment variables

- `.env` - Environment variables can be set in this file

***Note*** : You can quickly set the database information and other variables in this file and have the application fully working.

----------

# Testing

For testing purpose application has test data which are called factories. You can generate factory data using:

    php artisan migrate:fresh --seed
    php artisan db:seed --class=FactorySeeder

For automated unit and feature testing:

    vendor/bin/phpunit


Run the laravel development server

    php artisan serve

The application can now be accessed at

    http://localhost:8000/

----------

# Helping git Commands

This command is used to remove specific folder or file from committing to bitbucket.


    git rm -r --cached <some-directory>

----------

##Stripe Integration
Make stripe account

Copy stripe keys into env file
    
    STRIPE_KEY (Publishable key)
    STRIPE_SECRET (Secret key)
        
Make products in the stripe and copy price_id's into env file

    COMMUNITY_MONTHLY_PRICE_ID //create free product plan with monthly status 
    COMMUNITY_YEARLY_PRICE_ID //create free product plan with yealy status
    STANDARD_MONTHLY_PRICE_ID //create $199 monthly plan
    STANDARD_YEARLY_PRICE_ID //create $159 yearly plan
    PLUS_MONTHLY_PRICE_ID //create $999 monthly plan
    PLUS_YEARLY_PRICE_ID //create $799 yearly plan
    CATALOG_PRICE_ID // create $30 catalog plan 

Make webhook endpoint on stripe and copy stripe webhook secret into env file.

    STRIPE_WEBHOOK_SECRET
    
Now refresh and seed database to get everything running
    
    php artisan migrate:fresh --seed
