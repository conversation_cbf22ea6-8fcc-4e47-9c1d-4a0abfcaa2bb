# README #

<h1 align="center">Welcome to Mapping Connector Package (APIMIO) 👋</h1>
<p align="center">
  <img src="https://img.shields.io/badge/version-1.0.0-blue.svg?cacheSeconds=2592000" />
   <a href="https://www.linkedin.com/in/imnadir07/">
    <img alt="Nadir Ali" src="https://img.shields.io/badge/LinkedIn-blue?style=flat&logo=linkedin&labelColor=blue&label=imnadir07">
  </a>
</p>

This package is use to map product attributes according to provided formulas.

### What is this repository for? ###

* Quick summary
  * This package includes the product attributes mapping functionality. 
  * **Integrated Module:**
    * Import, export, integrations (Shopify, Magento, Woocommerce etc.) & bulk editing for Apimio store.
* Version : **1.0.0**
* Git Clone URL : [https://<EMAIL>/apimio/mapping-fields-package.git](https://<EMAIL>/apimio/mapping-fields-package.git)

### How do I get set up? ###

* Configuration
  * When you use **composer update**
    <p><b>OR</b></p>
    <b>composer update</b> "apimio/mapping-connector-package"
  * It requires credentials for this package integration so you need to add this key in password
    * **ATBB2sF46ZvNAevmjwJPQWMBqnYF9CFF5B45**
* Dependencies
  * **Laravel >= 7**
  * **PHP >= 7.3**
* Database configuration
  * php artisan migrate


### Who do I talk to? ###

* Author: 
  * 👤 **Nadir Ali**  [@imnadir07](https://www.linkedin.com/in/imnadir07/)
* Team Members: 
  * 👤 **Gazanfar Ali**
  * 👤 **Hashir Masood**
  * 👤 **Mubashir Hussain**
  * 👤 **Iqtedar Hussain**
  * 👤 **Moqeet Ul Hassan**
