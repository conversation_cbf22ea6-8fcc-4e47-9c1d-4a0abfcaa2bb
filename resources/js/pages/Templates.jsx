import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, Row, Col, Input, message, Modal } from "antd";
import { CheckCircleOutlined, UploadOutlined, SyncOutlined, DeleteOutlined, SearchOutlined } from "@ant-design/icons";
import axios from "axios";
import SVG1 from "../../../../../../public/v2/icons/CSV.svg";
import ProductsLayout from "@/Pages/product/ProductsLayout.jsx";
import { router } from "@inertiajs/react";

const Templates = ({ templates }) => {
    const [searchTerm, setSearchTerm] = useState("");
    const [filteredTemplates, setFilteredTemplates] = useState(templates || []);

    // Update filtered templates when templates prop changes
    useEffect(() => {
        setFilteredTemplates(templates || []);
    }, [templates]);

    // Format date function
    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString("en-US", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
        });
    };

    // Handle search input change
    const handleSearch = (e) => {
        const value = e.target.value;
        setSearchTerm(value);

        if (!templates) return;

        if (value.trim() === "") {
            setFilteredTemplates(templates);
        } else {
            const filtered = templates.filter((template) =>
                template.name.toLowerCase().includes(value.toLowerCase())
            );
            setFilteredTemplates(filtered);
        }
    };

    // Create a ref for the hidden form
    const formRef = useRef(null);

    // Handle apply template
    const handleApplyTemplate = (template) => {
        // Show loading message
        const loadingMsg = message.loading('Applying template...', 0);

        // Use Inertia router to make a POST request
        router.get('/products/import/apply-template', {
            template_id: template.id,
        }, {
            onSuccess: () => {
                message.success('Template applied successfully');
                // The Inertia response will automatically handle the redirect to CSVMapping
            },
            onError: (error) => {
                console.error('Error applying template:', error);
                message.error('Failed to apply template. Please try again.');
            },
            onFinish: () => {
                // Clean up the loading message
                loadingMsg();
            }
        });
    };

    // Handle delete template
    const handleDeleteTemplate = (template) => {
        Modal.confirm({
            title: 'Delete Template',
            content: `Are you sure you want to delete the template '${template.name}'?`,
            okText: 'Yes',
            okType: 'danger',
            cancelText: 'No',
            onOk: () => {
                return new Promise((resolve, reject) => {
                    // Show loading message
                    const loadingMsg = message.loading('Deleting template...', 0);

                    // Use Inertia router to make a POST request with _method=DELETE
                    router.post('/products/import/delete-template', {
                        _method: 'DELETE',
                        template_id: template.id
                    }, {
                        onSuccess: () => {
                            // The success message will be shown by the server response
                            // and the page will be reloaded automatically
                            resolve(); // Close the modal
                        },
                        onError: (error) => {
                            console.error('Error deleting template:', error);
                            message.error('Failed to delete template. Please try again.');
                            reject(); // Keep the modal open
                        },
                        onFinish: () => {
                            // Clean up the loading message
                            loadingMsg();
                        }
                    });
                });
            },
        });
    };

    // Handle create new template
    const handleCreateTemplate = (e) => {
        e.preventDefault()
        router.get("/products/import/create-template", {}, {
            onSuccess: () => {
                message.success("Now you can map data and then create a template.");
                // Add logic to handle success, e.g., refreshing the template list
            },
            onError: (error) => {
                console.error("Error creating template:", error);
                message.error("Failed to map data and create a template. Please try again.");
            },
        });
    };

    return (
        <ProductsLayout activeTab="importcsv">
            <>
        <div className="h-full p-0 m-0">
            <Row gutter={[20, 20]} className="pb-[20px]">
                <Col span={12}>
                    <div>
                        <p className="text-[#252525] font-[600] text-[18px]">Mapping Products</p>
                        <p className="text-[#626262] font-normal text-[14px]">Mapping convertion of your all products</p>
                    </div>
                </Col>
                <Col span={12}>
                    <div className="flex justify-end gap-[8px]">
                        <Button
                            className="bg-[#740898] text-white border border-[#740898] rounded-[4px]"
                            onClick={handleCreateTemplate}
                        >Create New Template</Button>
                    </div>
                </Col>
            </Row>
            <div className={`bg-white min-h-screen rounded-[12px] border border-[#DBDBDB]   pb-0 mb-0`}>
                <div className="flex justify-between items-center p-4 border-b-1 border-gray-300 pb-2">
                    <div>
                        <p className="text-[#252525] font-[600] text-[18px]">Templates</p>
                        <p className="text-[#626262] font-normal text-[14px]">
                            Would you like to use one of your previously saved templates for import?
                        </p>
                    </div>
                    <div>
                        <Input
                            placeholder="Search Template"
                            className="p-1 border border-gray-300 rounded-md"
                            prefix={<SearchOutlined />}
                            value={searchTerm}
                            onChange={handleSearch}
                        />
                    </div>
                </div>

                {filteredTemplates && filteredTemplates.length > 0 ? (
                    filteredTemplates.map((template) => (
                        <div key={template.id} className="border-1 mt-4 border-gray-300 rounded-xl mx-4 bg-[#f9fafb]">
                            <div className="flex justify-between items-center p-4">
                                <div className="flex gap-4">
                                    <img src={SVG1} alt="Spreadsheet Icon" />
                                    <div>
                                        <p className="text-[#252525] font-[600] text-[18px]">{template.name}</p>
                                        <p className="text-[#626262] font-normal text-[14px]">
                                            Created on {formatDate(template.created_at)}
                                        </p>
                                    </div>
                                </div>
                                <div className="flex gap-4">
                                    <Button
                                        className="border-0"
                                        onClick={() => handleApplyTemplate(template)}
                                    >
                                        Apply
                                    </Button>
                                    <Button
                                        type="text"
                                        icon={<DeleteOutlined style={{ color: "#ff4d4f", fontSize: "16px" }} />}
                                        className="bg-white"
                                        onClick={() => handleDeleteTemplate(template)}
                                    />
                                </div>
                            </div>
                        </div>
                    ))
                ) : (
                    <div className="text-center p-8">
                        <p className="text-[#626262] font-normal text-[16px]">No templates found</p>
                    </div>
                )}
            </div>
        </div>
            </>
        </ProductsLayout>
    );
};

export default Templates;
