import React from "react";
import SelectAttribute from "../components/SelectAttribute";
import { Form } from "antd";
const Slug = ({ node, onFieldChange, convertedOutputArray }) => {
   const [first] = node?.to || [""];
   const handleSelectChange = (selectedValue) => {
    // We pass array or single value, depending on your design
      onFieldChange(node.id, {
      to: selectedValue,
    });
  };
  return (
    <div className="flex flex-col gap-1">
       <Form.Item
        key={0}
        label={
          <span style={{ display: "block", marginBottom: "0px" }}>
            Apimio Attribute
          </span>
        }
        //name={`nodes[data][${rowIndex}][to][]`} // Dynamic name handling
        style={{ marginBottom: "0" }}
      >
          <SelectAttribute
          options={convertedOutputArray}
          value={first}
          onChange={handleSelectChange}
        />
      </Form.Item>
    </div>
  );
};

export default Slug;
