import React, { useState, useEffect } from "react";
import { Input, Form } from "antd";
import SelectAttribute from "../components/SelectAttribute";

const Replace = ({
  node,
  convertedOutputArray,
  onFieldChange,
}) => {
  const [Search, setSearch] = useState(node.replace || "");
  const [With, setWith] = useState(node.with || "");
  const [attributeOne, setAttributeOne] = useState(node.to?.[0] || null);

  const updateParent = (newSearch, newWith, newAttr1) => {
    // Always call onFieldChange to notify parent of any changes, including cleared values
    onFieldChange(node.id, {
      replace: newSearch || "", // Pass empty string if cleared
      with: newWith || "", // Pass empty string if cleared
      to: newAttr1 ? [newAttr1] : [], // Pass empty array if cleared
    });
  };

  const handleSearchChange = (e) => {
    const newSearch = e.target.value;
    setSearch(newSearch);
  };

  const handleSearchBlur = () => {
    updateParent(Search, With, attributeOne);
  };

  const handleWithChange = (e) => {
    const newWith = e.target.value;
    setWith(newWith);
  };

  const handleWithBlur = () => {
    updateParent(Search, With, attributeOne);
  };
  const handleAttributeOneChange = (val) => {
    setAttributeOne(val);
    updateParent(Search, With, val);
  };

  useEffect(() => {
    console.log("Local state in <Replace>:", {
      Search,
      attributeOne,
      With,
    });
  }, [With, attributeOne, Search]);

  useEffect(() => {
    console.log("Replace received node prop:", node);
  }, [node]);
  return (
    <div className="flex gap-4">
      <div className="flex flex-col gap-1">
        <Form.Item label="Search" style={{ marginBottom: 0 }}>
          <Input
            style={{ width: 100 }}
            value={Search} // ensures we see the local state
            onChange={handleSearchChange}
            onBlur={handleSearchBlur}
          />
        </Form.Item>
      </div>
      <div className="flex flex-col gap-1">
        <Form.Item label="With" style={{ marginBottom: 0 }}>
          <Input
            style={{ width: 100 }}
            value={With} // ensures we see the local state
            onChange={handleWithChange}
            onBlur={handleWithBlur}
          />
        </Form.Item>
      </div>
      <div className="flex flex-col gap-1">
        <Form.Item label="Apimio Attribute" style={{ marginBottom: 0 }}>
          <SelectAttribute
            value={attributeOne} // ensure we pass the local attributeOne as the current value
            options={convertedOutputArray}
            onChange={handleAttributeOneChange}
          />
        </Form.Item>
      </div>
    </div>
  );
};

export default Replace;
