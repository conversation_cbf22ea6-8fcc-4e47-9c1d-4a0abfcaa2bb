import React, { useRef, useState, useEffect, useCallback } from 'react';
import { RefreshCcw, ChevronDown } from 'lucide-react';

export default function ShortcodeEditor({ nodes = [], onChange, onSetContent, content = null }) {
    const editorRef = useRef(null);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const [isApplied, setIsApplied] = useState(true);
    const [localContent, setLocalContent] = useState(content || '');
    const lastCursorPositionRef = useRef(null);
    const updateTimeoutRef = useRef(null);
    const shouldFocusRef = useRef(false);

    // Update localContent when content prop changes
    useEffect(() => {
        setLocalContent(content || '');
    }, [content]);

    const saveCursorPosition = useCallback(() => {
        const sel = window.getSelection();
        if (sel && sel.rangeCount > 0) {
            lastCursorPositionRef.current = sel.getRangeAt(0).cloneRange();
        }
    }, []);

    const restoreCursor = useCallback(() => {
        if (!lastCursorPositionRef.current || !editorRef.current) return;
        
        const sel = window.getSelection();
        sel.removeAllRanges();
        const range = lastCursorPositionRef.current.cloneRange();
        sel.addRange(range);
    }, []);

    const handleEdit = useCallback(() => {
        setIsApplied(false);
        saveCursorPosition();
        if (editorRef.current) {
            const cleaned = editorRef.current.innerHTML.replace(/<br\s*\/?>/gi, '').trim();
            setLocalContent(cleaned);
            if (cleaned === '') {
                editorRef.current.innerHTML = '';
                onSetContent('');
                onChange(btoa(''));
            }
        }
    }, [saveCursorPosition, onSetContent, onChange]);

    const debouncedUpdate = useCallback(html => {
        clearTimeout(updateTimeoutRef.current);
        updateTimeoutRef.current = setTimeout(() => {
            const cleanedHtml = html.replace(/<br\s*\/?>/gi, '').trim();
            setLocalContent(cleanedHtml);
            const b64 = btoa(cleanedHtml);
            onSetContent(cleanedHtml);
            onChange(b64);
            setIsApplied(true);
        }, 300);
    }, [onSetContent, onChange]);

    // Only restore cursor when content changes and we explicitly want to focus
    useEffect(() => {
        if (shouldFocusRef.current && editorRef.current) {
            editorRef.current.focus();
            restoreCursor();
            shouldFocusRef.current = false;
        }
    }, [localContent, restoreCursor]);

    const handleApply = () => {
        if (!editorRef.current) return;
        saveCursorPosition();
        const html = editorRef.current.innerHTML;
        debouncedUpdate(html);
    };

    const insertElement = value => {
        setIsApplied(false);
        shouldFocusRef.current = true;
        const el = editorRef.current;
        if (!el) return;
        el.focus();

        const sel = window.getSelection();
        let range = lastCursorPositionRef.current
            ? lastCursorPositionRef.current.cloneRange()
            : sel.rangeCount
                ? sel.getRangeAt(0).cloneRange()
                : null;

        if (!range) {
            range = document.createRange();
            range.selectNodeContents(el);
            range.collapse(false);
        }

        range.deleteContents();
        const span = document.createElement('span');
        span.contentEditable = 'false';
        span.className = 'element_tags inline-block bg-indigo-100 text-indigo-700 px-1 rounded text-sm font-medium';
        span.textContent = `{{${value}}}`;
        range.insertNode(span);

        range.setStartAfter(span);
        range.collapse(true);
        sel.removeAllRanges();
        sel.addRange(range);

        saveCursorPosition();
        const newContent = el.innerHTML;
        setLocalContent(newContent);
    };

    const resetContent = () => {
        setIsApplied(true);
        shouldFocusRef.current = true;
        if (editorRef.current) {
            editorRef.current.innerHTML = '';
            setLocalContent('');
            onSetContent('');
            onChange(null);
        }
    };

    // Initial focus setup
    useEffect(() => {
        return () => clearTimeout(updateTimeoutRef.current);
    }, []);

    return (
        <div className="space-y-2 bg-white p-2 rounded-lg shadow-sm border border-gray-200">
            <input type="hidden" name="short_code_hidden_field" value={btoa(localContent || '')} />

            <div className="relative">
                <div
                    ref={editorRef}
                    contentEditable
                    suppressContentEditableWarning
                    className="w-full min-h-[80px] max-h-[100px] overflow-y-auto p-2 border border-gray-200 rounded-md bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                    onInput={handleEdit}
                    onBlur={saveCursorPosition}
                    onFocus={restoreCursor}
                    dangerouslySetInnerHTML={{ __html: localContent }}
                    data-placeholder="Enter your content..."
                />
                <style>{`
          [data-placeholder]:empty:before {
            content: attr(data-placeholder);
            color: #9ca3af;
          }
        `}</style>
            </div>

            <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                    <button
                        onClick={resetContent}
                        className="flex items-center gap-1 text-gray-600 hover:text-red-500 text-xs"
                        title="Clear"
                    >
                        <RefreshCcw className="w-3 h-3" /> Clear
                    </button>
                    {!isApplied && (
                        <button
                            onClick={handleApply}
                            className="px-3 py-1 text-xs rounded bg-blue-600 text-white hover:bg-blue-700 shadow"
                        >
                            Apply
                        </button>
                    )}
                </div>

                <div className="relative">
                    <button
                        type="button"
                        onClick={() => setDropdownOpen(o => !o)}
                        className="flex items-center gap-1 px-2 py-1 text-xs bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
                    >
                        Insert <ChevronDown className="w-3 h-3" />
                    </button>
                    {dropdownOpen && (
                        <div className="absolute right-0 z-10 mt-1 w-56 bg-white border border-gray-200 rounded-md shadow-lg max-h-48 overflow-y-auto">
                            {nodes.map((group, idx) => (
                                <div key={idx} className="border-b last:border-0">
                                    <div className="px-2 py-1 text-xs font-semibold text-gray-500 uppercase">
                                        {group.title || group.label}
                                    </div>
                                    {group.options.map(opt => (
                                        <button
                                            key={opt.value}
                                            onClick={() => {
                                                insertElement(opt.value);
                                                setDropdownOpen(false);
                                            }}
                                            className="w-full text-left px-2 py-1 text-xs hover:bg-gray-100"
                                        >
                                            {opt.label}
                                        </button>
                                    ))}
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
