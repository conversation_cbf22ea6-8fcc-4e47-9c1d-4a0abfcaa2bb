import React from "react";
import { Select } from "antd";

const SelectAttribute = ({ options, onChange, value }) => {
  // Filter function to search by label
  const filterOptionByLabel = (input, option) => {
    return (option?.label ?? '').toLowerCase().includes(input.toLowerCase());
  };

  return (
    <Select
      allowClear
      placeholder="Select Attribute"
      showSearch
      style={{
        width: 180,
      }}
      filterOption={filterOptionByLabel}
      options={options}
      value={value}
      onChange={onChange}
    />
  );
};

export default SelectAttribute;
