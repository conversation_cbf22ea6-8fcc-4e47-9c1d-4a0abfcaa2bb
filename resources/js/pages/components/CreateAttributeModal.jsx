import React, { useState, useEffect, useRef } from "react";
import { Modal, Form, Input, Select, Button, message, Spin, Dropdown } from "antd";
import axios from "axios";
import "../../../css/attribute-modal.css";

const CreateAttributeModal = ({
  isVisible,
  onClose,
  rowId,
  allFamilies = [],
  allAttributes = [],
  onAttributeCreated,
  dataRequired,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [filteredFamilies, setFilteredFamilies] = useState([]);
  const [familyInputValue, setFamilyInputValue] = useState("");
  const [isFamilyListVisible, setFamilyListVisible] = useState(false);
  const familyInputRef = useRef(null);

  // Reset form and states when modal becomes visible
  useEffect(() => {
    if (isVisible) {
      form.resetFields();
      setFamilyInputValue("");
      setFamilyListVisible(false);
      setFilteredFamilies(allFamilies);
    }
  }, [isVisible, form, allFamilies]);

  // Initialize filtered families when allFamilies changes
  useEffect(() => {
    setFilteredFamilies(allFamilies);
  }, [allFamilies]);

  // Sync form value with input value
  useEffect(() => {
    if (familyInputValue) {
      form.setFieldsValue({ attributeFamily: familyInputValue });
    }
  }, [familyInputValue, form]);

  // Handle input change in the attribute family field
  const handleFamilyInputChange = (e) => {
    const value = e.target.value;
    setFamilyInputValue(value);
    form.setFieldsValue({ attributeFamily: value });

    // Filter families based on input
    if (value) {
      const filtered = allFamilies.filter((family) =>
        family.name.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredFamilies(filtered);
    } else {
      setFilteredFamilies(allFamilies);
    }

    // Show the dropdown when typing
    setFamilyListVisible(true);
  };

  // Handle focus on the family input field
  const handleFamilyInputFocus = () => {
    setFamilyListVisible(true);
  };

  // Handle clicking on a family item in the dropdown
  const handleFamilyItemClick = (family) => {
    const familyName = family.name;
    setFamilyInputValue(familyName);
    form.setFieldsValue({ attributeFamily: familyName });
    setFamilyListVisible(false);
  };

  // Handle click outside to close the dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (familyInputRef.current && !familyInputRef.current.contains(event.target)) {
        setFamilyListVisible(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Reset form when modal closes
  const handleCancel = () => {
    form.resetFields();
    setFamilyInputValue("");
    setFamilyListVisible(false);
    onClose();
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // Prepare form data for API request
      const formData = {
        organization_id: dataRequired.organization_id,
        method_type: dataRequired.template_method_type,
        attribute_family_name: values.attributeFamily,
        name: values.attributeName,
        attribute_type_id: values.attributeType,
      };

      // Get CSRF token
      const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute("content");

      // Set up axios headers
      axios.defaults.headers.common["X-CSRF-TOKEN"] = csrfToken;

      // Send request to create attribute
      const response = await axios.post("/products/attributes", formData);

      if (response.data && response.data.data) {
        message.success("Attribute created successfully!");

        // Convert attribute name to handle format (lowercase, replace spaces with underscores)
        const attributeHandle = values.attributeName
          .toLowerCase()
          .replace(/[{()}[\]<>]/g, "")
          .replace(/ /g, "_");

        // Create the full attribute value (family,handle)
        const fullAttributeValue = `${values.attributeFamily},${attributeHandle}`;

        // Add the attribute name to the response data for display in the dropdown
        const updatedResponseData = {
          ...response.data.data,
          new_attribute_name: values.attributeName
        };

        // Call the callback with the updated data and new attribute
        onAttributeCreated(updatedResponseData, rowId, fullAttributeValue);

        // Reset all states
        form.resetFields();
        setFamilyInputValue("");
        setFamilyListVisible(false);
        onClose();
      }
    } catch (error) {
      if (error.response && error.response.status === 404) {
        message.error("Attribute already exists.");
      } else if (error.response && error.response.data && error.response.data.message) {
        message.error(error.response.data.message);
      } else if (!error.isAxiosError) {
        // This is likely a form validation error
        console.log("Validation error:", error);
      } else {
        message.error("An unexpected error occurred.");
        console.error(error);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="Create Apimio Attribute"
      open={isVisible}
      onCancel={handleCancel}
      footer={null}
      maskClosable={false}
      width={500}
      centered
      destroyOnClose={true}
    >
      <Spin spinning={loading}>
        <Form
          form={form}
          layout="vertical"
          name="createAttributeForm"
          initialValues={{ attributeType: "1" }}
        >
          <Form.Item
            name="attributeFamily"
            label="Attribute Family"
            rules={[{ required: true, message: "Please enter attribute family name" }]}
          >
            <div className="attribute-family-input" ref={familyInputRef}>
              <Input
                placeholder="Enter attribute family name"
                value={familyInputValue}
                onChange={handleFamilyInputChange}
                onFocus={handleFamilyInputFocus}
              />
              {isFamilyListVisible && filteredFamilies.length > 0 && (
                <div className="family-dropdown">
                  <ul>
                    {filteredFamilies.map((family) => (
                      <li
                        key={family.id}
                        className="family-dropdown-item"
                        onClick={() => handleFamilyItemClick(family)}
                      >
                        {family.name}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </Form.Item>

          <Form.Item
            name="attributeName"
            label="Attribute Name"
            rules={[{ required: true, message: "Please enter attribute name" }]}
          >
            <Input placeholder="Enter attribute name" />
          </Form.Item>

          <Form.Item
            name="attributeType"
            label="Attribute Type"
            rules={[{ required: true, message: "Please select attribute type" }]}
          >
            <Select placeholder="Select attribute type">
              {allAttributes
                .filter((attr) => attr.id === 1 || attr.id === 3)
                .map((attr) => (
                  <Select.Option key={attr.id} value={attr.id.toString()}>
                    {attr.name}
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>

          <div className="flex justify-end gap-2 mt-4">
            <Button onClick={handleCancel}>Cancel</Button>
            <Button type="primary" onClick={handleSubmit} loading={loading}>
              Create
            </Button>
          </div>
        </Form>
      </Spin>
    </Modal>
  );
};

export default CreateAttributeModal;
