import { useState, useEffect } from "react";
import axios from "axios";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

export default function Step1({ setVariantOptions, variantOptions, versionId, prodId, refreshVariants }) {
    const [showFirstTable, setShowFirstTable] = useState(true);
    const [selectedAttributes, setSelectedAttributes] = useState([]);
    const [selectAll, setSelectAll] = useState(false);
    const [selectedOptionsInSecondTable, setSelectedOptionsInSecondTable] = useState({});

    useEffect(() => {
        axios
            .get(`/api/product/attributes/variants/options/${prodId}/${versionId}`)
            .then((response) => {
                const fetchedData = response.data.data;

                // Initialize `selectedOptionsInSecondTable` based on `is_assigned` and `is_selected`
                const initialOptionsSetup = fetchedData.reduce((acc, attribute) => {
                    if (attribute.is_assigned) {
                        acc[attribute.name] = attribute.attribute_options.reduce((optionsAcc, option) => {
                            optionsAcc[option.name] = option.is_selected || false;
                            return optionsAcc;
                        }, {});
                    }
                    return acc;
                }, {});

                setVariantOptions(fetchedData);
                setSelectedOptionsInSecondTable(initialOptionsSetup);
            })
            .catch((error) => console.log(error));
    }, [prodId, versionId]); // Ensure useEffect is correctly dependent on external props

    const handleNextClick = () => {
        setShowFirstTable(false);

        // IDs of options that are disabled (is_assigned === true)
        const disabledOptionIds = variantOptions.filter((option) => option.is_assigned === true).map((option) => option.id); // Assuming IDs as strings for consistency

        // Update selected attributes to include both user-selected and disabled option IDs
        // Ensuring no duplicates
        setSelectedAttributes((prevSelected) => {
            const uniqueSelected = [...new Set([...prevSelected, ...disabledOptionIds])];
            return uniqueSelected;
        });

        // Since setSelectedAttributes is asynchronous, you need to ensure state is updated before calling prepareVariantOptions.
        // However, directly using state right after setting it won't reflect the update due to its asynchronous nature.
        // A workaround is to use the merged list directly if necessary immediately.
        const mergedSelectedForImmediateUse = [...new Set([...selectedAttributes, ...disabledOptionIds])];
        prepareVariantOptions(variantOptions, mergedSelectedForImmediateUse);
    };


    const handleCloseClick = () => {
        setShowFirstTable(true);
    };
    const handleConfirmClick = () => {
        setShowFirstTable(true);
        submitSelectedOptions();
        const modalElement = document.getElementById("exampleModal");
        const modalInstance = bootstrap.Modal.getInstance(modalElement);
        modalInstance.hide();
        // axios.defaults.headers.common["X-CSRF-TOKEN"] = document.querySelector('meta[name="csrf-token"]').getAttribute("content");

        // axios.POST(`/api/product/versions/variants/create`)
        //     .then(response => {
        //         // Update the state with the fetched data
        //         setVariantOptions(response.data.data);
        //         // console.log(variantOptions);

        //     })
        //     .catch(error => console.log(error));
    };

    const prepareVariantOptions = (variantOptions, selectedAttributes) => {
        const optionsByAttribute = {};
        variantOptions.forEach((option) => {
            if (selectedAttributes.includes(option.id)) {
                // Filter based on selected attributes
                if (!optionsByAttribute[option.name]) {
                    optionsByAttribute[option.name] = {
                        name: option.name,
                        options: option.attribute_options || [],
                    };
                }
            }
        });

        return optionsByAttribute;
    };

    const optionsByAttribute = prepareVariantOptions(variantOptions, selectedAttributes);
    //  console.log(selectedAttributes);

    const handleSelectAllChange = (e) => {
        setSelectAll(e.target.checked);
        if (e.target.checked) {
            // Select all attributes
            const allAttributeIds = variantOptions.map((option) => option.id); // Consistency in type
            setSelectedAttributes(allAttributeIds);
        } else {
            // Deselect all
            setSelectedAttributes([]);
        }
    };

    const handleCheckboxChange = (id) => {
        setSelectedAttributes((prevSelected) => {
            const idStr = id; // Ensure ID is a string for consistent comparison
            const isSelected = prevSelected.includes(idStr);
            if (isSelected) {
                // Remove the id if it's already selected
                return prevSelected.filter((attrId) => attrId !== idStr);
            } else {
                // Add the id if it's not already selected
                return [...prevSelected, idStr];
            }
        });
    };

    useEffect(() => {
        const allOptionIds = variantOptions.map((option) => option.id);
        // console.log(allOptionIds, "aasassasas", selectedAttributes);
        const allSelected = allOptionIds.length > 0 && allOptionIds.every((id) => selectedAttributes.includes(id));

        setSelectAll(allSelected);
    }, [selectedAttributes, variantOptions]);

    const updateOptionsForSecondTable = (selectedAttributeIdStr) => {
        const selectedAttribute = variantOptions.find((option) => option.id.toString() === selectedAttributeIdStr);

        if (selectedAttribute && !selectedOptionsInSecondTable[selectedAttribute.name]) {
            const optionsStructure = selectedAttribute.attribute_options.reduce((acc, option) => {
                // Assume options not previously selected; adjust based on your need
                acc[option.name] = option.is_selected || false; // Respect initial `is_selected` status
                return acc;
            }, {});

            setSelectedOptionsInSecondTable((prevState) => ({
                ...prevState,
                [selectedAttribute.name]: optionsStructure,
            }));
        }
    };

    const handleSecondTableCheckboxChange = (attributeName, optionName) => {
        setSelectedOptionsInSecondTable((prev) => ({
            ...prev,
            [attributeName]: {
                ...prev[attributeName],
                [optionName]: !(prev[attributeName]?.[optionName] || false),
            },
        }));
    };

    const prepareDataForSubmission = () => {
        const organizationId = variantOptions.length > 0 ? variantOptions[0].organization_id : null;

        const submissionData = {
            product_id: prodId.toString(),
            version_id: versionId,
            organization_id: organizationId ? organizationId.toString() : "",
            attributes: [],
        };

        // Ensure selectedAttributes are treated as strings for consistent comparison
        const selectedAttributesStr = selectedAttributes.map((attr) => attr.toString());

        variantOptions.forEach((attribute) => {
            // Convert attribute.id to string for consistent comparison
            const attributeIdStr = attribute.id.toString();

            if (selectedAttributesStr.includes(attributeIdStr)) {
                const attributeOptions = selectedOptionsInSecondTable[attribute.name] || {};
                const selectedOptions = Object.entries(attributeOptions)
                    .filter(([_, isSelected]) => isSelected)
                    .map(([optionName, _]) => optionName);

                if (selectedOptions.length > 0) {
                    submissionData.attributes.push({
                        id: attributeIdStr,
                        name: attribute.name,
                        options: selectedOptions,
                    });
                }
            }
        });

        return submissionData;
    };

   const submitSelectedOptions = async () => {
       const dataToSubmit = prepareDataForSubmission();

       // Check if any attribute has less than two options selected
       const isSelectionValid = dataToSubmit.attributes.every((attribute) => attribute.options.length >= 2);

       if (!isSelectionValid) {
           // If not valid, show a toast and exit the function
           toast.warn("At least select two options for each attribute!");
           return; // Stop execution if the condition is not met
       }

       try {
           const response = await axios.post("/api/product/versions/variants/create", dataToSubmit);
           console.log("success", refreshVariants); // Add this line to debug
           // window.location.reload();
           console.log(dataToSubmit);
           // refreshVariants();
           toast.success("Variants created successfully!");
           window.location.reload();
       } catch (error) {
           console.log("failed", refreshVariants); // Add this line to debug
           // refreshVariants();
            //  window.location.reload();
           console.log("data", dataToSubmit);
           toast.error("Submission failed!");
           window.location.reload();
       }
   };


    // useEffect(() => {
    //     // Extract IDs of disabled options
    //     const disabledOptionIds = variantOptions.filter((option) => option.is_assigned).map((option) => option.id); // Assuming IDs are strings for consistency

    //     // Merge with existing selected attributes, avoiding duplicates
    //     // This is important if selectedAttributes can also be modified elsewhere (e.g., through user interaction)
    //     console.log("usestattte", disabledOptionIds);
    //     setSelectedAttributes((prevSelected) => {
    //         const mergedSelected = [...new Set([...prevSelected, ...disabledOptionIds])];
    //         return mergedSelected;
    //     });
    // }, [variantOptions]); // Re-run this effect if variantOptions changes

    const doesExistInSelectedAttributes = (selectedAttributes, optionId) => {
        return selectedAttributes.some((attr) => attr === optionId);
    };

    return (
        <div className="modal fade " id="exampleModal" tabIndex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div className="modal-dialog">
                <div className="modal-content">
                    {showFirstTable ? (
                        <>
                            <form>
                                <div className="modal-header">
                                    <h4 className="modal-title" id="exampleModalLabel">
                                        Variant option
                                    </h4>
                                    <button
                                        type="button"
                                        className="btn-close"
                                        data-bs-dismiss="modal"
                                        aria-label="Close"
                                        onClick={handleCloseClick}
                                    ></button>
                                </div>

                                <div className="modal-body">
                                    <div>
                                        <h4>Step 02: ADD VARIANT VALUES</h4>
                                        <table className="table table-responsive">
                                            <thead>
                                                <tr>
                                                    <th className="d-flex align-items-center gap-1">
                                                        <input type="checkbox" checked={selectAll} onChange={handleSelectAllChange} />
                                                        Attribute Name
                                                    </th>
                                                    <th>Type</th>
                                                    <th>Group</th>
                                                    <th>By Default</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {variantOptions.map((option) => (
                                                    <tr key={option.id}>
                                                        <td className="d-flex align-items-center gap-1" id={option.id}>
                                                            {/* {console.log(
                                                                selectedAttributes,
                                                                option.id,
                                                                doesExistInSelectedAttributes(selectedAttributes, option.id)
                                                            )} */}
                                                            {!option.is_assigned ? (
                                                                <input
                                                                    type="checkbox"
                                                                    {...(doesExistInSelectedAttributes(selectedAttributes, option.id)
                                                                        ? { checked: "checked" }
                                                                        : {})}
                                                                    onChange={() => handleCheckboxChange(option.id)}
                                                                    name={option.name}
                                                                />
                                                            ) : (
                                                                <input type="checkbox" checked="checked" disabled name={option.name} />
                                                            )}

                                                            {option.name}
                                                        </td>
                                                        <td>Multi Selection</td>
                                                        <td>N/A</td>
                                                        <td>{option.is_default ? "Yes" : "No"}</td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div className="modal-footer ">
                                    <div className="d-flex gap-3 p-2">
                                        <button type="button" className="btn btn-outline-primary" data-bs-dismiss="modal">
                                            Close
                                        </button>
                                        <button type="button" className="btn btn-primary" onClick={handleNextClick}>
                                            Next
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </>
                    ) : (
                        <>
                            <form action="">
                                <div className="modal-header">
                                    <h4 className="modal-title" id="exampleModalLabel">
                                        Variant option
                                    </h4>
                                    <button
                                        type="button"
                                        className="btn-close"
                                        data-bs-dismiss="modal"
                                        aria-label="Close"
                                        onClick={handleCloseClick}
                                    ></button>
                                </div>

                                <div className="modal-body">
                                    <div>
                                        <h4>Step 03: PREVIEW SUMMARY</h4>
                                        {Object.keys(optionsByAttribute).map((attributeName) => (
                                            <div key={attributeName}>
                                                <div className="bg-dark">
                                                    <h4 className="text-white p-2">{attributeName}</h4>
                                                </div>
                                                <div className="d-flex w-100 justify-evenly px-2 row">
                                                    {optionsByAttribute[attributeName].options.map((option, index) => (
                                                        <label
                                                            key={index}
                                                            className="d-flex align-items-center gap-1 col-4 justify-content-start"
                                                        >
                                                            {option.is_selected == false ? (
                                                                <input
                                                                    type="checkbox"
                                                                    checked={
                                                                        selectedOptionsInSecondTable[attributeName]?.[option.name] || false
                                                                    }
                                                                    onChange={() =>
                                                                        handleSecondTableCheckboxChange(attributeName, option.name)
                                                                    }
                                                                    name={option.name}
                                                                />
                                                            ) : (
                                                                <input type="checkbox" checked="checked" disabled name={option.name} />
                                                            )}

                                                            {option.name}
                                                        </label>
                                                    ))}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                                <div className="modal-footer ">
                                    <div className="d-flex gap-3 p-2">
                                        <button
                                            type="button"
                                            className="btn btn-outline-primary"
                                            data-bs-dismiss="modal"
                                            onClick={handleCloseClick}
                                        >
                                            Close
                                        </button>
                                        <button
                                            type="button"
                                            className="btn btn-primary"
                                            onClick={handleConfirmClick}
                                            aria-label="Close"
                                            disabled={
                                                !Object.values(selectedOptionsInSecondTable).some((attribute) =>
                                                    Object.values(attribute).some((isChecked) => isChecked)
                                                )
                                            }
                                        >
                                            Confirm
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
}
