import React, { useState } from "react";

const Pagination = ({ dataRequired, totalPageCount, onPageChange, changedAttributes }) => {
    const [currentPage, setCurrentPage] = useState(1);
    const [showConfirmationModal, setShowConfirmationModal] = useState(false);
    const [pageNumber, setPageNumber] = useState();
    const goToPage = (pageNumber) => {
        setShowConfirmationModal(true);
        setPageNumber(pageNumber);
    };
    const handleSaveChanges = () => {
        const updatedDataRequired = {
            ...dataRequired,
            target_page: pageNumber,
        };
        setShowConfirmationModal(false);
        axios.defaults.headers.common["X-CSRF-TOKEN"] = document.querySelector('meta[name="csrf-token"]').getAttribute("content");
        axios
            .post("/products/bulk/edit/save", {
                data_required: updatedDataRequired, // Sending the updated data_required object
            })
            .then((response) => {
                const paginatedData = _.cloneDeep(response.data);
                onPageChange(pageNumber, paginatedData);
                setCurrentPage(pageNumber);
            })
            .catch((error) => console.log(error));
    };
    const handleDiscardChanges = () => {
        const updatedDataRequired = {
            ...dataRequired,
            target_page: pageNumber,
        };

        axios.defaults.headers.common["X-CSRF-TOKEN"] = document.querySelector('meta[name="csrf-token"]').getAttribute("content");
        axios
            .post("/products/bulk/edit/save", {
                data_required: updatedDataRequired, // Sending the updated data_required object
            })
            .then((response) => {
                const paginatedData = _.cloneDeep(response.data);
                onPageChange(pageNumber, paginatedData);
                setCurrentPage(pageNumber);
            })
            .catch((error) => console.log(error));
        setShowConfirmationModal(false);
    };
    const renderPageNumbers = () => {
        const pageNumbers = [];
        for (let i = 1; i <= totalPageCount; i++) {
            pageNumbers.push(
                <button
                    key={i}
                    className={`rounded-full py-1 px-2 mr-2 border border-[#2C4BFF] ${
                        currentPage === i ? "active bg-[#2C4BFF] text-white" : ""
                    }`}
                    onClick={() => goToPage(i)}
                >
                    {i}
                </button>
            );
        }
        return pageNumbers;
    };

    return (
        <div className="pagination border rounded-xl py-1 ">
            {renderPageNumbers()}
            {showConfirmationModal && (
                <div className="fixed w-full h-full bg-[rgba(0,0,0,0.7)] left-0 top-0 h-full flex items-center confirmation-modal">
                    <div className="w-[400px] mx-auto bg-white rounded-md text-center p-8 border border-[#ccc] shadow-xl relative">
                        <div className="absolute top-0 right-0 p-2 cursor-pointer" onClick={() => setShowConfirmationModal(false)}>
                            <i className="fa fa-times"></i>
                        </div>
                        <p>Do you want to save changes before moving to another page?</p>
                        <div className="flex justify-between mt-4">
                            <button
                                className="bg-[#2C4BFF] border-solid border-1 border-[#2C4BFF] h-8 w-45 py-2 px-4 text-md font-semibold rounded-md text-white"
                                onClick={handleSaveChanges}
                            >
                                Save Changes
                            </button>
                            <button
                                className=" border-solid border-1 border-[#2C4BFF] h-8 w-45 py-2 px-4 text-md font-semibold rounded-md text-[#2C4BFF]"
                                onClick={handleDiscardChanges}
                            >
                                Discard Changes
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Pagination;
