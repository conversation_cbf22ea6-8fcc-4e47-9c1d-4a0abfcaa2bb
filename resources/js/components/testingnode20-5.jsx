
import ReactDOM from 'react-dom/client';

export default function TestNode() {
    return (
        <>
            <h1>Hello World</h1>
            <p>this is the node version 20.5 for vite testing</p>
            <button className=" bg-[#2C4BFF] border-solid border-1 border-[#2C4BFF] h-8 w-45 py-2 px-4 text-md font-semibold rounded-md text-white">Save</button>
        </>
    );
}
const rootElement = document.getElementById('node-version20-5');

ReactDOM.createRoot(rootElement).render(
    <TestNode  />
);