import React, { useState, useEffect } from "react";

function MappingFormula({ index, selectedFormula, onFormulaChange }) {
  const [formulaValues, setFormulaValues] = useState(selectedFormula);
  useEffect(() => {
    setFormulaValues(selectedFormula); // Update the formulaValues if selectedFormula changes
  }, [selectedFormula]);
  const handleFormulaChange = (e) => {
    const newFormula = e.target.value;
    setFormulaValues(newFormula);

    onFormulaChange(index, newFormula);
  };
  const formulaOptions = [
    { value: "assign", label: "Assign" },
    { value: "split", label: "Split" },
    { value: "merge", label: "Merge (Basic)" },
    { value: "short_code", label: "Merge (Advance)" },
    { value: "replace", label: "Replace" },
    { value: "slug", label: "Slug" },
    { value: "vlookup", label: "Vlookup" },
    { value: "calculate", label: "Calculate" },
    { value: "expand", label: "Expand" },
  ];

  return (
    <div className="sm:w-3/12 w-6/12 formula">
      <div className="flex flex-wrap align-items-center h-full">
        <div className="w-8/12">
          <label className="block">Formula</label>
          <select
            name={`nodes[data][${index}][with_formula]`}
            className="formula_field w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent"
            id={`formula-${index}`} // Unique ID for each select element
            value={formulaValues[index]}
            onChange={handleFormulaChange}
          >
            {formulaOptions.map((option, optionIndex) => (
              <option
                key={optionIndex}
                value={option.value}
                className="Poppins regular text-color"
              >
                {option.label}
              </option>
            ))}
          </select>
        </div>
        <div className="w-2/12 mx-auto mt-8">
          <i className="icon-arrow-right"></i>
        </div>
      </div>
    </div>
  );
}

export default MappingFormula;
