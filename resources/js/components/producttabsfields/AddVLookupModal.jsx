import React, { useState, useEffect } from "react";
import axios from "axios";

import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
function AddVLookupModal({ handleCloseVlookupModal }) {
  const [vLookupData, setVLookupData] = useState([]);
  const [vlookupName, setVlookupName] = useState("");
  const [vlookupOptions, setVlookupOptions] = useState("");

  const handleNewVLookupClick = () => {
    $(".vlookup_error").html("");
    $(".updatelookup").addClass("hidden");
    $(".newvlookup").removeClass("hidden");
    $(".default-vlookup").addClass("hidden");
  };
  const handlesuccesvlookup = () => {
    $(".vlookup_success").addClass("hidden");
    $(".default-vlookup").removeClass("hidden");
  };
  const handleclosebutton = () => {
    handleCloseVlookupModal();
    $("#vlookupModal").removeClass("block");
  };
  const handlebackbutton = () => {
    $(".updatelookup").addClass("hidden");
    $(".newvlookup").addClass("hidden");
    $(".default-vlookup").removeClass("hidden");
  };
  const handleUpdateVLookupClick = async () => {
    $(".vlookup_error_update").html("");
    $(".default-vlookup").addClass("hidden");
    $(".newvlookup").addClass("hidden");
    $(".updatelookup").removeClass("hidden");
    try {
      const response = await axios.post("/vlookup/fetch");
      const data = response.data.data;
      setVLookupData(data);

      // Your success handling code here...
    } catch (error) {
      console.log(error);
    }
  };
  const handleNameChange = (event) => {
    const selectedName = event.target.value;

    setVlookupName(selectedName);

    // Find the corresponding values based on the selected name
    const selectedData = vLookupData.find((item) => item.name === selectedName);
    if (selectedData) {
      setVlookupOptions(selectedData.values);
    } else {
      setVlookupOptions("");
    }
  };
  const handleUpdateVLookup = async (event) => {
    event.preventDefault();
    // If an option is selected, get its ID
    var selectedOption = $(".vlookup_name_update").find(":selected");

    // Get the id attribute of the selected option
    const selectedId = selectedOption.attr("id");
    const selectedName = selectedOption.val();

    // Create FormData with the selected ID
    const formData = new FormData(event.target);
    formData.set("id", selectedId);
    formData.set("name", selectedName);
    formData.set("submit_type", "edit");

    try {
      const response = await axios.post("/vlookup", formData);

      // Handle success response
      const data = response.data;
      // Your success handling code here...
      toast.success(response.data.message);
      $(".vlookup_success").removeClass("hidden");
      $(".default-vlookup").addClass("hidden");
      $(".newvlookup").addClass("hidden");
      $(".updatelookup").addClass("hidden");
      handleCloseVlookupModal();
    } catch (error) {
      if (error.response && error.response.status === 500) {
        toast.error("Invalid Format.");
      } else {
        toast.error("An unexpected error occurred.");
        console.log(error);
      }
      console.log(error);
      const resp = error.response.data.data;
      Object.entries(resp).forEach(function (item, index) {
        $('.vlookup_error_update[data-name="' + item[0] + '"]').html(
          item[1][0]
        );
      });
      //handleCloseVlookupModal();
    }
  };
  const handleAddNewVLookup = async (event) => {
    event.preventDefault();
    $(".vlookup_error").html("");
    const formData = new FormData(event.target);
    formData.append("submit_type", "add");

    try {
      const response = await axios.post("/vlookup", formData);

      // Handle success response
      const data = response.data;
      toast.success(response.data.message);
      $(".vlookup_success").removeClass("hidden");
      $(".default-vlookup").addClass("hidden");
      $(".newvlookup").addClass("hidden");
      $(".updatelookup").addClass("hidden");
      $("#vlookup_form").trigger("reset");
      handleCloseVlookupModal();
      // Your success handling code here...
    } catch (error) {
      if (error.response && error.response.status === 500) {
        toast.error("Invalid Format.");
      } else {
        toast.error("An unexpected error occurred.");
        console.log(error);
      }
      // toast.error(error.response.data.message);
      const resp = error.response.data.data;
      Object.entries(resp).forEach(function (item, index) {
        $('.vlookup_error[data-name="' + item[0] + '"]').html(item[1][0]);
      });
      //handleCloseVlookupModal();
    }
  };
  return (
    <div
      className="fixed w-full h-screen top-0 left-0 z-20 bg-[#303030ab]"
      id="vlookupModal"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabIndex="-1"
      aria-labelledby="exampleModalLabel"
      aria-modal="true"
      role="dialog"
    >
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="default-vlookup">
            <div className="modal-header">
              <h3 className="modal-title" id="exampleModalLabel">
                V-Lookup
              </h3>
              <button
                type="button"
                className="btn-sm border-0 bg-white fs-24 px-0 pt-0 close-import-modal"
                data-bs-dismiss="modal"
                aria-label="Close"
                onClick={handleclosebutton}
              >
                <span aria-hidden="true">×</span>
              </button>
            </div>
            <div className="modal-body">
              <div
                className="inline-flex w-full justify-evenly relative"
                id="vlook-options"
              >
                <button
                  className="btn btn-primary "
                  onClick={handleNewVLookupClick}
                >
                  Create New Vlookup
                </button>

                <button
                  type="button"
                  className="btn btn-outline-primary"
                  onClick={handleUpdateVLookupClick}
                >
                  Update Existing Vlookup
                </button>
              </div>
            </div>
          </div>
          <form id="vlookup_form" onSubmit={handleAddNewVLookup}>
            <div className="form-group newvlookup hidden">
              <div className="modal-header">
                <button
                  type="button"
                  className="btn-sm border-0 bg-white fs-24 px-0 pt-0 close-import-modal"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                >
                  <i
                    className="icon-arrow-left fs-20 "
                    onClick={handlebackbutton}
                  ></i>
                </button>
                <h3 className="modal-title text-center" id="exampleModalLabel">
                  Create New V-lookup
                </h3>
                <button
                  type="button"
                  className="btn-sm border-0 bg-white fs-24 px-0 pt-0 close-import-modal"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                  onClick={handleclosebutton}
                >
                  <span aria-hidden="true">×</span>
                </button>
              </div>
              <div className="modal-body">
                <div className="form-group">
                  <label htmlFor="vlookup_name mb-2">Name</label>
                  <input
                    type="text"
                    className="form-control bg-white-smoke placeholder-gray-400"
                    id="vlookup_name"
                    name="name"
                    placeholder="Enter Vlookup Name"
                    onChange={(e) => setVlookupName(e.target.value)}
                  />
                  <span
                    className="text-danger text-sm vlookup_error"
                    data-name="name"
                  ></span>
                </div>
                <div className="form-group mt-4">
                  <label htmlFor="vlookup_options mb-2">Values</label>
                  <textarea
                    type="text"
                    className="form-control bg-white-smoke h-32 placeholder-gray-400"
                    id="vlookup_options"
                    name="values"
                    placeholder="blk,Black&#10;ylw,Yellow&#10;grn,Green&#10;etc..."
                    onChange={(e) => setVlookupOptions(e.target.value)}
                  ></textarea>
                  <span
                    className="text-danger text-sm vlookup_error"
                    data-name="values"
                  ></span>
                </div>
              </div>
              <div
                className="modal-footer justify-start p-2 form-group relative"
                id="vlook-options"
              >
                <button
                  id="pro_imp_start_btn"
                  name="import_csv"
                  className="btn btn-primary"
                >
                  Create
                </button>
              </div>
            </div>
          </form>
          <form id="vlookup_form_update" onSubmit={handleUpdateVLookup}>
            <div className="form-group updatelookup hidden">
              <div className="modal-header">
                <button
                  type="button"
                  className="btn-sm border-0 bg-white fs-24 px-0 pt-0 close-import-modal"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                >
                  <i
                    className="icon-arrow-left fs-20 "
                    onClick={handlebackbutton}
                  ></i>
                </button>
                <h3 className="modal-title text-center" id="exampleModalLabel">
                  Update V-lookup
                </h3>
                <button
                  type="button"
                  className="btn-sm border-0 bg-white fs-24 px-0 pt-0 close-import-modal"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                  onClick={handleclosebutton}
                >
                  <span aria-hidden="true">×</span>
                </button>
              </div>
              <div className="modal-body">
                <div className="form-group">
                  <label htmlFor="vlookup_name">Name</label>
                  <select
                    className="w-full p-2 border border-[#ced4da] vlookup_name_update bg-white-smoke mt-2"
                    onChange={handleNameChange}
                    value={vlookupName}
                    name="id"
                  >
                    <option value="">Choose</option>
                    {vLookupData.map((item) => (
                      <option key={item.id} value={item.name} id={item.id}>
                        {item.name}
                      </option>
                    ))}
                  </select>
                  <span
                    className="text-danger text-sm vlookup_error_update"
                    data-name="name"
                  ></span>
                </div>
                <div className="form-group mt-4">
                  <label htmlFor="vlookup_options">Values</label>
                  <textarea
                    type="text"
                    className="form-control bg-white-smoke placeholder-gray-400 mt-2 h-32"
                    id="vlookup_options"
                    value={vlookupOptions}
                    onChange={(e) => setVlookupOptions(e.target.value)}
                    name="values"
                    placeholder="blk,Black
ylw,Yellow
grn,Green
etc..."
                  ></textarea>
                  <span
                    className="text-danger text-sm vlookup_error_update"
                    data-name="values"
                  ></span>
                </div>
              </div>
              <div
                className="modal-footer justify-start p-2 form-group relative"
                id="vlook-options"
              >
                <button className="btn btn-primary float-start onclick-disabled-with-loader">
                  Update
                </button>
              </div>
            </div>
          </form>
          <div className="form-group vlookup_success hidden">
            <div className="justify-end modal-header">
              <button
                type="button"
                className="btn-sm border-0 bg-white fs-24 px-0 pt-0 close-import-modal"
                data-bs-dismiss="modal"
                aria-label="Close"
                onClick={handleclosebutton}
              >
                <span aria-hidden="true">×</span>
              </button>
            </div>
            <div className="modal-body">
              <img
                src="https://cdn.dribbble.com/users/2185205/screenshots/7886140/media/90211520c82920dcaf6aea7604aeb029.gif"
                className="mx-auto"
              />
            </div>
            <div className="modal-footer justify-center p-2 form-group relative">
              <button
                className="btn btn-primary float-start onclick-disabled-with-loader"
                onClick={handlesuccesvlookup}
              >
                Create Or Edit V-Lookup
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AddVLookupModal;
