import React, { useState, useEffect } from "react";

function MappingFrom({
  index,
  selectedValueFrom,
  onSelectedValueChange,
  inputArray,
}) {
  const input_array = data.input_array;
  const [selectedValue, setSelectedValue] = useState("");
  const defaultNodeName = Object.keys(input_array.nodes)[0]; // Fetch the node name dynamically

  const handleSelectChange = (e) => {
    const newValue = e.target.value;
    setSelectedValue(newValue);

    // Call the parent component's callback function with the updated value
    onSelectedValueChange(index, newValue);
  };
  useEffect(() => {
    if (data.data_required.template_method_type == "export") {
      function displayItemsWithIndex(inputArray) {
        let result = [];
        let currentIndex = 0;

        inputArray.nodes.forEach((familyNode, familyIndex) => {
          for (const attributeName in familyNode.attributes) {
            const attributeValue = familyNode.attributes[attributeName];
            result.push(attributeValue);
            currentIndex++;
          }
        });

        return result;
      }

      const newInputArray = displayItemsWithIndex(input_array);
      setSelectedValue(newInputArray[index]);
      console.log("selectedValue", selectedValue);
    } else {
      if (input_array.nodes.Default[index]) {
        const defaultNodeName = Object.keys(data.input_array.nodes)[0];
        const inputKey = input_array.nodes.Default[index];
        const familyInputValue = defaultNodeName + "," + inputKey;
        setSelectedValue(familyInputValue);
      } else {
        setSelectedValue(""); // No item at this index
      }
    }
  }, [index, inputArray.nodes]);
  return (
    <div className="sm:w-3/12 w-6/12 from">
      <div className="flex flex-wrap">
        <div className="w-8/12">
          <label className="block">
            {input_array.array_name || "Input"} Attribute
          </label>
          {data.data_required.template_method_type === "import" ? (
            <select
              className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-from-select"
              name={`nodes[data][${index}][from][]`}
              value={selectedValue}
              onChange={handleSelectChange}
            >
              <option value="" className="Poppins regular text-color">
                Select Field
              </option>

              <optgroup label={defaultNodeName}>
                {Object.keys(input_array.nodes.Default).map(
                  (inputKey, optionIndex) => {
                    return (
                      <option
                        value={
                          defaultNodeName +
                          "," +
                          input_array.nodes.Default[inputKey]
                        }
                      >
                        {input_array.nodes.Default[inputKey]}
                      </option>
                    );
                  }
                )}
              </optgroup>
            </select>
          ) : (
            <select
              className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent mapping-from-select"
              name={`nodes[data][${index}][from][]`}
              //value={selectedValue}
              onChange={handleSelectChange}
            >
              <option value="" className="Poppins regular text-color">
                Select Field
              </option>
              {input_array.nodes.map((familyNode, familyIndex) => (
                <optgroup label={familyNode.name} key={familyIndex}>
                  {Object.entries(familyNode.attributes).map(
                    ([attributeName, attributeValue], inputIndex) => {
                      if (attributeValue === selectedValue) {
                        console.log(
                          "selectedValue:",
                          selectedValue,
                          "attributeValue:",
                          attributeValue
                        );
                      }
                      return (
                        <option
                          value={`${familyNode.name},${attributeName}`}
                          selected={attributeValue === selectedValue}
                          key={inputIndex}
                        >
                          {attributeValue}
                        </option>
                      );
                    }
                  )}
                </optgroup>
              ))}
            </select>
          )}
        </div>
      </div>
    </div>
  );
}

export default MappingFrom;
