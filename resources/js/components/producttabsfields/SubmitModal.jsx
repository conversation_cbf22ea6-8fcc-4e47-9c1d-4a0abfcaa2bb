import React, { useState, useEffect } from "react";
import axios from "axios";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Select from "react-select";
import { set } from "lodash";
function SubmitModal({
  hideModal,
  data,
  submitData,
  tempName,
  setTempName,
  tempId,
  setTempId,
  selectedCatalog,
  setSelectedCatalog,
  onSaveTemplate,
  isSaveTemplate,
}) {
  const convertedData = submitData.map((item) => ({
    ...item,
    from: Array.isArray(item.from) ? item.from : [item.from], // Ensure 'from' is an array
    to: Array.isArray(item.to) ? item.to : [item.to], // Convert 'to' to an array
  }));
  const versions = data.data_required.versions || [];
  const [versionError, setVersionError] = useState("");
  const [storeError, setStoreError] = useState("");
  const [exportError, setExportError] = useState("");
  const [tempnameError, setTempnameError] = useState("");
  const catalogs = data.data_required.catalogs || [];
  const templatemethodtype = data.data_required.template_method_type || [];
  const options = Object.entries(catalogs).map(([key, value]) => ({
    value: key,
    label: value,
  }));
  var _token = $('meta[name="csrf-token"]').attr("content");
  const file_path = data.file_path;
  const template_method_type = templatemethodtype;
  const organization_id = data.data_required.organization_id.toString();

  const [saveTemplateButton, setSaveTemplateButton] = useState(0);
  const [submitButton, setSubmitButton] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState(
    data.template_attributes.version_id || ""
  );
  // const [selectedCatalog, setSelectedCatalog] = useState(null);
  const [selectedStore, setSelectedStore] = useState(
    data.template_attributes.channel_id || ""
  );
  // const [tempName, setTempName] = useState(data.template_attributes.name || "");
  // const [tempId, setTempId] = useState(data.template_attributes.id || "");
  const [exportType, setExportType] = useState(""); // State to store the input value
  const [saveTemplateName, setSaveTemplateName] = useState(false);
  const template_type = data.template_attributes.export_type || "";
  const [tempNameInput, SetTempNameInput] = useState(false);
  const handleVersionChange = (event) => {
    setSelectedVersion(event.target.value);
    if (version !== "") {
      setVersionError("");
    }
  };

  useEffect(() => {
    // If there's exactly one store and nothing is selected yet, auto-select it
    if (options.length === 1 && selectedCatalog.length === 0) {
      setSelectedCatalog([options[0]]);
    }
  }, [options, selectedCatalog]);
  useEffect(() => {
    if (isSaveTemplate) {
      setSaveTemplateName(true);
      setSaveTemplateButton(1);
      setSubmitButton(false);
    } else {
      if (data?.template_attributes?.template?.data?.length > 0) {
          if(data?.template_attributes?.id !== null) {
              if (data?.template_attributes?.id !== "") {
                  setSaveTemplateName(true);
              }
          }
      } else {
        setSaveTemplateName(false);
      }
      setSaveTemplateButton(0);
      setSubmitButton(true);
    }
  }, [isSaveTemplate]);
  useEffect(() => {
    if (data?.template_attributes?.template?.data?.length > 0) {
        if(data?.template_attributes?.id !== null) {
            if (data?.template_attributes?.id !== "") {
                SetTempNameInput(true);
            }
        }
    } else {
      SetTempNameInput(false);
    }
  }, [data]);
  const handleExportType = (event) => {
    setExportType(event.target.value);
  };
  const handleCatalogChange = (selectedCatalog) => {
    const newSelectedCatalogs = {};
    selectedCatalog.forEach((catalog, index) => {
      newSelectedCatalogs[index] = catalog.value;
    });
    setSelectedCatalog(selectedCatalog);
    if (selectedCatalog !== "") {
      setStoreError("");
      return;
    }
  };
  const handleCatalogSingleChange = (event) => {
    setSelectedStore(event.target.value);
  };
  const handleTempNameDisable = () => {
    SetTempNameInput(false);
  };

  const handleTempNameChange = (event) => {
    setTempName(event.target.value);
    if (event.target.value !== "") {
      setTempnameError("");
      return;
    }
  };
  $(document).on("click", "#edit_temp_name", function () {
    $(".template-name input").prop("readonly", false);
  });

  useEffect(() => {
    if (data.template_attributes.length == 0) {
    } else {
      const newSelectedCatalogs = {};

      if (Array.isArray(data.template_attributes.channel_id)) {
        // It's an array
        data.template_attributes.channel_id.forEach((channelId) => {
          // iterate through channel IDs
          const channelOption = Object.entries(
            data.data_required.catalogs
          ).find(([key]) => key === channelId); // find the channel option based on ID
          if (channelOption) {
            // if a channel option is found
            const [channelId, channelLabel] = channelOption; // destructure the channel option
            newSelectedCatalogs[channelId] = {
              // save the channel ID and label
              value: channelId,
              label: channelLabel,
            };
          }
        });
      } else {
        // It's a string
        const channelId = data.template_attributes.channel_id;
        const channelLabel = data.data_required.catalogs[channelId];
        if (channelLabel) {
          newSelectedCatalogs[channelId] = {
            value: channelId,
            label: channelLabel,
          };
        }
      }

      setSelectedCatalog(Object.values(newSelectedCatalogs));

      setTempId(data.template_attributes.id || "");
      if(data?.template_attributes?.id !== null){
          if(data?.template_attributes?.id !== "") {
              setTempName(data.template_attributes.name || "");
          }
      }

    }
  }, []);

  useEffect(() => {
    if (template_type === "") {
      setExportType("custom");
    } else {
      setExportType(template_type);
    }

    // if (templateId !== "" && templateId !== null) {
    //   $('input[name="temp_name"]').prop("readonly", true);
    // } else {
    //   $('input[name="temp_name"]').prop("readonly", false);
    // }
  }, []);
  useEffect(() => {
    if (Object.entries(versions).length === 1) {
      setSelectedVersion(Object.keys(versions)[0]);
    }
  }, [versions, data]);
  useEffect(() => {
    if (data.template_attributes.length > 0) {
      setIgnoreUnmapped(true); // Set the checkbox based on the condition
    }
  }, [data.template_attributes]);
  const handleSaveTemplateName = () => {
    if (saveTemplateName) {
      setSaveTemplateName(false);
      setSaveTemplateButton(0);
      setSubmitButton(true);
    } else {
      setSaveTemplateName(true);
      setSaveTemplateButton(1);
      setSubmitButton(true);
    }
  };

  const [importStatus, setImportStatus] = useState("1");
  const handleModalClose = () => {
    hideModal();
  };
  // Function to handle select change
  const handleStatusChange = (event) => {
    const selectedValue = event.target.value;
    setImportStatus(selectedValue);
  };
  const handleSubmitClick = () => {
    var version = $("#version").val();
    var exportType = $("#export-type").val();
    var store = selectedCatalog;
    var storeexport = $("#catalog").val();
    var templatenamefield = $(".template-name input").val();
    var errors = [];

    if (data.data_required.template_method_type === "import") {
      if (version === "") {
        errors.push("Please select a Language.");
      }
      if (data.import_action !== "2") {
        if (store === "" || store === null || store.length === 0) {
          errors.push("Please select a Store.");
        }
      }
      if ($("#customSwitch1").is(":checked")) {
        if (templatenamefield === "") {
          errors.push("Please enter a Template Name.");
        }
      }
    } else {
      if (storeexport === "") {
        errors.push("Please select a Store.");
      }
      if (version === "") {
        errors.push("Please select a Language.");
      }
      if (exportType === "") {
        errors.push("Please select Export Type.");
      }
      if ($("#ignore_unmapped").is(":checked")) {
        if (templatenamefield === "") {
          errors.push("Please enter a Template Name.");
        }
      }
    }

    if (errors.length > 0) {
      // Loop through errors and set them to respective variables
      errors.forEach(function (error) {
        if (error.includes("Language")) {
          setVersionError(error);
        } else if (error.includes("Store")) {
          setStoreError(error);
        } else if (error.includes("Export Type")) {
          setExportError(error);
        } else if (error.includes("Template Name")) {
          setTempnameError(error);
        }
      });
      return;
    } else {
      // Clear all errors if there are no errors
      setExportError("");
      setVersionError("");
      setStoreError("");
      setTempnameError("");
    }

    // Show loading state
    $("#pro_imp_start_btn .loading_btn").removeClass("d-none");
    $("#pro_imp_start_btn").prop("disabled", true);

    const ignoreUnmapped = $("#ignore_unmapped").prop("checked") ? "on" : "off";
    let convertedCatalog;
    if (data.data_required.template_method_type === "import") {
      convertedCatalog = selectedCatalog.map((item) => item.value);

      if (convertedCatalog.length === 0) {
        convertedCatalog = [];
      }
    } else {
      convertedCatalog = $("#catalog").val();
    }
    const newArray = {
      _token: _token,
      file_path: file_path,
      organization_id: organization_id,
      template_method_type: template_method_type,
      nodes: { data: convertedData },
      version: selectedVersion,
      catalog: convertedCatalog,
      ignore_unmapped: ignoreUnmapped,
      temp_name: tempName,
      temp_id: tempId,
      status: importStatus,
      export_type: exportType,
      import_action: data.import_action || "",
    };
    if ($("#customSwitch1").prop("checked")) {
      var tempStatus = "on";
      newArray.temp_status = tempStatus; // Add "temp_status" to newArray
    }
    if (data.data_required.template_method_type === "import") {
      var postroute = "import";
    } else {
      var postroute = "export";
    }
    axios.defaults.headers.common["X-CSRF-TOKEN"] = document
      .querySelector('meta[name="csrf-token"]')
      .getAttribute("content");

    axios
      .post(`/products/${postroute}`, newArray)
      .then((response) => {
        //     // Handle the response from Laravel
        toast.success(response.data.message);
        //$(".loading_btn").addClass("hidden");
        window.location.href = "/products";
      })
      .catch((error) => {
        // Handle errors and hide loading state
        $("#pro_imp_start_btn .loading_btn").addClass("d-none");
        $("#pro_imp_start_btn").prop("disabled", false);

        if (error.response) {
          toast.error(error.response.data.message || "An error occurred");
        } else if (error.request) {
          toast.error("No response received from the server");
        } else {
          toast.error("Error in setting up the request");
        }
      });
  };
  //handle save template
  const handleSaveTemplateClick = () => {
    var version = $("#version").val();
    var exportType = $("#export-type").val();
    var store = selectedCatalog;
    var storeexport = $("#catalog").val();
    var templatenamefield = $(".template-name input").val();
    if (data.data_required.template_method_type === "import") {
      if (version === "") {
        setVersionError("Please select a Language.");
        return;
      } else if (store === "" || store === null || store.length === 0) {
        setStoreError("Please select a Store.");
        return;
      } else if ($("#customSwitch1").is(":checked")) {
        if (templatenamefield === "") {
          setTempnameError("Please enter a Template Name.");
          return;
        }
      } else {
        setExportError("");
        setVersionError("");
        setStoreError("");
        setTempnameError("");
      }
    } else {
      if (storeexport === "") {
        setStoreError("Please select a Store.");
        return;
      } else if (version === "") {
        setVersionError("Please select a Language.");
        return;
      } else if (exportType === "") {
        setExportError("Please select Export Type.");
        return;
      } else if ($("#ignore_unmapped").is(":checked")) {
        if (templatenamefield === "") {
          setTempnameError("Please enter a Template Name.");
          return;
        }
      } else {
        setExportError("");
        setVersionError("");
        setStoreError("");
        setTempnameError("");
      }
    }
    $("#template-btn").removeClass("hidden");

    const ignoreUnmapped = $("#ignore_unmapped").prop("checked") ? "on" : "off";
    let convertedCatalog;
    if (data.data_required.template_method_type === "import") {
      convertedCatalog = selectedCatalog.map((item) => item.value);
    } else {
      convertedCatalog = $("#catalog").val();
    }

    const newArray = {
      _token: _token,
      file_path: file_path,
      organization_id: organization_id,
      template_method_type: template_method_type,
      nodes: { data: convertedData },
      version: selectedVersion,
      catalog: convertedCatalog,
      ignore_unmapped: ignoreUnmapped,
      temp_name: tempName,
      temp_id: tempId,
      status: importStatus,
      export_type: exportType || "",
      import_action: data.import_action || "",
    };

    if (saveTemplateName) {
      var tempStatus = "on";
      newArray.temp_status = tempStatus; // Add "temp_status" to newArray
    }

    axios.defaults.headers.common["X-CSRF-TOKEN"] = document
      .querySelector('meta[name="csrf-token"]')
      .getAttribute("content");

    axios
      .post("/Mapping/template/create", newArray)
      .then((response) => {
        //     // Handle the response from Laravel

        if (response.data.status === "success") {
          onSaveTemplate(response.data.data);
          toast.success(response.data.message);

          hideModal();
          // onUpdateTemplateName(templateName);
        } else if (response.data.status === "error") {
          toast.error(response.data.message);
          $(".template-name input").prop("readonly", false);
          hideModal();
        }
      })
      .catch((error) => {
        // Handle errors
        if (error.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          toast.error(error.response.data.message);
        } else if (error.request) {
          // The request was made but no response was received
          toast.error("No response received:", error.request);
          // You may want to handle this case differently, depending on your requirements
        } else {
          // Something happened in setting up the request that triggered an Error
          toast.error("Error setting up the request:", error.message);
        }
      });
  };
  return (
    <div
      className="modal block show"
      id="mapping_product"
      data-bs-backdrop="static"
      data-bs-keyboard="false"
      tabIndex="-1"
      aria-labelledby="exampleModalLabel"
      aria-modal="true"
      role="dialog"
    >
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header">
            <h3 className="modal-title " id="exampleModalLabel">
              {data.data_required.template_method_type === "import"
                ? "Import Configuration"
                : data.data_required.template_method_type === "export"
                ? "Export Configuration"
                : data.data_required.template_method_type === "shopify"
                ? "Shopify Configuration"
                : ""}
            </h3>
            <button
              type="button"
              className="btn-sm border-0 bg-white fs-24 px-0 pt-0 close-import-modal"
              data-bs-dismiss="modal"
              aria-label="Close"
              onClick={handleModalClose}
            >
              <span aria-hidden="true">×</span>
            </button>
          </div>
          <div className="modal-body">
            {data.data_required.template_method_type === "export" ? (
              <div className="form-group mt-3">
                <label htmlFor="export-type">
                  Export Type&nbsp;<span className="text-danger">*</span>
                </label>
                <input
                  id="export-type"
                  type="text"
                  value={exportType}
                  disabled
                  className="form-control capitalize pl-2 py-2 pr-6 mt-2"
                  onChange={handleExportType}
                />
                {/* <select
                  name="version"
                  className="w-full border b-1 bg-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm"
                  id="export-type"
                  value={exportType}
                  onChange={handleExportType}
                  disabled="disabled"
                >
                  <option value="">Choose</option>
                  <option
                    value="shopify"
                    selected={template_type === "shopify"}
                  >
                    Shopify
                  </option>
                  <option
                    value="magento"
                    selected={template_type === "magento"}
                  >
                    Magento
                  </option>
                  <option value="custom" selected={template_type === "custom"}>
                    Custom
                  </option>
                </select> */}
                {exportError && (
                  <span className="text-red-500">{exportError}</span>
                )}
              </div>
            ) : (
              ""
            )}
            <div className="form-group mt-3">
              <label htmlFor="version">
                Select Language&nbsp;<span className="text-danger">*</span>
              </label>
              <select
                name="version"
                className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent"
                id="version"
                value={selectedVersion}
                onChange={handleVersionChange}
              >
                <option value="">Choose</option>
                {Object.entries(versions).map(([versionId, versionName]) => (
                  <option
                    key={versionId}
                    value={versionId}
                    selected={
                      Object.entries(versions).length === 2 ? "selected" : ""
                    }
                  >
                    {versionName}
                  </option>
                ))}
              </select>
              {versionError && (
                <span className="text-red-500">{versionError}</span>
              )}
            </div>
            <div className="form-group mt-3">
              <label htmlFor="status">
                Select Status&nbsp;<span className="text-danger">*</span>
              </label>
              <select
                name="status"
                className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent"
                id="status"
                required=""
                value={importStatus}
                onChange={handleStatusChange}
              >
                <option value="1">Published</option>
                <option value="0">Draft</option>
              </select>
            </div>
            <div className="form-group mb-3 mt-3">
              <label htmlFor="catalog">
                Select Stores&nbsp;<span className="text-danger">*</span>
              </label>

              {data.data_required.template_method_type == "import" ? (
                <Select
                  className="w-full select-stores"
                  options={options}
                  isMulti
                  value={selectedCatalog}
                  onChange={handleCatalogChange}
                />
              ) : (
                <select
                  name="catalog"
                  className="w-full border b-1 b-gray-200 pl-2 py-2 pr-6 mt-2 rounded-sm bg-transparent"
                  id="catalog"
                  onChange={handleCatalogSingleChange}
                  value={selectedStore}
                >
                  {Object.entries(catalogs).length > 1 && (
                    <option value="">Choose</option>
                  )}

                  {Object.entries(catalogs).map(([catalogId, catalogName]) => (
                    <option
                      key={catalogId}
                      value={catalogId}
                      // selected={catalogId == selectedStore}
                    >
                      {catalogName}
                    </option>
                  ))}
                </select>
              )}
              {storeError && <span className="text-red-500">{storeError}</span>}
            </div>
            {submitButton && (
              <div
                id="template_checkbox_div"
                className={`formStyle ${
                  data.data_required.apimio_plans_access.template_save !== undefined &&
                  data.data_required.apimio_plans_access.template_save === false
                    ? "d-none"
                    : ""
                }`}
              >
                <div className="d-flex flex-row mb-3 align-items-center">
                  <label className="Roboto bold text-dark">
                    Do you want to save template for later?
                  </label>
                  <div className="custom-control custom-switch htmlFormStyle ms-3">
                    <label htmlFor="customSwitch1" className="me-2">
                      No
                    </label>
                    <div className="form-check form-switch d-inline" id="draft">
                      <input
                        type="checkbox"
                        className="custom-control-input form-check-input"
                        id="customSwitch1"
                        checked={saveTemplateName}
                        onChange={handleSaveTemplateName}
                      />
                      <label
                        className="custom-control-label custom-label ml-2"
                        htmlFor="customSwitch1"
                      >
                        {" "}
                        Yes
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {saveTemplateName && (
              <div
                className={`form-group mb-4 template-name ${
                  data.data_required.apimio_plans_access.template_save !== undefined &&
                  data.data_required.apimio_plans_access.template_save === false
                    ? "d-none"
                    : ""
                }`}
              >
                <label htmlFor="catalog">
                  Name this Template&nbsp;<span className="text-danger">*</span>
                </label>
                <div className="input-group relative">
                  <input
                    required=""
                    type="text"
                    className="w-full border b-1 b-gray-200 pl-2 py-2 pr-8 mt-2 rounded-sm "
                    name="temp_name"
                    value={tempName}
                    onChange={handleTempNameChange}
                    disabled={tempNameInput}
                  />
                  <span className="input-group-append absolute right-0 top-2 h-9">
                    <button
                      className="text-secondary border-start-0 border bg-white-smoke px-3 h-[34px]"
                      id="edit_temp_name"
                      type="button"
                      onClick={handleTempNameDisable}
                    >
                      <i className="fas fa-edit" aria-hidden="true"></i>
                    </button>
                  </span>
                </div>
                {tempnameError && (
                  <span className="text-red-500">{tempnameError}</span>
                )}

                <input
                  type="hidden"
                  className="htmlForm-control"
                  name="temp_id"
                  value={tempId}
                  key={tempId}
                />
              </div>
            )}

            <hr className="mt-1 mb-4 divider" />
            <div className="form-group relative" id="mapping_submit_popup">
              {submitButton ? (
                <>
                  {data.data_required.template_method_type === "export" &&
                  data.data_required.apimio_plans_access.prime_user !== undefined &&
                  data.data_required.apimio_plans_access.prime_user === false ? (
                    <button
                      data-bs-toggle="tooltip"
                      data-bs-placement="top"
                      className="text-white btn btn-primary float-end onclick-disabled-with-loader opacity-50"
                      data-bs-original-title="Export feature is available for paid plans only. Upgrade to a paid plan to enable this feature."
                      id="pro_imp_start_btn"
                      name="import_csv"
                    >
                      <span className="ms-2 d-sm-inline">
                        {data.data_required.template_method_type === "import"
                          ? "Start Importing"
                          : "Start Exporting"}
                      </span>
                      <i className="fa small fa-lock text-gray ms-2"></i>
                    </button>
                  ) : data.data_required.template_method_type === "export" ||
                    data.data_required.template_method_type === "import" ? (
                    <button
                      id="pro_imp_start_btn"
                      name="import_csv"
                      className="btn btn-primary float-end onclick-disabled-with-loader"
                      onClick={handleSubmitClick}
                    >
                      {data.data_required.template_method_type === "import"
                        ? "Start Importing"
                        : "Start Exporting"}
                      <span className="loading_btn d-none">
                        <span
                          className="spinner-border spinner-border-sm ms-2"
                          role="status"
                          aria-hidden="true"
                        ></span>
                      </span>
                    </button>
                  ) : null}
                </>
              ) : null}
              {saveTemplateButton ? (
                <button
                  type="button"
                  name="save_template"
                  id="template-btn"
                  className={`btn btn-outline-primary me-4 mb-1 float-end template-save ${
                    data.data_required.apimio_plans_access.template_save !== undefined &&
                    data.data_required.apimio_plans_access.template_save === false
                      ? "d-none"
                      : ""
                  }`}
                  data-btn_name="Save Template"
                  onClick={handleSaveTemplateClick}
                >
                  Save Template
                </button>
              ) : null}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SubmitModal;
