import React from "react";
import { <PERSON>, Col, Button } from "antd";
import UpArrow from "../../../../public/v2/icons/uparrow-icon.svg";
import Doughnut<PERSON>hart from "./DoughnutChart";

const ImageQualityScores = ({ title, description, showHeader = true, className = "", scores }) => {
    const cards = [
        {
            index: 1,
            value: scores?.good,
            quality: "Good",
            description: "Products with 90% or Greater Quality Score.",
            buttonLabel: "Update",
            color: "#15D476",
        },
        {
            index: 2,
            value: scores?.fair,
            quality: "Fair",
            description: "Products with less than 90% and Greater than Quality Score.",
            buttonLabel: "Improve",
            color: "#FF9C3E",
        },
        {
            index: 3,
            value: scores?.bad,
            quality: "Bad",
            description: "Products with less than 50% Quality Score.",
            buttonLabel: "Improve",
            color: "#FE1F23",
        },
    ];

    // Calculate total for percentage
    const total = scores?.good + scores?.fair + scores?.bad;
    const percentage = Math.round((scores?.good / total) * 100);

    return (
        <div className={`p-5 ${className} `}>
            {showHeader && (
                <>
                    <p className="text-[#252525] font-[600] text-[18px]">{title || "Images Quality Scores"}</p>
                    <p className="text-[#626262] font-normal text-[14px]">
                        {description || "Track image Quality Metrics for Your Products."}
                    </p>
                </>
            )}
            <div className="py-5">
                <Row className="flex gap-5">
                    <Col flex="1" className="flex items-center justify-center">
                        <DoughnutChart qualityType="Image" scores={scores} />
                    </Col>
                    {cards.map((card) => (
                        <Col flex="1" key={card.index}>
                            <div className="h-[168px] p-5 text-center bg-white flex flex-col rounded-[12px] items-center justify-center border border-[#EBEBEB]">
                                <p className="text-[#252525] text-[40px] font-[700]">{card.value}</p>
                                <p className="text-[16px] font-[600]" style={{ color: card.color }}>
                                    {card.quality}
                                </p>
                                <p className="text-[#626262] font-normal text-[10px]">{card.description}</p>
                                <Button
                                    className="text-[14px] font-normal rounded-[16px]"
                                    style={{ borderColor: card.color, color: card.color }}
                                >
                                    <img src={UpArrow} alt="up arrow" /> {card.buttonLabel}
                                </Button>
                            </div>
                        </Col>
                    ))}
                </Row>
            </div>
        </div>
    );
};

export default ImageQualityScores;
