import React from "react";
import { But<PERSON> } from "antd";
import UpArrow from "../../../../public/v2/icons/uparrow-icon.svg";

/**
 * Reusable quality score card component
 *
 * @param {Object} props - Component props
 * @param {number} props.value - The numeric value to display (e.g., 36, 50, 2)
 * @param {string} props.label - The label for this card (e.g., "Valid Field", "Good", "Bad")
 * @param {string} props.description - Description text for the card
 * @param {string} props.color - The color for the label and button (e.g., "#15D476")
 * @param {string} props.buttonLabel - The text for the button (e.g., "Update", "Improve")
 * @param {Function} props.onButtonClick - Function to call when button is clicked
 * @param {string} props.className - Additional CSS classes
 * @param {number} props.height - Optional height for the card (default: 168px)
 */
const QualityScoreCard = ({ value, label, description, color, buttonLabel = "Update", onButtonClick, className = "", height = 168 }) => {
    return (
        <div
            className={`p-5 text-center bg-white flex flex-col rounded-[12px] items-center justify-center border border-[#EBEBEB] ${className}`}
            style={{ height: `${height}px` }}
        >
            <p className="text-[#252525] text-[40px] font-[700] m-0">{value}</p>
            <p className="text-[16px] font-[600] m-0" style={{ color }}>
                {label}
            </p>
            <p className="text-[#626262] font-normal text-[10px] m-1">{description}</p>
            <Button className="text-[14px] font-normal rounded-[16px]" style={{ borderColor: color, color }} onClick={onButtonClick}>
                <img src={UpArrow} alt="up arrow" /> {buttonLabel}
            </Button>
        </div>
    );
};

export default QualityScoreCard;
