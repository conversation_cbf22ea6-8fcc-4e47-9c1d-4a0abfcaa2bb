import React, { useState, useEffect } from "react";
import { Upload, Button, Table } from "antd";
import { UploadOutlined, DeleteOutlined } from "@ant-design/icons";
import ImageQualityScores from "../../components/ImageQualityScores";
import { get } from "../../../axios";

const Media = ({ product, onDataChange }) => {
    const [fileList, setFileList] = useState([
        {
            key: "1",
            imageUrl: "/path/to/image.jpg",
            quality: "Good",
            type: "Heic",
            size: "50.02 KB",
            dimension: "1080 x 1080 px",
        },
    ]);

    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [qualityScores, setQualityScores] = useState(null);

    useEffect(() => {
        fetchQualityScores();
    }, []);

    const fetchQualityScores = async () => {
        try {
            const response = await get("image-quality-score");
            setQualityScores(response.imageQualityScore);
        } catch (error) {
            console.error("Error fetching quality scores:", error);
        }
    };

    const handleChange = ({ fileList: newFileList }) => {
        setFileList(newFileList);
        onDataChange?.();
    };

    const columns = [
        {
            title: "Image",
            dataIndex: "imageUrl",
            key: "image",
            render: (imageUrl) => <div className="w-12 h-12 bg-gray-200 rounded-md"></div>,
        },
        {
            title: "Image Quality",
            dataIndex: "quality",
            key: "quality",
            render: (quality) => (
                <span className={`${quality === "Good" ? "text-[#15D476]" : quality === "Fair" ? "text-[#FFA500]" : "text-[#FE1F23]"}`}>
                    {quality}
                </span>
            ),
        },
        {
            title: "Type",
            dataIndex: "type",
            key: "type",
        },
        {
            title: "Size",
            dataIndex: "size",
            key: "size",
        },
        {
            title: "Dimension",
            dataIndex: "dimension",
            key: "dimension",
        },
        {
            title: "Action",
            key: "action",
            render: () => <DeleteOutlined className="text-[#FE1F23] cursor-pointer" />,
        },
    ];

    const onSelectChange = (newSelectedRowKeys) => {
        setSelectedRowKeys(newSelectedRowKeys);
    };

    const rowSelection = {
        selectedRowKeys,
        onChange: onSelectChange,
    };

    return (
        <div className="p-4">
            {/* Top Section */}
            <div className="flex flex-col 2xl:flex-row gap-5 mb-8">
                {/* Left Section - Upload Controls */}
                <div className="w-full 2xl:w-6/12">
                    <h2 className="text-[14px] font-[600] text-[#252525] pb-1">Social Images</h2>
                    <div className="border border-[#DBDBDB] rounded-[12px] p-6 h-full">
                        <h2 className="text-xl font-bold mb-4 text-[#252525]">Images</h2>
                        <div className="flex gap-2 items-center h-[130px] justify-center">
                            <Button type="primary" className="bg-[#740898]">
                                Add Images
                            </Button>
                            <Button type="default" className="border-[#740898] text-[#292929]">
                                Add from Existing
                            </Button>
                        </div>
                    </div>
                </div>

                {/* Right Section - Quality Score */}
                <div className="w-full 2xl:w-6/12">
                    <div className="border border-[#DBDBDB] rounded-[12px] p-6">
                        <ImageQualityScores showHeader={true} className="p-0" scores={qualityScores} />
                    </div>
                </div>
            </div>

            {/* Table Section */}
            <div className="mt-8">
                <h2 className="text-lg font-semibold mb-4 text-[#252525]">Products</h2>
                <Table
                    rowSelection={rowSelection}
                    columns={columns}
                    dataSource={fileList}
                    className="border border-[#DBDBDB] rounded-md media-table"
                    pagination={false}
                />
                <style jsx global>{`
                    .media-table .ant-table-thead > tr > th {
                        border-right: 1px solid #dbdbdb;
                        background: #f9fafb;
                    }
                    .media-table .ant-table-thead > tr > th:last-child {
                        border-right: none;
                    }
                    .media-table .ant-table-tbody > tr > td {
                        border-right: 1px solid #dbdbdb;
                    }
                    .media-table .ant-table-tbody > tr > td:last-child {
                        border-right: none;
                    }
                    .media-table .ant-table-cell {
                        padding: 16px;
                    }
                `}</style>
            </div>
        </div>
    );
};

export default Media;
