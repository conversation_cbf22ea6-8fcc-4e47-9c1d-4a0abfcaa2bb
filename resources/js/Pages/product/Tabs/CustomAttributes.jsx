import React, { useState } from "react";
import { Select, Button, Input } from "antd";

const { Option } = Select;

const CustomAttributes = ({ product, onDataChange }) => {
    const [selectedSet, setSelectedSet] = useState(null);

    // Mock data - replace with actual data
    const attributeFields = [
        { label: "Brand", value: "Rivet Cove Mid-Century Modern Tufted" },
        { label: "Vendor", value: "Rivet Cove Mid-Century Modern Tufted" },
        { label: "Category", value: "Rivet Cove Mid-Century Modern Tufted" },
        { label: "Store", value: "Rivet Cove Mid-Century Modern Tufted" },
    ];

    const handleSetChange = (value) => {
        setSelectedSet(value);
        onDataChange?.();
    };

    return (
        <div className="flex flex-col 2xl:flex-row gap-8 p-5">
            {/* Left Section - Attribute Sets */}
            <div className="w-full 2xl:w-7/12">
                <div className="flex flex-col">
                    <div className="mb-4">
                        <label className="block mb-2 font-bold text-[14px] text-[#252525]">Attribute Sets</label>
                        <div className="flex gap-2">
                            <Select placeholder="Select" onChange={handleSetChange} style={{ width: "100%" }} className="flex-grow">
                                <Option value="default">Default</Option>
                                <Option value="clothing">Clothing</Option>
                                <Option value="electronics">Electronics</Option>
                            </Select>
                            <Button
                                type="default"
                                className="whitespace-nowrap border-[#740898] text-[#292929] hover:border-[#740898] hover:text-[#292929]"
                            >
                                Assign Attribute Sets
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Right Section - Attribute Fields */}
            <div className="w-full 2xl:w-5/12 border border-[#DBDBDB] rounded-[12px] p-6">
                <div className="space-y-6">
                    {attributeFields.map((field, index) => (
                        <div key={index} className="flex flex-col gap-2">
                            <div className="flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-[#15D476]"></div>
                                <label className="text-[14px] font-medium text-[#252525]">{field.label}</label>
                            </div>
                            <Select value={field.value} style={{ width: "100%" }} className="text-[#252525]">
                                <Option value={field.value}>{field.value}</Option>
                            </Select>
                            {field.label === "Category" && (
                                <div className="flex gap-2 mt-2">
                                    <div className="bg-[#F3E6F9] text-[#740898] px-3 py-1 rounded text-sm">Short</div>
                                    <div className="bg-[#F3E6F9] text-[#740898] px-3 py-1 rounded text-sm">Pak</div>
                                </div>
                            )}
                            {field.label === "Store" && (
                                <div className="flex gap-2 mt-2">
                                    <div className="bg-[#F3E6F9] text-[#740898] px-3 py-1 rounded text-sm">Store name 1</div>
                                    <div className="bg-[#F3E6F9] text-[#740898] px-3 py-1 rounded text-sm">Store name 2</div>
                                </div>
                            )}
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default CustomAttributes;
