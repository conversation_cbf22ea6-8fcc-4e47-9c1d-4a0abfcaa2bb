import React, { useState, useEffect } from "react";
import { Row, Col } from "antd";
import ProductQualityScore from "./ProductQualityScore";
import ImageQualityScores from "../../components/ImageQualityScores";
import { get } from "../../../axios";

const QualityScoresDashboard = ({ product }) => {
    // Sample data for image quality scores - replace with API data in production
    const [imageQualityScores, setImageQualityScores] = useState({
        good: 50,
        fair: 30,
        bad: 20,
    });

    useEffect(() => {
        fetchImageQualityScores();
    }, []);

    const fetchImageQualityScores = async () => {
        try {
            const response = await get("image-quality-score");
            setImageQualityScores(response.imageQualityScore);
        } catch (error) {
            console.error("Error fetching image quality scores:", error);
        }
    };

    const handleDataChange = () => {
        // Handle data change functionality
        console.log("Data changed");
    };

    return (
        <div className="p-4">
            {/* First row with two columns side by side */}
            <Row gutter={[16, 16]}>
                {/* Left column: Product Quality Score */}
                <Col xs={24} md={12}>
                    <ProductQualityScore product={product} onDataChange={handleDataChange} />
                </Col>

                {/* Right column: Image Quality Scores with Good/Fair/Bad cards */}
                <Col xs={24} md={12}>
                    <ImageQualityScores
                        showHeader={true}
                        scores={imageQualityScores}
                        title="Images Quality Score"
                        description="Track Image Quality Metrics for Your Products."
                    />
                </Col>
            </Row>

            {/* Second row with another Product Quality Score */}
            <Row className="mt-4">
                <Col span={24}>
                    <ProductQualityScore product={product} onDataChange={handleDataChange} />
                </Col>
            </Row>
        </div>
    );
};

export default QualityScoresDashboard;
