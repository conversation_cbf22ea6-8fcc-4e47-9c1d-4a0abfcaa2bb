import React from "react";
import { <PERSON>, Row, Col } from "antd";
import Doughnut<PERSON>hart from "../../components/DoughnutChart";
import QualityScoreCard from "../../components/QualityScoreCard";
import QualityScoreIndicator from "../../components/QualityScoreIndicator";
import ImageQualityScores from "../../components/ImageQualityScores";

const ProductQualityScore = ({ product, onDataChange }) => {
    // Sample data - replace with actual API data in production
    const scores = {
        good: 50,
        fair: 30,
        bad: 20,
        approve: 36,
        warning: 0,
        error: 2,
    };

    const completionPercentage = 80;

    const handleUpdate = () => {
        // Handle update functionality
        console.log("Update button clicked");
        if (onDataChange) onDataChange();
    };

    const handleImprove = () => {
        // Handle improve functionality
        console.log("Improve button clicked");
        if (onDataChange) onDataChange();
    };

    return (
        <div className="pr-5">
            <Row gutter={[35, 20]}>
                <Col xs={24} xxl={12}>
                    <Card className="border border-[#DBDBDB] rounded-md">
                        <div className="flex flex-col">
                            <QualityScoreIndicator
                                percentage={completionPercentage}
                                title="EN-US PRODUCT QUALITY SCORE"
                                description="Track Image Quality Metrics for Your Products."
                            />

                            <Row className="flex gap-4">
                                <Col flex="1" className="flex items-center justify-center">
                                    <DoughnutChart qualityType="Image" scores={scores} />
                                </Col>

                                <Col flex="1">
                                    <QualityScoreCard
                                        value={scores.approve}
                                        label="Valid Field"
                                        description="These fields don't require immediate attention."
                                        color="#15D476"
                                        buttonLabel="Update"
                                        onButtonClick={handleUpdate}
                                    />
                                </Col>

                                <Col flex="1">
                                    <QualityScoreCard
                                        value={scores.error}
                                        label="Invalid Field"
                                        description="Update these fields to improve the Product Comprehensive Score."
                                        color="#FE1F23"
                                        buttonLabel="Improve"
                                        onButtonClick={handleImprove}
                                    />
                                </Col>
                            </Row>
                        </div>
                    </Card>
                </Col>
                <Col xs={24} xxl={12} className="border border-[#DBDBDB] rounded-md">
                    <ImageQualityScores scores={scores} />
                </Col>
                <Col xs={24} xxl={12}>
                    <Card className="border border-[#DBDBDB] rounded-md">
                        <div className="flex flex-col">
                            <QualityScoreIndicator
                                percentage={completionPercentage}
                                title="EN-US PRODUCT QUALITY SCORE"
                                description="Track Image Quality Metrics for Your Products."
                            />

                            <Row className="flex gap-4" gutter={20}>
                                <Col flex="1" className="flex items-center justify-center">
                                    <DoughnutChart qualityType="Image" scores={scores} />
                                </Col>

                                <Col flex="1">
                                    <QualityScoreCard
                                        value={scores.approve}
                                        label="Valid Field"
                                        description="These fields don't require immediate attention."
                                        color="#15D476"
                                        buttonLabel="Update"
                                        onButtonClick={handleUpdate}
                                    />
                                </Col>

                                <Col flex="1">
                                    <QualityScoreCard
                                        value={scores.error}
                                        label="Invalid Field"
                                        description="Update these fields to improve the Product Comprehensive Score."
                                        color="#FE1F23"
                                        buttonLabel="Improve"
                                        onButtonClick={handleImprove}
                                    />
                                </Col>
                            </Row>
                        </div>
                    </Card>
                </Col>
            </Row>
        </div>
    );
};

export default ProductQualityScore;
