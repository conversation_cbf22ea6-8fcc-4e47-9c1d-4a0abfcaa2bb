import React, { useState } from "react";
import { Table, Button, Input, Select, Modal } from "antd";
import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";

const { Option } = Select;

const Variants = ({ product, onDataChange }) => {
    const [variants, setVariants] = useState([
        {
            key: "1",
            image: "/path/to/image.jpg",
            sku: "SF9702-S",
            name: "Small",
            upc: "",
            price: "1200",
            compareAtPrice: "70",
            costPrice: "1000",
            weight: "10",
            weightUnit: "Kg",
        },
    ]);

    const [isAddOptionModalOpen, setIsAddOptionModalOpen] = useState(false);
    const [optionTitle, setOptionTitle] = useState("");
    const [optionValues, setOptionValues] = useState([]);

    const handleVariantChange = (key, field, value) => {
        const newVariants = variants.map((variant) => {
            if (variant.key === key) {
                return { ...variant, [field]: value };
            }
            return variant;
        });
        setVariants(newVariants);
        onDataChange?.();
    };

    const handleAddOptionValue = () => {
        setOptionValues([...optionValues, { id: Date.now(), value: "" }]);
    };

    const handleOptionValueChange = (id, value) => {
        setOptionValues(optionValues.map((option) => (option.id === id ? { ...option, value } : option)));
    };

    const handleSaveOption = () => {
        // Handle saving the option and its values
        console.log("Saving option:", { title: optionTitle, values: optionValues });
        setIsAddOptionModalOpen(false);
        setOptionTitle("");
        setOptionValues([]);
    };

    const columns = [
        {
            title: "Image",
            dataIndex: "image",
            key: "image",
            render: (text, record) => <div className="w-12 h-12 bg-gray-200">{/* Image placeholder */}</div>,
        },
        {
            title: "SKU",
            dataIndex: "sku",
            key: "sku",
            render: (text, record) => <Input value={text} onChange={(e) => handleVariantChange(record.key, "sku", e.target.value)} />,
        },
        {
            title: "Name",
            dataIndex: "name",
            key: "name",
            render: (text, record) => <Input value={text} onChange={(e) => handleVariantChange(record.key, "name", e.target.value)} />,
        },
        {
            title: "UPC/EAN/ISBN",
            dataIndex: "upc",
            key: "upc",
            render: (text, record) => <Input value={text} onChange={(e) => handleVariantChange(record.key, "upc", e.target.value)} />,
        },
        {
            title: "Price",
            dataIndex: "price",
            key: "price",
            render: (text, record) => <Input value={text} onChange={(e) => handleVariantChange(record.key, "price", e.target.value)} />,
        },
        {
            title: "Compare At Price",
            dataIndex: "compareAtPrice",
            key: "compareAtPrice",
            render: (text, record) => (
                <Input value={text} onChange={(e) => handleVariantChange(record.key, "compareAtPrice", e.target.value)} />
            ),
        },
        {
            title: "Cost Price",
            dataIndex: "costPrice",
            key: "costPrice",
            render: (text, record) => <Input value={text} onChange={(e) => handleVariantChange(record.key, "costPrice", e.target.value)} />,
        },
        {
            title: "Weight",
            dataIndex: "weight",
            key: "weight",
            render: (text, record) => (
                <div className="flex items-center gap-2">
                    <Input value={text} onChange={(e) => handleVariantChange(record.key, "weight", e.target.value)} />
                    <Select
                        value={record.weightUnit}
                        onChange={(value) => handleVariantChange(record.key, "weightUnit", value)}
                        style={{ width: 70 }}
                    >
                        <Option value="Kg">Kg</Option>
                        <Option value="g">g</Option>
                        <Option value="lb">lb</Option>
                    </Select>
                </div>
            ),
        },
        {
            title: "Action",
            key: "action",
            render: (_, record) => (
                <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => {
                        const newVariants = variants.filter((item) => item.key !== record.key);
                        setVariants(newVariants);
                        onDataChange?.();
                    }}
                />
            ),
        },
    ];

    const addVariant = () => {
        const newVariant = {
            key: String(variants.length + 1),
            image: "/path/to/image.jpg",
            sku: `SF9702-${variants.length + 1}`,
            name: "Small",
            upc: "",
            price: "1200",
            compareAtPrice: "70",
            costPrice: "1000",
            weight: "10",
            weightUnit: "Kg",
        };
        setVariants([...variants, newVariant]);
        onDataChange?.();
    };

    return (
        <div className="p-4">
            <div className="flex justify-between items-center mb-4">
                <div className="flex items-center gap-2">
                    <h2 className="text-[14px] font-normal text-[#252525]">Already added Variants:</h2>
                    <span className=" font-[700]">{variants.length}</span>
                </div>
                <div className="flex gap-2">
                    <Button>Recreate Variants</Button>
                    <Button type="primary" className="bg-[#740898]" onClick={() => setIsAddOptionModalOpen(true)}>
                        Add Options
                    </Button>
                </div>
            </div>
            <div className="variants-container">
                <div className="variants-header h-[60px] border border-[#DBDBDB] border-b-0 rounded-t-md px-4 flex items-center bg-white">
                    <span className=" text-[#252525] font-[600] text-[18px]">All combinations of your variants are listed below.</span>
                </div>
                <Table
                    columns={columns}
                    dataSource={variants}
                    pagination={false}
                    className="border border-[#DBDBDB] rounded-b-md variants-table"
                    rowClassName="border-b border-[#DBDBDB]"
                />
            </div>

            <Modal
                title="Add Option"
                open={isAddOptionModalOpen}
                onCancel={() => {
                    setIsAddOptionModalOpen(false);
                    setOptionTitle("");
                    setOptionValues([]);
                }}
                footer={[
                    <Button
                        key="cancel"
                        onClick={() => {
                            setIsAddOptionModalOpen(false);
                            setOptionTitle("");
                            setOptionValues([]);
                        }}
                    >
                        Close
                    </Button>,
                    <Button key="submit" type="primary" className="bg-[#740898]" onClick={handleSaveOption}>
                        Save Changes
                    </Button>,
                ]}
            >
                <div className="space-y-4">
                    <div>
                        <label className="block mb-1">
                            Option title <span className="text-red-500">*</span>
                        </label>
                        <Input value={optionTitle} onChange={(e) => setOptionTitle(e.target.value)} placeholder="Enter option title" />
                    </div>

                    <div>
                        <label className="block mb-2">Manage Options (Values of your attribute)</label>
                        <div className="space-y-2">
                            {optionValues.map((option) => (
                                <Input
                                    key={option.id}
                                    value={option.value}
                                    onChange={(e) => handleOptionValueChange(option.id, e.target.value)}
                                    placeholder="Enter option value"
                                />
                            ))}
                        </div>
                        <Button type="primary" className="bg-[#740898] mt-2" onClick={handleAddOptionValue}>
                            Add Option Value
                        </Button>
                    </div>
                </div>
            </Modal>

            <style jsx global>{`
                .variants-table .ant-table-thead > tr > th {
                    border-right: 1px solid #dbdbdb;
                    background: #f9fafb;
                }
                .variants-table .ant-table-thead > tr > th:last-child {
                    border-right: none;
                }
                .variants-table .ant-table-tbody > tr > td {
                    border-right: 1px solid #dbdbdb;
                }
                .variants-table .ant-table-tbody > tr > td:last-child {
                    border-right: none;
                }
                .variants-table .ant-table-cell {
                    padding: 16px;
                }
            `}</style>
        </div>
    );
};

export default Variants;
