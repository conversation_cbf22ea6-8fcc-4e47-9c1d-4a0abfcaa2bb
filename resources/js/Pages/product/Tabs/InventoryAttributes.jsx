import React, { useState } from "react";
import { Select, Checkbox, Input } from "antd";

const { Option } = Select;

const InventoryAttributes = () => {
    const [selectedStore, setSelectedStore] = useState(null);
    const [selectedVariant, setSelectedVariant] = useState(null);
    const [trackQuantity, setTrackQuantity] = useState(true);
    const [continueSellingOutOfStock, setContinueSellingOutOfStock] = useState(false);

    // Mock data - replace with actual data
    const stores = [
        { value: "store1", label: "Store 1" },
        { value: "store2", label: "Store 2" },
    ];

    const variants = [
        { size: "Small", sku: "SF9702-S", location: "Torque Store Warehouse", quantity: 2095 },
        { size: "Medium", sku: "SF9702-M", location: "Torque Store Warehouse", quantity: 1500 },
        { size: "Large", sku: "SF9702-L", location: "Torque Store Warehouse", quantity: 1200 },
        { size: "Extra Large", sku: "SF9702-XL", location: "Torque Store Warehouse", quantity: 800 },
    ];

    const handleQuantityChange = (value, variant) => {
        // Handle quantity change
        console.log(value, variant);
    };

    return (
        <div className="flex flex-col">
            {/* Store Selection */}
            <div className="mb-6">
                <label className="block mb-2 font-semibold text-[14px] text-[#252525]">Select a Store:</label>
                <Select placeholder="Store Name" style={{ width: "660px" }} onChange={(value) => setSelectedStore(value)}>
                    {stores.map((store) => (
                        <Option key={store.value} value={store.value}>
                            {store.label}
                        </Option>
                    ))}
                </Select>
            </div>

            {/* Product Handle */}
            <div className="mb-6">
                <h2 className="text-lg font-semibold text-[#252525]">Rome-court-shorts-black-grey</h2>
            </div>

            {/* Main Content */}
            <div className="flex gap-6">
                {/* Variants List (20%) */}
                <div className="w-[20%]">
                    <div className="flex flex-col gap-2">
                        {variants.map((variant) => (
                            <div
                                key={variant.sku}
                                onClick={() => setSelectedVariant(variant)}
                                className={`cursor-pointer p-3 rounded-md flex items-center gap-2 ${
                                    selectedVariant?.sku === variant.sku ? "bg-[#F1E6F5] text-[#740898]" : "hover:bg-gray-50"
                                }`}
                            >
                                <div className="w-6 h-6 bg-gray-200 rounded-md"></div>
                                <span>{variant.size}</span>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Table Section (80%) */}
                <div className="w-[80%]">
                    {/* Checkboxes */}
                    <div className="mb-4 flex gap-6">
                        <Checkbox checked={trackQuantity} onChange={(e) => setTrackQuantity(e.target.checked)}>
                            Track quantity
                        </Checkbox>
                        <Checkbox checked={continueSellingOutOfStock} onChange={(e) => setContinueSellingOutOfStock(e.target.checked)}>
                            Continue selling when out of stock
                        </Checkbox>
                    </div>

                    {/* Table */}
                    <div className="border border-[#DBDBDB] rounded-md overflow-hidden">
                        <table className="w-full">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-4 py-3 text-left text-sm font-semibold text-gray-600">SKU</th>
                                    <th className="px-4 py-3 text-left text-sm font-semibold text-gray-600">Location</th>
                                    <th className="px-4 py-3 text-left text-sm font-semibold text-gray-600">Quantity</th>
                                </tr>
                            </thead>
                            <tbody>
                                {selectedVariant && (
                                    <tr>
                                        <td className="px-4 py-3 text-sm">{selectedVariant.sku}</td>
                                        <td className="px-4 py-3 text-sm">{selectedVariant.location}</td>
                                        <td className="px-4 py-3 text-sm">
                                            <Input
                                                type="number"
                                                value={selectedVariant.quantity}
                                                onChange={(e) => handleQuantityChange(e.target.value, selectedVariant)}
                                                style={{ width: "100px" }}
                                            />
                                        </td>
                                    </tr>
                                )}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default InventoryAttributes;
