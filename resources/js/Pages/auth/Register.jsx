import React, { useState, useEffect } from "react";
import { useF<PERSON>, router, Head } from "@inertiajs/react";
import { router as Router } from "@inertiajs/core";
import Logo from "../../../../public/v2/images/logo.png";
import RegisterBack from "../../../../public/v2/images/signup-img.png";
import { Input, Button, Checkbox, message } from "antd";
import GoogleIcon from "../../../../public/v2/icons/google-icon.svg";
import "react-phone-input-2/lib/style.css";
import PhoneInput from "react-phone-input-2";
import { contains } from "jquery";

// Add style tag before the component
const style = `
    @media (max-width: 1000px) {
        .right-section {
            display: none !important;
        }
        .left-section {
            width: 100% !important;
        }
    }
`;

const Register = ({ invitation = {} }) => {
    const [countryCode, setCountryCode] = useState("");
    const [fullName, setFullName] = useState("");

    const { data, setData, processing, post, errors } = useForm({
        fname: invitation.fname || "",
        lname: invitation.lname || "",
        email: invitation.email || "",
        password: "",
        password_confirmation: "",
        phone: "",
        scheduleDemo: false,
        agreeToTerms: false,
        is_invitation: invitation.is_invitation || false,
    });

    // Add style tag at the top of the component
    const rightSectionStyle = {
        "@media (max-width: 1000px)": {
            display: "none !important",
        },
    };

    useEffect(() => {
        fetch("https://ipapi.co/json/")
            .then((response) => response.json())
            .then((data) => setCountryCode(data.country_code.toLowerCase()))
            .catch(() => setCountryCode("us"));
    }, []);

    // Set full name when invitation data is available
    useEffect(() => {
        if (invitation.fname || invitation.lname) {
            const name = `${invitation.fname || ''} ${invitation.lname || ''}`.trim();
            setFullName(name);
        }
    }, [invitation]);

    const handleChange = (e) => {
        const { name, value, type, checked } = e.target;
        if (name === "name") {
            setFullName(value);
            // Split name into first and last name
            const nameParts = value.trim().split(/\s+/);
            setData("fname", nameParts[0] || "");
            setData("lname", nameParts.slice(1).join(" ") || "");
        } else {
            setData(name, type === "checkbox" ? checked : value);
        }
    };

    const handlePhoneChange = (value) => {
        setData("phone", value);
    };

    const handleSubmit = () => {
        post(route("create.account"), {
            preserveScroll: true,
            onSuccess: () => {
                message.success("Registration successful!");
            },
            onError: (errors) => {
                message.error(Object.values(errors)[0] || "Registration failed!");
            },
        });
    };

    const handleGoogleSignup = () => {
        window.location.href = "/google/redirection";
    };

    const handleLogin = () => {
        Router.visit("/login");
    };
    return (
        <div className="flex h-screen">
            <Head title="Register" />
            <style>{style}</style>
            {/* Left Section */}
            <div className="left-section w-7/12 2xl2:w-7/12 bg-white flex flex-col justify-center items-center relative">
                {/* Logo */}
                <div className="absolute top-8 left-8">
                    <img src={Logo} alt="Logo" className="w-28 md2:w-32 2xl2:w-36" />
                </div>

                <div className="max-w-xl md2:max-w-2xl 2xl2:max-w-3xl">
                    <h1 className="text-2xl md2:text-3xl 2xl2:text-[40px] font-[700] mb-4 md2:mb-6 2xl2:mb-8 text-center text-[#252525]">
                        {invitation.is_invitation ? 'Complete Your Registration' : 'Create an Account'}
                    </h1>

                    {/* Invitation Banner */}
                    {invitation.is_invitation && (
                        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                            <div className="flex items-center">
                                <div className="flex-shrink-0">
                                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <div className="ml-3">
                                    <p className="text-sm text-blue-700">
                                        <strong>You've been invited to join a team!</strong> Complete your registration to access your organization.
                                    </p>
                                </div>
                            </div>
                        </div>
                    )}

                    <div className="space-y-[10px]">
                        <input name="ip" value="" id="ip_field" type="hidden" />

                        {/* Name Field */}
                        <div>
                            <label htmlFor="name" className="block mb-1 font-[400]">
                                Name
                            </label>
                            <Input
                                value={fullName}
                                status={errors.fname || errors.lname ? "error" : ""}
                                onChange={handleChange}
                                name="name"
                                placeholder="Full name"
                                className="p-2 h-8 border rounded-md"
                            />
                            {(errors.fname || errors.lname) && (
                                <div className="text-red-500 text-sm mt-1">{errors.fname || errors.lname}</div>
                            )}
                        </div>

                        {/* Email Field */}
                        <div>
                            <label htmlFor="email" className="block mb-1 font-[14px]">
                                Business Email
                                {invitation.is_invitation && <span className="text-blue-600 text-xs ml-2">(From invitation)</span>}
                            </label>
                            <Input
                                name="email"
                                status={errors.email ? "error" : ""}
                                value={data.email}
                                onChange={handleChange}
                                required
                                id="email"
                                placeholder="Enter email"
                                className="p-2 h-8 border rounded-md"
                                readOnly={invitation.is_invitation}
                                style={invitation.is_invitation ? { backgroundColor: '#f5f5f5' } : {}}
                            />
                            {errors.email && <div className="text-red-500 text-sm mt-1">{errors.email}</div>}
                        </div>

                        {/* Password Field */}
                        <div>
                            <label htmlFor="password" className="block mb-1 font-[14px]">
                                Password
                            </label>
                            <Input.Password
                                status={errors.password ? "error" : ""}
                                name="password"
                                value={data.password}
                                onChange={handleChange}
                                id="password"
                                placeholder="Enter password"
                                className="p-2 h-8 border rounded-md"
                            />
                            {errors.password && <div className="text-red-500 text-sm mt-1">{errors.password}</div>}
                        </div>

                        {/* Confirm Password Field */}
                        <div>
                            <label htmlFor="confirm-password" className="block mb-1 font-[14px]">
                                Confirm Password
                            </label>
                            <Input.Password
                                status={errors.password ? "error" : ""}
                                name="password_confirmation"
                                value={data.password_confirmation}
                                onChange={handleChange}
                                id="confirm-password"
                                placeholder="Confirm password"
                                className="p-2 h-8 border rounded-md"
                                required
                            />
                            {errors.password && <div className="text-red-500 text-sm mt-1">{errors.password}</div>}
                        </div>

                        {/* Phone Number */}
                        <div>
                            <label htmlFor="phone" className="block mb-1 font-[14px]">
                                Phone Number
                            </label>
                            <PhoneInput
                                className=""
                                country={countryCode}
                                preferredCountries={["us", "ca"]}
                                value={data.phone}
                                onChange={handlePhoneChange}
                                inputProps={{
                                    name: "phone",
                                    required: true,
                                    id: "phone",
                                    className: `p-2 !pl-11  h-8 rounded-md border ${errors.phone ? " border-[red]" : " border-[#D9D9D9]"}`,
                                    placeholder: "Enter phone number",
                                }}
                                enableSearch
                                containerStyle={{ width: "100%" }}
                                inputStyle={{ width: "100%" }}
                            />
                            {errors.phone && <div className="text-red-500 text-sm mt-1">{errors.phone}</div>}
                        </div>

                        {/* Checkboxes */}
                        <div className="">
                            <Checkbox name="scheduleDemo" checked={data.scheduleDemo} onChange={handleChange}>
                                {" "}
                                I would like to schedule a product demo
                            </Checkbox>
                            {errors.scheduleDemo && <div className="text-red-500 text-sm mt-1">{errors.scheduleDemo}</div>}
                            <br />
                            <Checkbox name="agreeToTerms" checked={data.agreeToTerms} onChange={handleChange}>
                                <span className="text-[14px]">
                                    By continuing, you are agreeing to our
                                    <a href="#" className="text-gray-800 font-bold">
                                        {" "}
                                        Privacy Policy
                                    </a>{" "}
                                    and
                                    <a href="#" className="text-gray-800 font-bold">
                                        {" "}
                                        Terms of <span className="">Service</span>
                                    </a>
                                </span>
                            </Checkbox>
                            {errors.agreeToTerms && <div className="text-red-500 text-sm mt-1">{errors.agreeToTerms}</div>}
                        </div>

                        {/* Buttons */}
                        <Button
                            type="primary"
                            onClick={handleSubmit}
                            loading={processing}
                            block
                            className="h-8 bg-purple-900 flex items-center justify-center"
                        >
                            Sign Up
                        </Button>

                        <div className="flex items-center pt-2">
                            <div className="flex-grow border-t border-[#D9D9D9]"></div>
                            <span className="mx-4 text-[#D9D9D9]">or</span>
                            <div className="flex-grow border-t border-[#D9D9D9]"></div>
                        </div>

                        <Button
                            id="reg_google_btn"
                            block
                            className="h-8 bg-white border-gray-300 flex items-center justify-center"
                            onClick={handleGoogleSignup}
                        >
                            <img src={GoogleIcon} alt="Google Icon" className="w-4 h-4 mr-2" />
                            Sign in with Google
                        </Button>
                    </div>
                    <div className="signuplink text-center mt-4">
                        <span className="text-[14px]">Already have an account? </span>
                        <a href="#" onClick={handleLogin} className="text-[#1890FF] text-[14px]">
                            Sign In
                        </a>
                    </div>
                </div>
            </div>

            {/* Right Section */}
            <div
                id="Signup-page"
                className="right-section w-5/12 flex flex-col bg-purple-900 text-white bg-center"
                style={{
                    backgroundImage: `url(${RegisterBack})`,
                    backgroundSize: `auto`,
                    backgroundRepeat: `no-repeat`
                }}
            >
                <div className="xl:pt-[74px] sm:pt-16 rounded-lg text-center ">
                    <h2 className="text-2xl md2:text-3xl 2xl2:text-4xl font-bold mb-2 md2:mb-3 2xl2:mb-4 text-white">Healthy Parcel</h2>
                    <p className="text-base md2:text-lg 2xl2:text-[20px] mb-4 md2:mb-5 2xl2:mb-6 text-white font-[400] px-6 md2:px-16 2xl2:px-[124px] leading-[24px]">
                        Managing our product information has never been easier since we started using Apimio. We've seen a noticeable boost
                        in customer satisfaction as a result.
                    </p>
                    <p className="font-[600] text-white">KAREENA</p>
                    <p className="font-[400] text-white">CEO Healthy Parcel</p>
                </div>
            </div>
        </div>
    );
};

export default Register;
