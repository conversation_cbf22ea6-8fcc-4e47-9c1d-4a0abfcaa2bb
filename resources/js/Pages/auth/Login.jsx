import React from "react";
import { use<PERSON><PERSON>, <PERSON>, router, Head } from "@inertiajs/react";
import { createRoot } from "react-dom/client";
import { router as Router } from "@inertiajs/core";
import Logo from "../../../../public/v2/images/logo.png";
import RegisterBack from "../../../../public/v2/images/signup-img.png";

import { Input, Button, message } from "antd";
import GoogleIcon from "../../../../public/v2/icons/google-icon.svg";
import axios from "axios";

// Add style tag before the component
const style = `
    @media (max-width: 1000px) {
        .right-section {
            display: none !important;
        }
        .left-section {
            width: 100% !important;
        }
    }
`;

const Login = () => {
    const { data, setData, post, processing, errors } = useForm({
        email: "",
        password: "",
        remember: false,
    });

    const handleSubmit = () => {
        post(route("login"), {
            onSuccess: () => {
                message.success("Login successful!");
            },
            onError: (errors) => {
                if (Object.keys(errors).length === 0) {
                    message.error("Login ID or password is incorrect. Please try again.");
                }
            },
            preserveScroll: true,
        });
    };

    const handleGoogleLogin = () => {
        window.location.href = "/google/redirection";
    };

    const handleRegister = () => {
        Router.visit("/register");
    };

    return (
        <div className="flex h-screen">
            <Head title="Login" />
            <style>{style}</style>
            {/* Left Section */}
            <div className="left-section w-7/12 bg-white flex flex-col justify-center items-center relative">
                {/* Logo */}
                <div className="absolute top-8 left-8">
                    <img src={Logo} alt="Logo" className="w-36" />
                </div>

                <div className="max-w-md w-[450px]">
                    <h1 className="xl:text-[40px] sm:text-3xl font-[700] xl:mb-8 sm:mb-0 text-center text-[#252525]">Welcome Back</h1>

                    <form className="space-y-[10px]">
                        {/* Email Field */}
                        <div>
                            <label htmlFor="email" className="block mb-1 font-[14px]">
                                Business Email
                            </label>
                            <Input
                                status={errors.email ? "error" : ""}
                                id="email"
                                type="email"
                                value={data.email}
                                onChange={(e) => setData("email", e.target.value)}
                                placeholder="Enter email"
                                className="p-2 h-8 border rounded-md"
                                required
                            />
                            {errors.email && <div className="text-red-500 text-sm mt-1">{errors.email}</div>}
                        </div>

                        {/* Password Field */}
                        <div>
                            <label htmlFor="password" className="block mb-1 font-[14px]">
                                Password
                            </label>
                            <Input.Password
                                status={errors.password ? "error" : ""}
                                id="password"
                                value={data.password}
                                onChange={(e) => setData("password", e.target.value)}
                                placeholder="Enter password"
                                className="p-2 h-8 border rounded-md"
                                required
                            />
                            {errors.password && <div className="text-red-500 text-sm mt-1">{errors.password}</div>}
                        </div>

                        <div className="forget text-end">
                            <Link href="/forgot-password" className="text-[#1890FF] text-[14px]">
                                Forgot Password?
                            </Link>
                        </div>

                        {/* Submit Button */}
                        <Button
                            type="primary"
                            onClick={handleSubmit}
                            loading={processing}
                            disabled={processing}
                            block
                            className="h-8 bg-purple-900 flex items-center justify-center"
                        >
                            Login
                        </Button>

                        <div className="flex items-center pt-2">
                            <div className="flex-grow border-t border-[#D9D9D9]"></div>
                            <span className="mx-4 text-[#D9D9D9]">or</span>
                            <div className="flex-grow border-t border-[#D9D9D9]"></div>
                        </div>

                        <Button block className="h-8 bg-white border-gray-300 flex items-center justify-center" onClick={handleGoogleLogin}>
                            <img src={GoogleIcon} alt="Google Icon" className="w-4 h-4 mr-2" />
                            Continue with Google
                        </Button>
                    </form>
                    <div className="signuplink text-center mt-4">
                        <span className="text-[14px]">Don't have an account? </span>
                        <Link href={route("register")} className="text-[#1890FF] text-[14px]">
                            Sign Up
                        </Link>
                    </div>
                </div>
            </div>

            {/* Right Section */}
            <div
                className="right-section w-5/12 bg-purple-900 flex flex-col text-white bg-center"
                style={{
                    backgroundImage: `url(${RegisterBack})`,
                    backgroundSize: `auto`,
                    backgroundRepeat: `no-repeat`
                }}
            >
                <div className="xl:pt-[74px] sm:pt-16 rounded-lg text-center ">
                    <h2 className="xl:text-4xl sm:text-3xl font-bold xl:mb-4 sm:mb-0 text-white">Healthy Parcel</h2>
                    <p className="xl:text-[20px] sm:text-sm xl:mb-6 sm:mb-0 text-white font-[400] px-[124px] sm:px-5 leading-[24px]">
                        Managing our product information has never been easier since we started using Apimio. We've seen a noticeable boost
                        in customer satisfaction as a result.
                    </p>
                    <p className="font-[600]">KAREENA</p>
                    <p className="font-[400]">CEO Healthy Parcel</p>
                </div>
            </div>
        </div>
    );
};

export default Login;

// Render the Login component
const rootElement = document.getElementById("v2-login");

if (rootElement) {
    ReactDOM.createRoot(rootElement).render(<Login />);
}
