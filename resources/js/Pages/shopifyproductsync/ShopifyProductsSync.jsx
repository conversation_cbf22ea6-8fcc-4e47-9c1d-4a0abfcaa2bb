// resources/js/v2/pages/onboarding/ShopifyProductsSync.jsx

import React, { useState, useEffect } from "react";
import { Layout, Button, Form, Input, Modal, Image, message } from "antd";
import RightArrow from "../../../../public/v2/icons/right-arrow.svg";
import ExcelIcon from "../../../../public/v2/icons/excel-icon.svg";
import ShopifyIcon from "../../../../public/v2/icons/shopify-icon.svg";
import BoxIcon from "../../../../public/v2/icons/box-icon.svg";
import SyncProducts from "../../../../public/v2/icons/syncproducts-icon.svg";
import CheckedIcon from "../../../../public/v2/icons/checked-icon.svg";
import ReactDOM from "react-dom/client";
import { router } from "@inertiajs/react";

const { Content } = Layout;

const ShopifyProductsSync = () => {
    // State variables
    const [currentStep, setCurrentStep] = useState(1); // 1: Select Import Method, 2: Sync Products
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [shopifyurl, setShopifyurl] = useState("");
    const [loading, setLoading] = useState(false); // Loading state for sync button
    const [urlError, setUrlError] = useState("");

    // Options for selecting import method
    const options = [
        {
            id: 1,
            name: "Import Your Existing CSV",
            img: ExcelIcon,
            fun: () => {
                router.visit("/products/import/step1");
            },
        },
        {
            id: 2,
            name: "Connect Your Shopify Store to Import Products",
            img: ShopifyIcon,
            fun: () => {
                setIsModalVisible(true);
            },
        },
        {
            id: 3,
            name: "Add Product Manually",
            img: BoxIcon,
            fun: () => {
                router.visit("/products");
            },
        },
    ];

    const currentPath = window.location.pathname;
        // Effect to clear localStorage when user reaches the last step or navigates to /syncproducts
        useEffect(() => {
            console.log("Current step:",currentPath );  // Log current step
    
            if (typeof window !== 'undefined') {
                
    
                // Check if the path contains "/syncproducts" and if we're at the last step
                if (currentPath.includes("/syncproducts") ) {
                    console.log("Completed all steps. Clearing localStorage...");
    
                    // Remove data from localStorage when step 7 (or last step) is completed
                    localStorage.removeItem('onboarding_current_step');
                    localStorage.removeItem('onboarding_form_data');
                    localStorage.removeItem('onboarding_shopify_url');
                } 
            }
        }, [ currentPath]);

    /**
     * Validates a Shopify store URL
     * @param {string} url - The URL to validate
     * @returns {boolean} - Whether the URL is valid
     */
    const validateShopifyUrl = (url) => {
        if (!url || !url.trim()) {
            return false;
        }

        try {
            // Handle URLs with or without protocol
            let cleanUrl = url.trim();

            // Remove protocol if present (http://, https://)
            cleanUrl = cleanUrl.replace(/^https?:\/\//, "");

            // Remove www. if present
            cleanUrl = cleanUrl.replace(/^www\./, "");

            // Remove trailing slash if present
            cleanUrl = cleanUrl.replace(/\/$/, "");

            // Check if the URL matches Shopify store pattern
            const shopifyPattern = /^[a-zA-Z0-9][a-zA-Z0-9-]*\.myshopify\.com$/;
            return shopifyPattern.test(cleanUrl);
        } catch (error) {
            return false;
        }
    };

    /**
     * Validates the current URL input and updates error state
     * Returns true if valid, false if invalid
     */
    const validateInput = () => {
        if (!shopifyurl.trim()) {
            setUrlError("Please enter your Shopify store URL");
            return false;
        }

        if (!validateShopifyUrl(shopifyurl)) {
            setUrlError("Please enter a valid Shopify store URL (e.g., your-store.myshopify.com)");
            return false;
        }

        setUrlError("");
        return true;
    };

    /**
     * Handles the "Connect" action after entering the Shopify URL.
     * Validates the URL and proceeds to the next step.
     */
    const handleConnect = () => {
        if (validateInput()) {
            setIsModalVisible(false);
            setCurrentStep(2);
        }
    };

    /**
     * Handles the "Sync Products Now" button click.
     * Redirects the user to the backend endpoint to initiate the syncing process.
     */
    const handleSyncProducts = () => {
        if (!shopifyurl.trim() || !validateShopifyUrl(shopifyurl)) {
            message.error("Please provide a valid Shopify store URL.");
            return;
        }

        setLoading(true);

        // Process the Shopify URL to remove protocol (http:// or https://)
        const processedShopUrl = shopifyurl.replace(/^https?:\/\//, "");

        // Prepare the query parameters using URLSearchParams for proper encoding
        const params = new URLSearchParams({
            shop: processedShopUrl,
            sync_product: "yes",
            should_not_billed: "1",
        });

        // Construct the full URL
        const syncUrl = `/channel/shopify/install?${params.toString()}`;

        // Redirect to the constructed URL
        window.location.href = syncUrl;

        // Optionally, you can handle loading state if the redirection is not immediate
        // For example, if the backend returns a response before redirecting
        // setLoading(false);
    };

    // Render the initial step: Selecting import method
    const renderStepOne = () => (
        <div className="flex-1 px-[70px] py-[40px] pb-[120px] bg-[#F8F9FA] min-h-screen">
            <h2 className="text-[40px] text-center font-bold pb-[10px]">Start Onboarding Your Products</h2>
            <p className="text-gray-500 text-center mb-6">Yay! You are just one more step away.</p>

            {options.map((opt) => (
                <div
                    key={opt.id}
                    onClick={opt.fun}
                    className="max-w-4xl cursor-pointer mx-auto mb-[20px] p-[18px] bg-[white] rounded-[12px] border border-[#D9D9D9]"
                >
                    <div className="flex items-center">
                        <div className="w-1/2 flex items-center gap-[17px]">
                            <Image src={opt.img} alt={`${opt.name} Icon`} />
                            <p className="font-[700] text-[16px]">{opt.name}</p>
                        </div>
                        <div className="w-1/2 flex justify-end">
                            <Image src={RightArrow} alt="right arrow" />
                        </div>
                    </div>
                </div>
            ))}

            <div className="flex justify-center mt-6 gap-[30px]">
                <Button
                    className="bg-[#740898] text-[14px] rounded-[4px] mt-[40px] font-[400] h-[32px] px-[16px] py-[4px] border border-[#740898] text-[#FFFFFF]"
                    onClick={() => router.visit("/dashboard")}
                >
                    Continue to Dashboard
                </Button>
            </div>

            {/* Modal for entering Shopify URL */}
            <Modal
                centered
                title={<span className="text-[24px] font-[700]">Enter Your Store URL</span>}
                visible={isModalVisible}
                onCancel={() => setIsModalVisible(false)}
                footer={null}
                closable={false}
                width={640}
            >
                <div className="space-y-4 w-[600px]">
                    <Form layout="vertical">
                        <Form.Item label="Shop URL" validateStatus={urlError ? "error" : ""} help={urlError}>
                            <Input
                                value={shopifyurl}
                                onChange={(e) => {
                                    const newValue = e.target.value;
                                    setShopifyurl(newValue);

                                    // Don't validate empty inputs instantly to avoid showing errors as user starts typing
                                    if (!newValue.trim()) {
                                        setUrlError("Please enter your Shopify store URL");
                                    } else if (!validateShopifyUrl(newValue)) {
                                        setUrlError("Please enter a valid Shopify store URL (e.g., your-store.myshopify.com)");
                                    } else {
                                        setUrlError("");
                                    }
                                }}
                                placeholder="your-shop-url.myshopify.com"
                                className="rounded-[2px]"
                                width={640}
                            />
                            <p className="text-[#626262] text-[14px] font-[400] mt-[8px]">
                                Haven't created a store yet? Learn more on&nbsp;
                                <a
                                    href="https://www.shopify.com"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-[#740898] underline"
                                >
                                    shopify.com
                                </a>
                            </p>
                        </Form.Item>
                        <div className="flex justify-end gap-4">
                            <Button onClick={() => setIsModalVisible(false)}>Cancel</Button>
                            <Button
                                type="primary"
                                onClick={handleConnect}
                                disabled={!shopifyurl.trim() || urlError !== ""}
                                className="bg-[#740898] text-[#FFFFFF] border-[#740898]"
                            >
                                Connect
                            </Button>
                        </div>
                    </Form>
                </div>
            </Modal>
        </div>
    );

    // Render the second step: Sync Products
    const renderStepTwo = () => (
        <div className="flex-1 px-70 py-40 pb-120 bg-gray-100 min-h-screen">
            <h2 className="text-4xl text-center font-bold pb-10">Welcome to Apimio!</h2>
            <p className="text-gray-500 text-center mb-6">Your account is successfully created.</p>
            <div className="flex gap-4 max-w-3xl mx-auto justify-center items-center flex-col">
                <img src={SyncProducts} alt="Sync Products Icon" />
                <p className="text-3xl font-semibold leading-tight text-center">
                    Now it's time to sync your Shopify store with Apimio and manage your products seamlessly.
                </p>
            </div>
            <div className="flex justify-center mt-6 gap-8">
                <Button
                    type="primary"
                    className="bg-purple-700 text-sm rounded font-medium h-8 px-4 py-1 border border-purple-700 text-white"
                    onClick={handleSyncProducts}
                    loading={loading}
                >
                    Sync Products Now
                </Button>
            </div>
            <div className="flex justify-center mt-6">
                <button className="text-purple-700 text-sm" onClick={() => router.visit("/dashboard")}>
                    Skip for now
                </button>
            </div>
            <div className="flex max-w-3xl mx-auto items-start mt-5">
                <div className="mt-1">
                    <img src={CheckedIcon} alt="Checked Icon" />
                </div>
                <div className="pl-3">
                    <p className="text-lg font-bold">What happens next?</p>
                    <p className="text-gray-600 text-sm pt-2">
                        Once synced, your Shopify products will automatically import into Apimio. You can then manage your product data
                        efficiently in one place.
                    </p>
                </div>
            </div>
            <div className="flex max-w-3xl mx-auto items-start pt-5">
                <div className="mt-1">
                    <img src={CheckedIcon} alt="Checked Icon" />
                </div>
                <div className="pl-3">
                    <p className="text-lg font-bold">Benefits of Syncing with Apimio</p>
                    <ul className="list-disc pl-4 text-gray-600">
                        <li className="pt-2">
                            <span className="font-semibold">Centralized Product Management:</span> Control all your product data in one
                            platform, making management easier and faster.
                        </li>
                        <li className="pt-2">
                            <span className="font-semibold">Seamless Channel Sync:</span> Ensure consistency by syncing product data across
                            multiple channels, including Shopify and more.
                        </li>
                        <li className="pt-2">
                            <span className="font-semibold">Improve Data Accuracy:</span> Eliminate errors and inconsistencies with Apimio's
                            powerful data validation tools.
                        </li>
                        <li className="pt-2">
                            <span className="font-semibold">Bulk Editing Tools:</span> Make large-scale edits to your product catalog
                            quickly and efficiently.
                        </li>
                        <li className="pt-2">
                            <span className="font-semibold">Automated Updates:</span> Save time by setting up automated product data
                            updates.
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    );

    return (
        <Layout>
            <Content className="flex flex-col md:flex-row">
                {currentStep === 1 && renderStepOne()}
                {currentStep === 2 && renderStepTwo()}
            </Content>
        </Layout>
    );
};

export default ShopifyProductsSync;

const rootElement = document.getElementById("v2-syncproducts");

if (rootElement) {
    ReactDOM.createRoot(rootElement).render(<ShopifyProductsSync />);
}
