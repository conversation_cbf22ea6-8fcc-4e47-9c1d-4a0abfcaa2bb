import React, { useState, useContext, useEffect } from "react";
import { Layout, Button, Form, Input, Select, message } from "antd";
import axios from "axios"; // Import Axios
import { OnboardingContext } from "./OnboardingContext";
const { Content } = Layout;
const { Option } = Select; // Destructure Option from Select
import { post } from "../../axios";
import CurrencySelect from "../components/CurrencySelect";
import LanguageSelect from "../components/LanguageSelect";
import UnitSelect from '../components/WeightUnitSelect'

const OnboardingSix = () => {
    const { handleNext, handleBack } = useContext(OnboardingContext);
    const [loading, setLoading] = useState(false); // Manage loading state
    const [isButtonDisabled, setIsButtonDisabled] = useState(true); // Manage button disabled state
    const [errors, setErrors] = useState({});
    // Use Form instance to control the form
    const [form] = Form.useForm();

    // Define required fields
    const requiredFields = ["name", "language", "currency", "separator", "weightUnit"];

    // Function to check if all required fields are filled and valid
    const checkFormValid = () => {
        // Get current values of required fields
        const fieldsValues = form.getFieldsValue(requiredFields);
        // Check if any required field is empty
        const hasEmpty = requiredFields.some((field) => {
            const value = fieldsValues[field];
            return value === undefined || value === null || value === "";
        });

        // Get current validation errors for required fields
        const fieldsError = form.getFieldsError(requiredFields);
        const hasErrors = fieldsError.some(({ errors }) => errors.length > 0);

        // Set button disabled state
        setIsButtonDisabled(hasErrors || hasEmpty);
    };

    // Handle form submission
    const onFinish = async (values) => {
        setLoading(true); // Start loading
        try {
            const response = await post("organization", values);
            // Assuming the API returns a success status
            message.success("Onboarding details added successfully!");
            handleNext(); // Proceed to the next step
            // Optional: Reset the form after successful submission
            form.resetFields();
            setIsButtonDisabled(true); // Disable button again after reset
        } catch (error) {
            // Set server-side errors
            const serverErrors = error.response?.data?.errors || {};
            setErrors(serverErrors);

            console.error("Error submitting organization details:", error);
            message.error(error.response?.data?.message || "Failed to add organization details. Please try again.");
        } finally {
            setLoading(false); // Stop loading
        }
    };
    // Handle form submission failure
    const onFinishFailed = (errorInfo) => {
        console.log("Failed:", errorInfo);
        message.error("Please check the form for errors and try again.");
    };

    // useEffect to perform an initial validation check when the component mounts
    useEffect(() => {
        checkFormValid();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Handle field value changes
    const handleValuesChange = (changedValues, allValues) => {
        checkFormValid();
    };

    // Handle organization name input change
    const handleNameChange = (e) => {
        // Store a reference to the current active element (the input)
        const activeElement = document.activeElement;

        // Clear the name error when the field is being typed in
        if (errors.name) {
            setErrors((prevErrors) => {
                const newErrors = { ...prevErrors };
                delete newErrors.name;
                return newErrors;
            });
        }

        // Don't use form.setFieldsValue as it causes focus issues
        // Let Ant Design handle the value change naturally
    };

    console.log("errors in onboarding six", errors);

    return (
        <div className="flex-1 px-[70px] py-[40px] pb-[120px] bg-[#F8F9FA]">
            <h2 className="xl:text-[40px] sm:text-xl text-center font-bold pb-[10px]">Add Organization Details</h2>
            <p className="text-gray-500 text-center mb-6">Please enter the following information regarding your Apimio workspace:</p>
            <div className="p-5">
                <div className="max-w-lg mx-auto p-6 bg-white rounded-lg border border-[#D9D9D9]">
                    <Form
                        form={form} // Associate the form instance
                        layout="vertical"
                        className="space-y-4"
                        onFinish={onFinish}
                        onFinishFailed={onFinishFailed}
                        onValuesChange={handleValuesChange}
                        initialValues={{
                            language: "EN-US", // Default to first language option
                            currency: "USD", // Default to first currency option
                            separator: ".", // Default to first separator option
                            weightUnit: "oz", // Default to first weight unit option
                        }}
                    >
                        {/* Organization Name */}
                        <Form.Item
                            className="text-[14px] font-[400]"
                            label="Organization Name"
                            name="name"
                            rules={[{ required: true, message: "Please input the organization name!" }]}
                            validateStatus={errors.name ? "error" : null}
                            help={errors.name ? errors.name : null}
                        >
                            <Input
                                placeholder="Organization Name"
                                onChange={handleNameChange}
                                className="rounded-lg border-gray-300 focus:ring-indigo-500 focus:border-indigo-500"
                            />
                        </Form.Item>

                        {/* Language */}
                        <Form.Item
                            className="text-[14px] font-[400]"
                            label="Language"
                            name="language"
                            rules={[{ required: true, message: "Please select a language!" }]}
                        >
                             <LanguageSelect style={{ width: "100%" }} />
                        </Form.Item>

                        {/* Currency */}
                        <Form.Item
                            className="text-[14px] font-[400]"
                            label="Currency"
                            name="currency"
                            rules={[{ required: true, message: "Please select a currency!" }]}
                        >
                            <CurrencySelect style={{ width: "100%" }} />
                        </Form.Item>

                        {/* Currency Decimal Separator */}
                        <Form.Item
                            className="text-[14px] font-[400]"
                            label="Currency Decimal Separator"
                            name="separator"
                            rules={[{ required: true, message: "Please select a decimal separator!" }]}
                        >
                            <Select
                                placeholder="Select decimal separator"
                                className="rounded-lg border-gray-300 focus:ring-indigo-500 focus:border-indigo-500"
                            >
                                <Option value=".">Point</Option>
                                <Option value=",">Comma</Option>
                            </Select>
                        </Form.Item>

                        {/* Weight Unit */}
                        <Form.Item
                            className="text-[14px] font-[400]"
                            label="Weight Unit"
                            name="weightUnit"
                            rules={[{ required: true, message: "Please select a weight unit!" }]}
                        >
                            <UnitSelect style={{ width: "100%" }} />
                        </Form.Item>

                        {/* Submit Button */}
                        <Form.Item>
                            <div className="flex justify-center mt-6 gap-6">
                                <Button
                                    type="primary"
                                    className="bg-[#740898] rounded-[4px] font-[400] h-[40px] w-[120px] border border-[#740898] text-[#FFFFFF]"
                                    loading={loading} // Show loading indicator
                                    htmlType="submit" // Automatically handle form submission
                                    disabled={isButtonDisabled} // Disable button if form is invalid
                                >
                                    Continue
                                </Button>
                                <button className="hidden text-[#740898] text-sm cursor-pointer" onClick={handleNext}>
                                    Skip for now
                                </button>
                            </div>
                        </Form.Item>
                    </Form>
                </div>
            </div>
        </div>
    );
};

export default OnboardingSix;
