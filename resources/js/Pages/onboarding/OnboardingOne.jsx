import React, { useState, useContext, useEffect } from "react";
import { Layout, Card, Button } from "antd";
import { OnboardingContext } from "./OnboardingContext";
import ElectronicsIcon from "../../../../public/v2/icons/electronics.svg";
import FashionIcon from "../../../../public/v2/icons/fashion.svg";
import FoodIcon from "../../../../public/v2/icons/food.svg";
import DiyIcon from "../../../../public/v2/icons/diy.svg";
import ChairIcon from "../../../../public/v2/icons/chair.svg";
import MediaIcon from "../../../../public/v2/icons/media.svg";
import BeautyIcon from "../../../../public/v2/icons/beauty.svg";
import TobacooIcon from "../../../../public/v2/icons/tobacco.svg";
import ToysIcon from "../../../../public/v2/icons/toys.svg";
import OthersIcon from "../../../../public/v2/icons/others.svg";

const { Content } = Layout;

const OnboardingOne = () => {
    const { handleNext, updateFormData, formData } = useContext(OnboardingContext);
    const [selectedIndustry, setSelectedIndustry] = useState("");

    const industries = [
        { label: "Electronics", icon: ElectronicsIcon },
        { label: "Fashion & Apparel", icon: FashionIcon },
        { label: "Food & Beverage", icon: FoodIcon },
        { label: "DIY & Hardware", icon: DiyIcon },
        { label: "Furniture", icon: ChairIcon },
        { label: "Media", icon: MediaIcon },
        { label: "Beauty & Personal Care", icon: BeautyIcon },
        { label: "Tobacco Products", icon: TobacooIcon },
        { label: "Toys & Hobbies", icon: ToysIcon },
        { label: "Other", icon: OthersIcon },
    ];

    // Pre-fill selectedIndustry from context if available
    useEffect(() => {
        if (formData.product_category) {
            setSelectedIndustry(formData.product_category);
        }
    }, [formData.product_category]);

    const handleSelection = (industry) => {
        setSelectedIndustry(industry);
    };

    const handleNextClick = () => {
        if (selectedIndustry) {
            updateFormData("product_category", selectedIndustry);
        }
        handleNext();
    };

    const handleSkip = () => {
        // Optionally, set a default value or leave it undefined
        updateFormData("product_category", null);
        handleNext();
    };

    return (
        <div className="flex-1 px-4 sm:px-6 md:px-[40px] lg:px-[70px] py-[20px] md:py-[40px] pb-[60px] md:pb-[120px] bg-[#F8F9FA]">
            <h2 className="text-[28px] md:text-[34px] lg:text-[40px] text-center font-bold pb-[10px]">Select your industry</h2>
            <p className="text-gray-500 text-center mb-4 md:mb-6">This helps us give you more relevant recommendations</p>
            <div className="grid grid-cols-1 sm:grid-cols-2 2xl:grid-cols-4 gap-[15px] md:gap-[20px]">
                {industries.map((industry) => (
                    <Card
                        key={industry.label}
                        className={`border rounded-[12px] flex items-end justify-center h-36 sm:h-40 md:h-44 text-center cursor-pointer ${
                            selectedIndustry === industry.label ? "bg-[#740898]" : "hover:border-[#740898]"
                        }`}
                        onClick={() => handleSelection(industry.label)}
                        hoverable
                    >
                        <div className="flex flex-col items-center justify-center gap-[12px] md:gap-[20px]">
                            <img src={industry.icon} alt={`${industry.label} icon`} className="w-[30px] md:w-auto" />
                            <div
                                className={`font-normal text-sm md:text-base ${
                                    selectedIndustry === industry.label ? "text-white font-[700]" : "text-[#252525]"
                                }`}
                            >
                                {industry.label}
                            </div>
                        </div>
                    </Card>
                ))}
            </div>

            <div className="flex justify-end mt-4 md:mt-6 gap-[15px] md:gap-[20px]">
                <Button
                    type="primary"
                    onClick={handleNextClick}
                    disabled={!selectedIndustry} // Disable if no selection
                >
                    Next
                </Button>
                <button className="text-[#740898] text-sm cursor-pointer" onClick={handleSkip}>
                    Skip for now
                </button>
            </div>
        </div>
    );
};

export default OnboardingOne;
