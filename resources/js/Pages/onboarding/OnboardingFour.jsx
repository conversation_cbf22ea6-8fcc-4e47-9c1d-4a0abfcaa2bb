import React, { useState, useContext, useEffect } from "react";
import { Layout, Button, Radio, Row, Col, Input } from "antd";
import { OnboardingContext } from "./OnboardingContext";

const { Content } = Layout;

const OnboardingFour = () => {
    const { handleNext, handleBack, formData, updateFormData } = useContext(OnboardingContext);
    const [selectedRole, setSelectedRole] = useState(""); // State to store the selected role

    const radioOptions = [
        { label: "Product/E-commerce Manager" },
        { label: "External Consultant/Freelancer" },
        { label: "IT/Operations Manager" },
        { label: "Marketing Specialist" },
        { label: "Data Entry Specialist" },
        { label: "Owner or Founder" },

        { label: "Other" },
    ];

    // Pre-fill selectedRole from context if available
    useEffect(() => {
        if (formData.current_role) {
            setSelectedRole(formData.current_role);
        }
    }, [formData.current_role]);

    const handleRadioChange = (e) => {
        setSelectedRole(e.target.value); // Set the selected role
    };

    // Function to handle "Next" click
    const handleNextClick = () => {
        if (selectedRole) {
            updateFormData("current_role", selectedRole);
        }
        handleNext();
    };

    // Function to handle "Skip for now" click
    const handleSkip = () => {
        updateFormData("current_role", "");
        handleNext();
    };

    return (
        <div className="flex-1 px-[70px] py-[40px] pb-[120px] bg-[#F8F9FA]">
            <h2 className="xl:text-[40px] sm:text-xl text-center font-bold pb-[10px]">Select Your Role</h2>
            <p className="text-gray-500 text-center mb-6">This helps us to easily customize order size to match your exact needs</p>
            <div className="p-5">
                <Row gutter={[20, 20]}>
                    {radioOptions.map((option, index) => (
                        <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={6} key={index}>
                            <div
                                className="bg-white p-4 rounded-[12px] flex items-start cursor-pointer"
                                onClick={() => setSelectedRole(option.label)}
                            >
                                <Radio
                                    className="text-[#252525] text-[16px]"
                                    value={option.label} // Set the value to the label
                                    checked={selectedRole === option.label} // Check if this option is selected
                                    onChange={handleRadioChange} // Handle selection
                                >
                                    <span
                                        className={`pl-[4px] pt-[2px] ${
                                            selectedRole === option.label ? "text-[#740898] font-semibold" : ""
                                        }`}
                                    >
                                        {option.label}
                                    </span>
                                </Radio>
                            </div>
                        </Col>
                    ))}
                </Row>
            </div>

            <div className="flex justify-end mt-6 gap-[20px]">
                <Button onClick={handleBack}>Back</Button>
                <Button
                    type="primary"
                    onClick={handleNextClick}
                    disabled={!selectedRole} // Disable if no selection is made
                >
                    Next
                </Button>
                <button className="text-[#740898] text-sm cursor-pointer" onClick={handleSkip}>
                    Skip for now
                </button>
            </div>
        </div>
    );
};

export default OnboardingFour;
