import React, { useEffect, useState } from "react";
import { Layout } from "antd";
import Logo from "../../../../public/v2/images/logo.png";
import axios from "axios";
import { Link, usePage } from "@inertiajs/react";

const { Header } = Layout;

const ObHeader = () => {
    const { props, component, url } = usePage();
    const { auth, organization } = props;
    const user = auth?.user;

    return (
        <Header className="bg-white px-[30px] border border-[#D9D9D9] flex items-center justify-between h-[80px]">
            <div className="text-2xl font-bold text-purple-600">
                <img src={Logo} alt="Logo" className="w-36" />
            </div>
            <div className="flex items-center">
                <div className="w-8 h-8 rounded-sm bg-[#740898] m-[6px] flex items-center justify-center text-white uppercase">
                    {user.fname && user.lname
                        ? user.fname[0] + user.lname[0]
                        : user.fname
                        ? user.fname[0]
                        : user.lname
                        ? user.lname[0]
                        : ""}
                </div>
                <div className="text-sm font-[700] text-black mr-4 capitalize">
                    {user.fname && user.lname ? user.fname + " " + user.lname : user.fname ? user.fname : "User"}
                </div>
            </div>
        </Header>
    );
};

export default ObHeader;
