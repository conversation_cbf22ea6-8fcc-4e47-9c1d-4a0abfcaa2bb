// resources/js/v2/pages/onboarding/OnboardingTen.jsx

import React, { useState, useContext } from "react";
import { Layout, Button, message } from "antd";
import axios from "axios";
import { OnboardingContext } from "./OnboardingContext";
import ExcelIcon from "../../../../public/v2/icons/excel-icon.svg";
import ShopifyIcon from "../../../../public/v2/icons/shopify-icon.svg";
import BoxIcon from "../../../../public/v2/icons/box-icon.svg";
import SyncProducts from "../../../../public/v2/icons/syncproducts-icon.svg";
import CheckedIcon from "../../../../public/v2/icons/checked-icon.svg";

const { Content } = Layout;

const OnboardingTen = () => {
    const { handleNext, shopifyurl } = useContext(OnboardingContext);
    const [loading, setLoading] = useState(false); // Loading state for the button

    /**
     * Function to handle the "Sync Products Now" button click.
     * It sends a GET request to the Laravel backend to initiate the syncing process.
     */
    const handleSyncProducts = async () => {
        // Validate that the Shopify URL is provided
        if (!shopifyurl) {
            message.error("Shopify URL is missing. Please provide a valid Shopify URL.");
            return;
        }

        setLoading(true); // Start loading indicator

        // Process the Shopify URL to remove protocol (http:// or https://)
        const processedShopUrl = shopifyurl.replace(/^https?:\/\//, "");

        // Prepare the payload to send to the backend
        const data = {
            shop: processedShopUrl,
            sync_product: "yes", // Indicates that products should be synced
            should_not_billed: 1, // Additional parameter as per backend requirements
        };

        try {
            // Send POST request using Axios
            const response = await axios.get("/channel/shopify/install", data, {
                headers: {
                    Accept: "application/json", // Expect JSON response
                },
            });

            // Check if the response is successful
            if (response.status === 200) {
                // Display success message to the user
                message.success(response.data.message || "Shopify store connected successfully!");
                // Proceed to the next onboarding step
                handleNext();
            } else {
                // Handle unexpected successful responses
                message.error(response.data.message || "An unexpected response was received. Please try again.");
            }
        } catch (error) {
            // Handle errors from the server or network issues
            if (error.response) {
                // Server responded with a status other than 2xx
                message.error(error.response.data.message || "An error occurred. Please try again.");
            } else if (error.request) {
                // Request was made but no response received
                message.error("Network error. Please check your connection and try again.");
            } else {
                // Other errors
                message.error("An unexpected error occurred.");
            }
            console.error("Error syncing Shopify products:", error);
        } finally {
            setLoading(false); // Stop loading indicator
        }
    };

    // Sample data representing different organization options
    const organizations = [
        { id: 1, name: "Import Your Existing CSV", img: ExcelIcon },
        { id: 2, name: "Connect Your Shopify Store to Import Products", img: ShopifyIcon },
        { id: 3, name: "Add Product Manually", img: BoxIcon },
    ];

    return (
        <Layout>
            <Content className="flex flex-col md:flex-row">
                <div className="flex-1 px-70 py-40 pb-120 bg-gray-100 min-h-screen">
                    <h2 className="text-4xl text-center font-bold pb-10">Welcome to Apimio!</h2>
                    <p className="text-gray-500 text-center mb-6">Your account is successfully created.</p>
                    <div className="flex gap-4 max-w-3xl mx-auto justify-center items-center flex-col">
                        <img src={SyncProducts} alt="Sync Products Icon" />
                        <p className="text-3xl font-semibold leading-tight text-center">
                            Now it's time to sync your Shopify store with Apimio and manage your products seamlessly.
                        </p>
                    </div>
                    <div className="flex justify-center mt-6 gap-8">
                        <Button
                            type="primary"
                            className="bg-purple-700 text-sm rounded font-medium h-8 px-4 py-1 border border-purple-700 text-white"
                            onClick={handleSyncProducts}
                            loading={loading} // Show loading spinner when processing
                        >
                            Sync Products Now
                        </Button>
                    </div>
                    <div className="flex justify-center mt-6">
                        <button className="text-purple-700 text-sm cursor-pointer" onClick={handleNext}>
                            Skip for now
                        </button>
                    </div>
                    <div className="flex max-w-3xl mx-auto items-start mt-5">
                        <div className="mt-1">
                            <img src={CheckedIcon} alt="Checked Icon" />
                        </div>
                        <div className="pl-3">
                            <p className="text-lg font-bold">What happens next?</p>
                            <p className="text-gray-600 text-sm pt-2">
                                Once synced, your Shopify products will automatically import into Apimio. You can then manage your product
                                data efficiently in one place.
                            </p>
                        </div>
                    </div>
                    <div className="flex max-w-3xl mx-auto items-start pt-5">
                        <div className="mt-1">
                            <img src={CheckedIcon} alt="Checked Icon" />
                        </div>
                        <div className="pl-3">
                            <p className="text-lg font-bold">Benefits of Syncing with Apimio</p>
                            <ul className="list-disc pl-4 text-gray-600">
                                <li className="pt-2">
                                    <span className="font-semibold">Centralized Product Management:</span> Control all your product data in
                                    one platform, making management easier and faster.
                                </li>
                                <li className="pt-2">
                                    <span className="font-semibold">Seamless Channel Sync:</span> Ensure consistency by syncing product data
                                    across multiple channels, including Shopify and more.
                                </li>
                                <li className="pt-2">
                                    <span className="font-semibold">Improve Data Accuracy:</span> Eliminate errors and inconsistencies with
                                    Apimio’s powerful data validation tools.
                                </li>
                                <li className="pt-2">
                                    <span className="font-semibold">Bulk Editing Tools:</span> Make large-scale edits to your product
                                    catalog quickly and efficiently.
                                </li>
                                <li className="pt-2">
                                    <span className="font-semibold">Automated Updates:</span> Save time by setting up automated product data
                                    updates.
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </Content>
        </Layout>
    );
};

export default OnboardingTen;
