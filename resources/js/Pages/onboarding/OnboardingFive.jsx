import React, { useState, useContext, useEffect } from "react";
import { Layout, Button, Checkbox, Row, Col, Input, message, Spin } from "antd";
import { OnboardingContext } from "./OnboardingContext";
import axios from "axios";

const { Content } = Layout;

const OnboardingFive = () => {
    const { handleNext, handleBack, formData, updateFormData } = useContext(OnboardingContext);
    const [checkedItems, setCheckedItems] = useState({});
    const [otherDetail, setOtherDetail] = useState("");
    const [loading, setLoading] = useState(false); // Loading state
    const [error, setError] = useState(null); // Error state

    const checkboxOptions = [
        { label: "Centralize product information in one platform" },
        { label: "Ensure data accuracy across channels" },
        { label: "Reduce time spent on manual data entry and bulk edits" },
        { label: "Streamline team collaboration" },
        { label: "Manage supplier data more efficiently" },
        { label: "Scale product information for rapid expansion" },
        { label: "Optimize product listings for better sales performance" },
        { label: "Enhance inventory management across multiple stores" },
        { label: "Other" },
    ];

    // Pre-fill checkedItems and otherDetail from context if available
    useEffect(() => {
        if (formData.purpose_for_pim) {
            const initialChecked = {};
            formData.purpose_for_pim.split(";").forEach((detail) => {
                if (detail.startsWith("Other:")) {
                    initialChecked["Other"] = true;
                    setOtherDetail(detail.replace("Other:", "").trim());
                } else {
                    initialChecked[detail] = true;
                }
            });
            setCheckedItems(initialChecked);
        }
    }, [formData.purpose_for_pim]);

    const handleCheckboxChange = (e, label) => {
        setCheckedItems((prevState) => ({
            ...prevState,
            [label]: e.target.checked,
        }));

        if (label === "Other" && !e.target.checked) {
            setOtherDetail("");
        }
    };

    const handleNextClick = async () => {
        setLoading(true);
        setError(null);
        // Extract selected organization details
        const selectedLabels = Object.keys(checkedItems).filter((label) => checkedItems[label]);

        let selectedDetails = selectedLabels
            .map((label) => {
                if (label === "Other") {
                    return `Other: ${otherDetail}`;
                }
                return label;
            })
            .join(";");

        // Construct the updated form data
        const updatedFormData = { ...formData, purpose_for_pim: selectedDetails };

        try {
            // Perform the PUT request with the updated payload
            const response = await axios.put("/api/2024-12/hubspot/1", updatedFormData);

            if (response.status === 200 || response.status === 204) {
                // Update context with selected organization details
                updateFormData("purpose_for_pim", selectedDetails);
                // Proceed to next step
                handleNext();
                message.success("Onboarding details saved successfully!");
            } else {
                // Handle unexpected success responses
                setError("Unexpected response from the server. Please try again.");
                message.error("Unexpected response from the server. Please try again.");
            }
        } catch (err) {
            // Handle error responses
            console.error(err);
            setError("Failed to save onboarding details. Please try again.");
            message.error("Failed to save onboarding details. Please try again.");
        } finally {
            setLoading(false);
        }
    };

    const handleSkip = () => {
        // Update context with empty organization details
        updateFormData("purpose_for_pim", "");

        // Proceed to next step
        handleNext();
    };

    return (
        <div className="flex-1 px-[70px] py-[40px] pb-[120px] bg-[#F8F9FA]">
            <h2 className="xl:text-[40px] sm:text-xl text-center font-bold pb-[10px]">What Are Your Main Objectives with Apimio?</h2>
            <p className="text-gray-500 text-center mb-6">
                Help us tailor your onboarding experience by providing your company details, industry, team size, and business goals.
            </p>
            <div className="p-5">
                <Row gutter={[20, 20]}>
                    {checkboxOptions.map((option, index) => (
                        <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={12} key={index}>
                            <div
                                className="bg-white p-4 rounded-[12px] flex items-start cursor-pointer"
                                onClick={(e) => {
                                    // Prevent triggering checkbox twice
                                    if (e.target.tagName !== "INPUT") {
                                        handleCheckboxChange({ target: { checked: !checkedItems[option.label] } }, option.label);
                                    }
                                }}
                            >
                                <Checkbox
                                    className="text-[#252525] text-[16px]"
                                    checked={checkedItems[option.label] || false}
                                    onChange={(e) => handleCheckboxChange(e, option.label)}
                                ></Checkbox>
                                <span
                                    className={`pl-[14px] pt-[2px] text-[#252525] text-[16px] ${
                                        checkedItems[option.label] ? "text-[#740898] font-semibold" : ""
                                    }`}
                                >
                                    {option.label}
                                </span>
                            </div>
                        </Col>
                    ))}
                </Row>
            </div>

            {/* If "Other" is checked, show an input field */}
            {checkedItems["Other"] && (
                <div className="p-5">
                    <Input placeholder="Please specify" value={otherDetail} onChange={(e) => setOtherDetail(e.target.value)} />
                </div>
            )}

            <div className="flex justify-end mt-6 gap-[20px]">
                <Button onClick={handleBack} disabled={loading}>
                    Back
                </Button>
                <Button
                    type="primary"
                    onClick={handleNextClick}
                    disabled={Object.keys(checkedItems).filter((key) => checkedItems[key]).length === 0 || loading} // Disable if no selection is made or loading
                >
                    {loading ? <Spin size="small" /> : "Next"}
                </Button>
                <button className="text-[#740898] text-sm cursor-pointer" onClick={handleSkip} disabled={loading}>
                    Skip for now
                </button>
            </div>
        </div>
    );
};

export default OnboardingFive;
