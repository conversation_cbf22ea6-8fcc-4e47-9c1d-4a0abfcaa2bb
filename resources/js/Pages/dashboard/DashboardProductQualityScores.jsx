import React, { useState, useEffect } from "react";
import ProductQualityScores from "../components/ProductQualityScores";
import { get } from "../../axios";

const DashboardProductQualityScores = () => {
    const [qualityScores, setQualityScores] = useState(null);
    console.log("product qualityScores", qualityScores);

    useEffect(() => {
        fetchQualityScores();
    }, []);

    const fetchQualityScores = async () => {
        try {
            const response = await get("product-quality-score");
            setQualityScores(response.productQualityScore);
        } catch (error) {
            console.error("Error fetching quality scores:", error);
        }
    };
    const mockScores = {
        good: 10,
        fair: 5,
        bad: 2,
        status: "Successfully synced and up to date!",
    };
    return (
        <ProductQualityScores
            title="Product Quality Scores"
            description="Track Product Quality Metrics for Your Products."
            scores={qualityScores}
        />
    );
};

export default DashboardProductQualityScores;
