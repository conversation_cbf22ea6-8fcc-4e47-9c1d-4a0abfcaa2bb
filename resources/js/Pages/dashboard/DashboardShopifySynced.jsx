import React, { useState, useEffect } from "react";
import { Select, Button, Row, Col } from "antd";
import Doughnut<PERSON>hart from "../components/DoughnutChart";
import ShopifyIcon from "../../../../public/v2/icons/shopify-icon.svg";

const DashboardShopifySynced = ({ shopifySyncData = [] }) => {
    const [selectedStore, setSelectedStore] = useState("");
    const [storeOptions, setStoreOptions] = useState([]);

    useEffect(() => {
        if (shopifySyncData.length > 0) {
            // Get the list of stores from the data
            const stores = shopifySyncData.map((store) => store.name);
            setStoreOptions(stores.map((store) => ({ value: store, label: store })));
            // Select the first store by default
            setSelectedStore(stores[0]);
        }
    }, [shopifySyncData]);

    // Get current store's data
    const currentStoreData = shopifySyncData.find((store) => store.name === selectedStore) || {
        id: 0,
        name: "",
        not_synced: 0,
        synced: 0,
        in_process: 0,
    };

    const colors = {
        synced: "#740898",
        updateAvailable: "#D256FC",
        inProcess: "#E8A4FF",
    };

    // Total number of products for percentage calculation
    const totalProducts = currentStoreData.synced + currentStoreData.not_synced + currentStoreData.in_process;

    // Determine status message based on data
    let statusMessage = "No data available";
    if (totalProducts > 0) {
        if (currentStoreData.not_synced > 0) {
            statusMessage = "Updates available for sync";
        } else if (currentStoreData.in_process > 0) {
            statusMessage = "Products are being synced";
        } else {
            statusMessage = "Successfully synced and up to date!";
        }
    }

    const currentScores = {
        synced: currentStoreData.synced || 0,
        updateAvailable: currentStoreData.not_synced || 0,
        inProcess: currentStoreData.in_process || 0,
        status: statusMessage,
    };
    const mockScores = {
        synced: 10,
        updateAvailable: 5,
        inProcess: 2,
        status: "Successfully synced and up to date!",
    };
    const cards = [
        {
            index: 1,
            value: currentScores.synced,
            quality: "Synced",
            description: "Products are synced with Shopify.",
            buttonLabel: "View",
            color: colors.synced,
        },
        {
            index: 2,
            value: currentScores.updateAvailable,
            quality: "Update Available",
            description: "Products are updated and not synced to Shopify.",
            buttonLabel: "View",
            color: colors.updateAvailable,
        },
        {
            index: 3,
            value: currentScores.inProcess,
            quality: "In Process",
            description: "Products are in process to synced with Shopify",
            buttonLabel: "View",
            color: colors.inProcess,
        },
    ];

    const handleStoreChange = (value) => {
        setSelectedStore(value);
    };

    return (
        <div className="p-5 rounded-[16px] bg-white">
            <div className="flex justify-between items-center mb-0">
                <h2 className="text-xl font-semibold text-[#252525]">Shopify sync status</h2>
                {storeOptions.length > 0 && (
                    <Select value={selectedStore} onChange={handleStoreChange} style={{ width: 120 }} options={storeOptions} />
                )}
            </div>
            <p className="text-gray-600 mb-0">Your Shopify store is {currentScores.status}</p>

            <div className="py-5">
                <Row className="flex gap-5">
                    <Col flex="1" className="flex items-center justify-center">
                        <div className="relative flex items-center justify-center">
                            <DoughnutChart
                                scores={currentScores}
                                colors={[colors.synced, colors.updateAvailable, colors.inProcess]}
                                dataKeys={{
                                    first: "synced",
                                    second: "updateAvailable",
                                    third: "inProcess",
                                }}
                                centerLabel={{
                                    firstLine: "Shopify",
                                    secondLine: "Sync",
                                }}
                            />
                            <div className="absolute inset-0 flex items-center justify-center">
                                <img src={ShopifyIcon} alt="Shopify Logo" className="w-15 h-15" />
                            </div>
                        </div>
                    </Col>
                    {cards.map((card) => (
                        <Col flex="1" key={card.index}>
                            <div className="h-[168px] p-5 text-center bg-white flex flex-col rounded-[12px] items-center justify-center border border-[#EBEBEB]">
                                <p className="text-[#252525] text-[40px] font-[700]">{card.value}</p>
                                <p className="text-[16px] font-[600]" style={{ color: card.color }}>
                                    {card.quality}
                                </p>
                                <p className="text-[#626262] font-normal text-[10px]">{card.description}</p>
                                <Button
                                    className="text-[14px] font-normal rounded-[16px]"
                                    style={{ borderColor: card.color, color: card.color }}
                                >
                                    {card.buttonLabel}
                                </Button>
                            </div>
                        </Col>
                    ))}
                </Row>
            </div>
        </div>
    );
};

export default DashboardShopifySynced;
