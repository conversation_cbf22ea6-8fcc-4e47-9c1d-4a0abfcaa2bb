import React from "react";
import { <PERSON>, Col, <PERSON><PERSON>, Progress } from "antd";
import UpArrow from "../../../../public/v2/icons/uparrow-icon.svg";
import ShopifySync from "../../../../public/v2/icons/shopifysync-icon.svg";
import Info from "../../../../public/v2/icons/info-icon.svg";
import DoughnutChart from "../components/DoughnutChart";

const DashboardSeoQualityScore = () => {
    const cards = [
        {
            index: 1,
            title: "URL Slug",
            percent: 80,
        },
        {
            index: 2,
            title: "SEO Title",
            percent: 50,
        },
        {
            index: 3,
            title: "SEO Description",
            percent: 70,
        },
    ];
    const twoColors = {
        "0%": "#740898",
        "100%": "#D256FC",
    };
    return (
        <div className="p-5 w-full">
            <p className="text-[#252525] font-[600] text-[18px]">SEO Quality Score</p>
            <p className="text-[#626262] font-normal text-[14px]">Track SEO Quality Metrics for Your Products.</p>
            <div className="pt-2">
                <Row className="flex gap-5">
                    {cards.map((card) => (
                        <Col span={24} key={card.index}>
                            <div className="h-[50px] p-5 text-start bg-white flex flex-col rounded-[12px] justify-center border border-[#EBEBEB]">
                                <Row>
                                    <Col span={19}>
                                        <p className="text-[#252525] font-[600] text-[14px]">{card.title}</p>
                                        <Progress
                                            percent={card.percent}
                                            strokeColor={twoColors}
                                            format={(percent) => `${card.percent}/100`}
                                        />
                                    </Col>
                                    <Col span={5}>
                                        <div className="flex items-center justify-end h-full">
                                            <Button className="text-[14px] font-normal rounded-[16px] border">
                                                <img src={UpArrow} alt="up arrow" /> Improve
                                            </Button>
                                        </div>
                                    </Col>
                                </Row>
                            </div>
                        </Col>
                    ))}
                </Row>
            </div>
        </div>
    );
};

export default DashboardSeoQualityScore;
