import React, { useState, useEffect } from "react";
import ImageQualityScores from "../components/ImageQualityScores";
import { get } from "../../axios";

const DashboardImageQualityScores = () => {
    const [qualityScores, setQualityScores] = useState(null);

    useEffect(() => {
        fetchQualityScores();
    }, []);

    const fetchQualityScores = async () => {
        try {
            const response = await get("image-quality-score");
            setQualityScores(response.imageQualityScore);
        } catch (error) {
            console.error("Error fetching quality scores:", error);
        }
    };

    return (
        <ImageQualityScores
            title="Images Quality Scores"
            description="Track image Quality Metrics for Your Products."
            scores={qualityScores}
        />
    );
};

export default DashboardImageQualityScores;
