@extends('layouts.app_new')

@section('titles', 'Inventory')
@section('content')
    <link href="https://unpkg.com/gijgo@1.9.13/css/gijgo.min.css" rel="stylesheet" type="text/css" />

    <x-products.edit-product-header :product="$product" :buttons="false" :version="$version" />
    <x-products.edit-product-header-navs :product="$product" :version="$version" />
    <x-products.page-title name="Inventory stock quantity"
        description="Please update the inventory stock quantities for each location." links="true" button="false">
    </x-products.page-title>


        <div class="row inventory">
            <div class="col-3">
                <label for="storeSelect" class="block mb-2">Select a Store:</label>
                <select class="form-select" id="storeSelect" name="storeSelect">
                    @foreach ($channels as $data)
                        @php
                            $isSelected =
                                isset($_POST['storeSelect']) && $_POST['storeSelect'] == "store-id-{$data->id}"
                                    ? 'selected'
                                    : '';
                        @endphp
                        <option value="store-id-{{ $data->id }}" {{ $isSelected }}>{{ $data->name }}</option>
                    @endforeach
                </select>
            </div>

            <div class="col-3 ms-auto">
                <div class="col-12 my-2 d-flex justify-content-end">
                    <button id="saveButton" class="btn btn-primary">Save</button>
                </div>
            </div>

            <div class="col-12">
                <div>
                    @foreach ($channels as $outerIndex => $data)
                        <div class="store-variants  mt-4 @if ($outerIndex != 0) d-none @endif"
                            id="store-id-{{ $data->id }}">
                            <div>
                                @if (count($data->products->first()->variants) > 1)
                                    @include('products.inventories.variant-inventories', [
                                        'variants' => $data->products->first()->variants,
                                        'outerIndex' => $outerIndex,
                                        'channel' => $data,
                                        'products' => $data->products,
                                    ])
                                @else
                                    @include('products.inventories.product-inventories', [
                                        'variant' => $data->products->first()->variants->first(),
                                        'products' => $data->products,
                                        'outerIndex' => $outerIndex,
                                        'channel' => $data,
                                    ])
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>

            </div>



        </div>
        </div>
@endsection





@push('footer_scripts')
    <script src="{{ asset('js/delete_product.js') }}"></script>
    <script>
        $(document).ready(function() {

            $(document).on('keyup', '.quantity-adjustable', function() {
                this.value = parseInt(this.value, 10) || '';
                var data = {
                    variant_id: this.getAttribute('data-variant-id'),
                    location_id: this.getAttribute('data-location-id'),
                    product_id: this.getAttribute('data-product-id'),
                    quantity: this.value,
                };
                var url = "{{ route('products.newUpdateInventory') }}";

                fetchApi(url, 'POST', data)
            });

            $(document).on('change', '.check_box_check', function() {
                let variant_id = $(this).data('variant-id')

                let isCheckedTrack = $("#track_id_" + variant_id).is(':checked');
                let isCheckedContinue = $("#continue_id_" + variant_id).is(':checked');

                var data = {
                    variant_id: variant_id,
                    track_quantity: isCheckedTrack ? 1 : 0,
                    continue_selling: isCheckedContinue ? 1 : 0,
                };

                var url = "{{ route('products.newUpdateInventory') }}";
                fetchApi(url, 'POST', data)
            });
        });

        function fetchApi(url = null, method = 'POST', data = {}) {

            // Use fetch to send the data
            fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data) // Convert data to JSON
                })
                .then(response => {
                    if (response.ok) {
                        return response.json(); // or response.text() if the response is text
                    }
                    throw new Error('Network response was not ok.');
                })
                .then(data => {
                    console.log('Success:', data);
                })
                .catch(error => {
                    console.error('Error:', error);
                });

        }
        // Get a reference to the select element

        $('#storeSelect').on('change', function() {
            var selectedValue = $(this).val(); // Get the selected value
            $('.store-variants').addClass('d-none'); // Hide all .store-variants elements initially

            // Check if there is a matching .store-variants element
            if ($('#' + selectedValue).length > 0) {
                $('#' + selectedValue).removeClass('d-none'); // Show the matching element

            }
        });
    </script>

    <script>
        $(document).ready(function() {
            var add_button = $(".add_field_button");
            var x = 0;
            var btn_ref = 1;
            $(".track_quantity").click(function() {
                let obj = $(this);
                let id = obj.data("variant-id");
                if (!obj.is(":checked")) {
                    $("#continue_selling-" + id).prop("checked", false);
                    $("#continue_selling-" + id).css("pointer-events", "none");
                } else {
                    $("#continue_selling-" + id).prop("checked", true);
                    $("#continue_selling-" + id).css("pointer-events", "auto");
                }
            });
            // trigger for button to clone input field.
            $(document).on("click", ".add_field_button", function(e) {
                //let add_new_btn = $(this).clone();
                //x++;
                let input_cloned = $(this).parent().find(`.clone_input_field`).clone(true);
                input_cloned.removeClass("clone_input_field");
                input_cloned.addClass("input-width-css");
                input_cloned.find(`input`).val("");
                input_cloned.find(`.custom-attribute-input-css`).val('{{ $version->currency }}');
                input_cloned.val('');
                let dd = $("<div class='mt-3 position-relative parent-div'></div>");
                dd.append(
                    "<button type='button' class='remove_field'><i class='trash-button fa fa-trash-o' aria-hidden='true'></i></button>"
                );
                $(this).parent().children().first().append(dd.append(input_cloned));
            });
            // remove the html after triger this function.
            $(document).on("click", ".remove_field", function(e) {
                e.preventDefault();
                const data_length = this.parentNode.previousElementSibling.querySelectorAll(".form-control")
                    .length;
                const data = this.parentNode.previousElementSibling.querySelectorAll(
                    "input,.form-control,.input-custom-css")[1];
                const data1 = this.parentNode.previousElementSibling.querySelectorAll(
                    ".form-control,.clone_input_field")[0];
                let datakey = $(this).parent().remove();
                if (data_length > 0 && data_length < 2) {
                    input_status1(data1);
                } else if (data_length > 1) {
                    input_status1(data);
                } else {
                    input_status1(data);
                }
            })
        });
    </script>
    <script src="{{ asset('js/delete_product.js') }}"></script>

    <script>
        $("#assign_attribute_set").click(function() {
            let family_ids = $("#family_ids").val();
            $(document).ready(function() {
                $('<form action="" method="GET"><input type="hidden" name="family_ids" value="' +
                    family_ids + '"></form>').appendTo('body').submit();
            });
        });

        /**
         * Input status change
         * */
        function input_status(obj) {
            if ($(obj).attr('data-rules')) {

                let data_rule = $(obj).attr('data-rules');
                let data_id = $(obj).attr('data-status-id');

                let data_rule_array = data_rule.split("|");

                let self = $(obj);
                let flag = true; // true = success, false = error

                data_rule_array.forEach(function(item) {

                    if (item === "required") {
                        if (self.val().length < 1) {
                            $(data_id).attr("data-content",
                                $(data_id).attr("data-content") +
                                "• This is a required field.<br>"
                            )
                            flag = false;
                            return 0;
                        }
                    }
                    if (item === "max:255") {
                        if (self.val().length > 255) {
                            $(data_id).attr("data-content",
                                $(data_id).attr("data-content") +
                                "• Character length should not be greater than 255.<br>"
                            )
                            flag = false;
                            return 0;
                        }
                    }
                    if (item === "number") {
                        if (self.val().search(/^[0-9.]+$/) === -1) {
                            $(data_id).attr("data-content",
                                $(data_id).attr("data-content") +
                                "• This field can only contain numbers with decimals.<br>"
                            )
                            flag = false;
                            return 0;
                        }
                    }
                    if (item === "slug") {
                        if (self.val().search(/^[a-zA-Z0-9-_]+$/) === -1) {
                            $(data_id).attr("data-bs-content",
                                $(data_id).attr("data-bs-content") +
                                "• This field should only contain alphanumerics and dashes.<br>"
                            )
                            flag = false;
                            return 0;
                        }
                    }
                    // else if(item === "nullable") {
                    //     if(self.val().length  0) {
                    //         flag = true;
                    //         return 0;
                    //     }
                    // }
                });

                console.log(flag, self.val().length, self);

                if (flag) {
                    $(data_id).removeClass("circle-error", 1000);
                    $(data_id).addClass("circle-success", 1000);
                } else {
                    $(data_id).removeClass("circle-success", 1000);
                    $(data_id).addClass("circle-error", 1000);
                }
            }
        }

        /**
         * Trigger input status change on keyup
         * */
        // $('input, select, textarea').keyup(function () {
        //     input_status(this)
        // });

        /**
         * Trigger input status change on page refresh | page load
         * */
        // $(document).ready(function () {
        //     const productEl111 = document.querySelectorAll('.fa-exclamation-circle');
        //     productEl111.forEach(function(element) {
        //         var popover111 = new bootstrap.Popover(element, {
        //             content: function() {
        //                 return "This is the dynamic content";
        //             }
        //         });
        //     });
        // });

        /**
         * Popover trigger
         * */
        /**
         * Popover trigger
         * */
        const productEl = document.querySelectorAll('.circle-sm');
        productEl.forEach(function(element) {
            var popover = new bootstrap.Popover(element, {
                content: function() {
                    return "This is the dynamic content";
                }
            });
        });

        function unAssign(id) {

            var options = $('#family_ids option');
            if (options.val() == id) {
                console.log(options);
                options.attr('selected', false);
            }

            // var values = $.map(options, function (option) {
            //     if (option.selected && (option.value == id)) {
            //         return option.removeAttribute('selected');
            //     }
            //     console.log(option)
            // });
        }
        $(document).ready(function() {
            $('#saveButton').on('click', function() {
            location.reload();
        });
        });
    </script>
@endpush
