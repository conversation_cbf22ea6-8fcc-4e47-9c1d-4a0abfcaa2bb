@if(count($variantSettings) > 0)
<div class="row mt-2">
    <div class="col-12">
        <table class="table table-responsive table-custom-spacing">
            <thead>
            <tr>
                <th scope="col" class="col-name">{{ __('Name') }}</th>
                <th scope="col" class="col-is-required">{{ __('Is Required') }}</th>
                <th class="text-end col-actions" scope="col">{{ __('Actions') }}</th>
            </tr>
            </thead>
            <tbody>
            @foreach($variantSettings as $key => $value)
                <tr>
                    <td>{{ ucwords(str_replace('_', ' ', $key)) }}</td>
                    <td>
                        <div class="form-check form-switch">
                            <input class="form-check-input setting-toggle" type="checkbox" id="{{ $key }}"
                                   data-key="{{ $key }}" {{ $value ? 'checked' : '' }}>
                        </div>
                    </td>
                    <td class="text-end">
                    </td>
                </tr>
            @endforeach
            </tbody>
        </table>
    </div>
</div>
@else
<div class="row mt-2">
    <div class="col-12 d-flex justify-content-center align-items-center" style="height: 200px;">
        <p>No Attributes Found.</p>
    </div>
</div>
@endif
