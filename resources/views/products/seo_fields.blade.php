<?php ?>
@extends('layouts.app_new')
@if(request()->has('id'))
    @section('titles','Edit Products')
@else
    @section('titles','Add Product')
@endif
@section('content')

    @push('header_scripts')
        <link href="{{asset('css/tagger.css')}}" rel="stylesheet">
        <style>
            .tox-statusbar {
                display: none !important;
            }
        </style>
    @endpush

    <div>
        <x-products.edit-product-base-form :product="$product" :version="$current_version">

            <x-products.edit-product-header :product="$product" :version="$current_version"/>
            <x-products.edit-product-header-navs :product="$product" :version="$current_version"/>
            @if($product)
                <input type="hidden" value="{{$product->status}}" name="status">
            @endif
            <div class="row">
                <div class="col-12 {{--col-md-8 col-lg-7 --}}col-xl-8" id="product_content">
                    <div class="tab-content" id="pills-tabContent">
                        {{--GENERAL--}}
                        @foreach($default_families as $key => $family)
                            <div class="">
                                @foreach($family->attributes as $attribute)
                                    <div class="formStyle mb-lg-2 mb-xl-3">
                                        <label for=""> {{$attribute->name??""}}&nbsp;
                                            <x-products.edit-product-attribute-status-dot
                                                :id="$attribute->pivotId"/>
                                        </label>
                                        @if($attribute->handle == 'seo_url')
                                            <input type="text" name="attribute[{{ $attribute->pivotId }}]"
                                                   value="{{ isset($attribute->value) ? $attribute->value->pluck('value')->first() : '' }}"
                                                   data-confirm-before-leave="true"
                                                   data-rules="{{ $attribute->validate_single_values() }}"
                                                   data-status-id="#dot_{{ $attribute->pivotId }}"
                                                   class="form-control "
                                                   @if($attribute->is_required) @endif  id="{{$attribute->handle}}">
                                        @elseif($attribute->handle == 'seo_title')
                                            <input type="text" name="attribute[{{ $attribute->pivotId }}][value]"
                                                   data-confirm-before-leave="true"
                                                   {{--                                                           oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"--}}
                                                   value="{{isset($attribute->value) ?  $attribute->value->pluck('value')->first() : '' }}"
                                                   data-rules="{{ $attribute->text_validation() }}"
                                                   data-status-id="#dot_{{ $attribute->pivotId }}"
                                                   class="form-control"
                                                   placeholder=""  id="{{$attribute->handle}}">
                                        @elseif($attribute->handle == 'seo_description')
                                            <textarea id="mytextarea"
                                                      name="attribute[{{ $attribute->pivotId }}]"
                                                      data-confirm-before-leave="true"
                                                      class="form-control {{$attribute->handle}}"
                                                      data-rules="{{ $attribute->text_validation() }}"
                                                      data-status-id="#dot_{{ $attribute->pivotId }}"
                                                      style="height: 150px">{{ isset($attribute->value) ? $attribute->value->pluck('value')->first() : '' }}</textarea>
                                        @elseif($attribute->handle == 'seo_keyword')
                                            {{--Seo tags--}}
                                            <input type="text" name="attribute[{{ $attribute->pivotId }}]"
                                                   value="{{ isset($attribute->value) ? $attribute->value->pluck('value')->first() : '' }}"
                                                   data-confirm-before-leave="true"
                                                   data-rules="{{ $attribute->text_validation() }}"
                                                   data-status-id="#dot_{{ $attribute->pivotId }}"
                                                   id="{{$attribute->handle}}"
                                                   class="form-control tags"
                                            />
                                            <small class="text-muted fs-10">Simply type and press enter to add tags!</small>
                                        @endif

                                    </div>
                                @endforeach
                            </div>
                        @endforeach
                    </div>
                </div>

                {{--Right Side options--}}
                <div class="col-12 col-xl-4" id="right_sidebar">
                    <div class="row">
                        <div class="col-12 d-none d-xl-block mt-4">
                            {{--dropdown with search--}}
                            <div class="dropdown float-md-right">
                                <span class="btn-outline dropdown-toggle dropdown-toggle-attribute" type="button"
                                      id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ $current_version->name }}
                                </span>
                                <div class="dropdown-menu dropdown-menu-attribute" aria-labelledby="dropdownMenuButton">
                                    @foreach($versions as $version)
                                        <a class="dropdown-item"
                                           href="{{ route("products.edit", ["id" => $product->id, "version_id" => $version->id]) }}">{{ $version->name }}
                                            @foreach($product_versions as $product_version)
                                                @if($product_version->id == $version->id)
                                                    <span class="text-success ms-4"><i class="fas fa-check"></i></span>
                                                @endif
                                            @endforeach
                                        </a>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <x-completeness-product-fields-scoring :product="$product" :version="$current_version"/>
                    <x-products.edit-product-selection-widget :product="$product"/>
                    <x-products.edit-product-footer-btns/>

                </div>
            </div>


        </x-products.edit-product-base-form>
    </div>


@endsection
@push('footer_scripts')

    <script src="{{asset('js/delete_product.js')}}"></script>
    <script>
        $("#pills-completeness-tab").on('click', function () {
            document.getElementById('right_sidebar').style.display = 'none';
            $("#product_content").removeClass('col-md-8 col-lg-7 col-xl-8');
        });

        $(".product_data").on('click', function () {
            document.getElementById('right_sidebar').style.display = 'block';
            $("#product_content").addClass('col-md-8 col-lg-7 col-xl-8');
        });

        @if(Session::has('delete'))
        $(document).ready(function () {
            $('#pills-tab a[href="#pills-product-images"]').tab('show');
        })
        @endif

        $("#pills-variation-tab").click(function (e) {
            e.preventDefault();
            $("#pro_edit_form").prepend("<input type='hidden' name='variant_submit' value = '1'>");
            $("#pro_edit_form").submit();
        });

        /**
         * Input status change
         * */


            // new code start

        class Validation {
            /* In constructor we pass Rule and its values */
            constructor(rule, value, rulesAll = null) {
                this.rule = rule;
                this.value = value;
                this.rulesAll = rulesAll;

                this.res = true;
                this.ruleStringToArray();

            }


            /* This method split the values after this symbol "|" */
            ruleStringToArray() {
                let ruleArray = this.rule;
                this.validate(ruleArray);
            }

            // check type of data
            checkType(type) {
                if(this.rulesAll.type){
                    return this.rulesAll.type===type;
                }else{
                    return this.rulesAll[0]===type;
                }
            }

            /* Rules for validation for all methods */
            validate(ruleArray) {
                const required = /required/g;
                const integer = /min:*/g;
                const decimal = /min:*/g;
                const max = /max:*/g;
                const min = /min:*/g;
                const slug= /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
                const url = /url/g;
                const urlValidator = /(http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/;
                const regex = /regex/g;
                const precision = /precision/g;
                const dateRegax =/dateRegax/g;
                const before = /before/g;
                const after = /after/g;
                this.messages={
                    required:"The value is required.",
                    max:"The value should be equal to or smaller than",
                    min:"The value should be equal to or greater than",
                    format:"Please enter the correct format",
                    url:"The Url is not valid",
                    regax:"Not valid",
                    precision:"digit after point",
                    dateRegax:"The Date should be in between two dates",
                    after:"The value is not valid for after",
                    before:"The value is not valid for before",
                    character:"Please Enter the character"
                };
                this.errors=[];

                for(let i in ruleArray) {

                    /* This rule is used when filed is mandatory */
                    if(ruleArray[i].match(required)) {
                        if(!this.value.split("|")[i]) {
                            this.res = false;
                            this.errors.push(this.messages.required);
                        }
                    }
                    /* The ruler is used for the maximum character length. */
                    if (ruleArray[i].match(max)) {
                        let matcher = ruleArray;
                        if((this.checkType("decimal") || this.checkType("integer") || this.checkType("list") || this.checkType("dimension") || this.checkType("weight") || this.checkType("volume") || this.checkType("price") ) && (parseFloat(this.value) > parseFloat(matcher[1]))){
                            if(this.checkType("dimension") || this.checkType("weight") || this.checkType("volume") || this.checkType("integer") || this.checkType("decimal") || this.checkType("price")){
                                if(parseFloat(matcher[1])>0){
                                    this.res = false;
                                    this.errors.push(`${this.messages.max} ${parseFloat(matcher[1])}`);
                                }

                            }
                        }
                        if (this.value.length > parseFloat(matcher[1])) {
                            this.res = false;
                            this.errors.push(`${this.messages.max} ${parseFloat(matcher[1])}`);

                        }
                        if (this.checkType("date") || this.checkType("date_and_time")) {
                            if (!((Date.parse(this.value) >= Date.parse(matcher[1])) && (Date.parse(this.value) <= Date.parse(matcher[2])))) {
                                this.res = false;
                                this.errors.push(this.messages.dateRegax);
                            }
                            this.res = true;
                        }
                    }
                    /* This rule applies with the length of minimum. */
                    if(ruleArray[i].match(min)) {
                        let matcher = ruleArray;

                        if (($('input[type="text"]')) && !($.isNumeric(this.value)) && (this.value.length < parseFloat(matcher[1]))) {
                            this.res = false;
                            this.errors.push(`${this.messages.min} ${parseFloat(matcher[1])}`);
                        }
                        if(this.checkType("date") || this.checkType("datetime-local")){
                            if(!((Date.parse(this.value) >= Date.parse(matcher[1])) && (Date.parse(this.value) <= Date.parse(matcher[2])))) {
                                this.res = false;
                                this.errors.push(this.messages.dateRegax);
                            }
                            this.res = true;
                        }
                        if ((this.checkType("dimension") || this.checkType("weight") || this.checkType("volume") || this.checkType("integer") || this.checkType("decimal") || this.checkType("price"))) {
                            if (this.value < matcher[1]) {
                                this.res = false;
                                this.errors.push(`${this.messages.min} ${parseFloat(matcher[1])}`);
                            }
                        }
                    }

                    // Rule for integer
                    if(ruleArray[i].match(integer)) {
                        let matcher = ruleArray[i].split(":");
                        if((parseFloat(this.value.split("|")[i]) < parseFloat(matcher[1]))){
                            this.res = false;
                            this.errors.push(`${this.messages.min} ${parseFloat(matcher[1])}`);
                        }

                    }

                    // Rule for decimal number
                    if(ruleArray[i].match(decimal) && this.value.length>0 || this.checkType("decimal") && this.value.length>0 || this.checkType("integer") && this.value.length>0) {
                        let matcher = ruleArray[i].split(":");
                        if(!$.isNumeric(this.value.split("|")[i])){
                            this.res = false;
                            this.errors.push(`${this.messages.format}`);
                        }

                    }
                    /* It is used to validate URL slug.*/
                    if(ruleArray[i].match(slug) && this.checkType("slug")) {
                        if(!this.value.match(urlValidator) && !this.value.match(slug)) {
                            this.res = false;
                            this.errors.push(this.messages.url);
                        }
                    }
                    /* This ruler is used for checking the regex. */
                    if(ruleArray[i].match(regex)) {
                        let matcher = ruleArray[i].split(":");
                        if(!this.value.match(matcher[1])) {
                            this.res = false;
                            this.errors.push(this.messages.regax);
                        }
                    }
                    /* This rule is used to check the values after point */
                    if(ruleArray[i].match(precision)) {
                        let matcher = ruleArray[i].split(":");
                        if((this.value + "").split(".")[1].length > parseInt(matcher[1])) {
                            this.res = false;
                            this.errors.push(this.messages.precision);
                        }
                    }
                    /* This rule is used to check the date in between two dates */
                    if(ruleArray[i].match(dateRegax)) {
                        let matcher = ruleArray[i].split(":");
                        if(!((Date.parse(this.value) >= Date.parse(matcher[1])) && (Date.parse(this.value) <= Date.parse(matcher[2])))) {
                            this.res = false;
                            this.errors.push(this.messages.dateRegax);
                        }
                    }
                    /* This rule is used to check the before date */
                    if(ruleArray[i].match(before)) {
                        let matcher = ruleArray[i].split(":");
                        if((Date.parse(this.value) >= Date.parse(matcher[1]))) {
                            this.res = false;
                            this.errors.push(this.messages.before);
                        }
                    }
                    /* This rule is used to check the after date */

                    if(ruleArray[i].match(after)) {
                        let matcher = ruleArray[i].split(":");
                        if((Date.parse(this.value) <= Date.parse(matcher[1]))) {
                            this.res = false;
                            this.errors.push(this.messages.after);
                        }
                    }
                    return false;

                }
            }

            /* This method is used to show the result true or false */
            result() {
                return this.res;
            }
            errorMessages() {
                return this.errors;
            }
        }
        /* Validation("Rule:value", "value") */

        function input_status(obj) {
            if ($(obj).attr('data-rules')) {
                let rules_array = $(obj).attr('data-rules');
                let data_id = $(obj).attr('data-status-id');
                var data_rule=JSON.parse(rules_array);
                const keys = Object.keys(data_rule);
                let self = $(obj);
                let flag = true; // true = success, false = error
                keys.forEach((key, index) => {
                    const item=[];
                    if (data_rule[key] == "0"){
                    item[0] = "";
                    item[1] = "";
                    }else{
                    item[0] = key;
                    item[1] = data_rule[key];
                    }

                    //  const item=key+":"+data_rule[key];
                    let validation = new Validation(item,self.val(),data_rule);
                    if(!validation.result() || self.val()<0){
                        let chars = validation.errorMessages();
                        let unique_data = [...new Set(chars)];
                        $(data_id).attr("data-bs-content", unique_data
                        )
                        flag = false;
                        return 0;
                    }
                });
                if (flag) {

                    $(data_id).attr("data-bs-content",'✔');
                    $(data_id).removeClass("circle-error", 1000);
                    $(data_id).addClass("circle-success", 1000);


                } else {
                    $(data_id).removeClass("circle-success", 1000);
                    $(data_id).addClass("circle-error", 1000);
                }
            }
        }

        /**
         * Trigger input status change on keyup
         * */
        $('input, select, textarea').keyup(function () {
            input_status(this)
        });
        $('select').change(function () {
            input_status(this)
        });
        /**
         * Trigger input status change on page refresh | page load
         * */
        $(document).ready(function () {
            $("input, select, textarea").each(function () {
                input_status(this)
            });
        });
        $(document).ready(function () {
            $('input[type=date]').change(function () {
                input_status1(this)
            });
        });

        $(document).ready(function () {
            $('input[type=datetime-local]').change(function () {
                input_status1(this)
            });
        });
        $(document).ready(function () {
            $('#justAnotherInputBox').change(function () {
                input_status(this)
            });
        });
        /**
         * Popover trigger
         * */
        /**
         * Popover trigger
         * */
        const productEl = document.querySelectorAll('.circle-sm');
        productEl.forEach(function(element) {
            var popover = new bootstrap.Popover(element, {
                content: function() {
                    return "This is the dynamic content";
                }
            });
        });
    </script>

    <script>
        @if(isset($product->versions->families))
        @foreach($product->versions->families as $selected_family)
        @if($selected_family)
        $(document).ready(function () {
            let obj = [];
            var data = $('#Family').val();
            obj = $('#Family option:selected').map(function () {
                return $(this).text();
            });
            createTags(obj, data, 'div', 'Family');
            //showAttributes(data);
        });
        @endif
        @endforeach
        @endif
        //family
        $('select#Family').on('sumo:closed', function (sumo) {
            let obj = [];
            var data = $('#Family').val();
            obj = $('#Family option:selected').map(function () {
                return $(this).text();
            });
            createTags(obj, data, 'div', 'Family');
            showAttributes(data);

        });

        // Brands

        $('select#Brands').on('sumo:closed', function (sumo) {
            let obj = [];
            var data = $('#Brands').val();
            obj = $('#Brands option:selected').map(function () {
                return $(this).text();
            });
            createTags(obj, data, 'div1', 'Brands');
        });

        // Vendors

        $('select#Vendor').on('sumo:closed', function (sumo) {
            let obj = [];
            var data = $('#Vendor').val();
            obj = $('#Vendor option:selected').map(function () {
                return $(this).text();
            });
            createTags(obj, data, 'div2', 'Vendors');
        });

        // Categories

        $('select#category').on('sumo:closed', function (sumo) {
            let obj = [];
            var data = $('#category').val();
            obj = $('#category option:selected').map(function () {
                return $(this).text();
            });
            createTags(obj, data, 'div3', 'category');
        });

        //    $( "#other" ).click(function() {

        function createTags(text, val, id, sumo_id) {
            $('#' + id).html('');
            for (let i = 0; i < text.length; i++) {

                let div = document.getElementById(id);
                // console.log(id);
                var params = val[i] + ",'" + id + "' , '" + sumo_id + "'";
                var func = 'onclick="deleteTab(' + params + ')"';
                $(div).append("<div class='col-5 col-sm-4 col-md-5 col-lg-5 col-xl-4 mt-2 pr-0'><div class='delete_" + id + "_" + val[i] + "'><div class='d-flex flex-row'>" +
                    "<div class='badge py-2 tg text-break text-left black Roboto regular tags ' style='border-radius:  0.25rem 0 0 0.25rem ;'><span>" + text[i] +
                    "</span></div>" +
                    "<div class='float-right mr-2 px-2 py-1' style='background-color: #C9C9CF;border-radius: 0 0.25rem 0.25rem 0;' " + func + ">" +
                    "<img src='{{asset('media/sidebar/x.png')}}'  width='10' height='10' ></div></div></div></div>");

            }
        }

    </script>

@endpush
