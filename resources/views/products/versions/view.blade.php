<?php ?>
@extends('layouts.app_new')
@section('titles','Language')
@section('content')
    <div>
        <x-products.page-title name="{{trans('products_versions.page_title')}}" description="{{trans('products_versions.page_description')}}"
                               links="true" button="true">
            <x-slot name="addbutton">
                <a href="{{route('versions.create')}}" id="add-language"
                   class="btn btn-primary float-lg-right float-md-right ">
                    {{trans('products_versions.add_language_btn')}}
                </a>
            </x-slot>
        </x-products.page-title>
        @if(count($data['version']) > 0)
            <div class="row">
                <div class="col-12 col-md-6 col-xl-3">
                    <x-general.search-bar placeholder="{{trans('products_versions.search_placeholder')}}"/>
                </div>
            </div>

                    <div class="row mt-2">
                        <div class="col-12">
                                <table class="table">
                                    <caption style="visibility: hidden"></caption>
                                    <thead class="thead">
                                    <tr>
                                        <th scope="col">{{ __('Name') }}</th>
                                        <th scope="col">{{ __('Currency') }}</th>
                                        <th scope="col">{{ __('Separator') }}</th>
                                        <th class="text-end">{{ __('Actions') }}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($data['version'] as $version)
                                        <tr>
                                            <td>{{substr($version->name,0,52)}}</td>
                                            <td>{{$version->currency}}</td>
                                            <td>{{$version->separator == ',' ? "Comma ($version->separator)" : "Point ($version->separator)" }}</td>
                                            <td class="text-end">
                                                    <a href="{{route('versions.edit', $version->id)}}"
                                                       class="pro_ver_edit text-decoration-none">
                                                        <i class="fa-regular fa-pen-to-square fs-20"></i>
                                                    </a>
                                                    <a href="#" data-id="{{$version->id}}" data-retailer-name=""
                                                       data-bs-toggle="modal" data-bs-target="#delete-modal-{{$version->id}}" class="btn-delete text-decoration-none">
                                                        <i class="fa-regular fa-trash-can fs-20 text-danger"></i>
                                                    </a>
                                            </td>
                                            <x-assets.delete-modal id="{{$version->id}}" text="Are you sure you want to delete this language?" button="Delete Language" title="Delete Language" url="{{route('versions.destroy',$version->id)}}" type="version"/>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                                {!! $data['version']->appends($request->all())->links() !!}
                            </div>
                        </div>

        @else
            <x-general.empty-page description="{{trans('products.empty_table_description')}}"/>
        @endif
    </div>


@endsection
@push('footer_scripts')
    <script type="text/javascript">
        let attrid;
        let attrname;

        $(".btn-delete").click(function () {
            attrid = $(this).attr('data-id');
            attrname = $(this).attr('data-retailer-name');
            document.getElementById('name').innerHTML = attrname;
        });

        function del() {
            var form = document.getElementById('delete-vendor');
            form.setAttribute('action', 'versions/' + attrid);
            form.submit();
        }

        // $("#delete-brand").click(function () {
        {{--//--}}
        {{--//   .href = "--}}{{--{{route('delete.brands', '')}}--}}{{--" + "/" + attrid;--}}
        {{--// });--}}

    </script>

    <script type="text/javascript">
        @error('name')
        $('#add-modal').modal('show')
        @enderror
    </script>
@endpush
