@extends('layouts.app_new')
@section('titles', 'Update Media')
@section('content')
@push('header_scripts')

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.0/dropzone.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.0/dropzone.js"></script>
<link rel="stylesheet" href="{{asset('css/gallery/css/checkbox.css')}}" type="text/css" />
<style>
    .popover {
        max-width: 350px;
        max-height: 5rem;
        min-height: 5rem !important;
        display: flex;
        align-items: center;
        width: auto;
    }

    .equal-width {
        width: 16.66%;
    }

    .text-center {
        text-align: center;
    }

    #delete-selected {
        display: none;
    }

</style>

@endpush


<x-products.edit-product-header :product="$product" :buttons="false" :version="$version" />
<x-products.edit-product-header-navs :product="$product" :version="$version" />





{{-- new code start--}}

<x-gallery-upload-from-gallery :product="$product"></x-gallery-upload-from-gallery>
<x-gallery-image-uploader :product_id="$product->id"></x-gallery-image-uploader>
<div class="row">
    <div class="col-12 col-xl-8">
        {{-- social media start  --}}
        <h2 class="mb-2">Social Images</h2>
        {{-- <div class="d-flex mb-4 mt-3 justify-content-between">--}}
        {{-- @foreach($folders as $folder)--}}
        {{-- <div class="folder d-flex justify-content-between align-items-center {{ $loop->last ? '' : 'me-3' }}" data-bs-target="#upload_image" data-bs-toggle="modal">--}}
        {{-- <i class="fa-regular fa-folder"></i>--}}
        {{-- <a href="{{route('gallery.show', $folder->id)}}" class="mb-0 flex-grow-1 ms-3" style="text-decoration: none">--}}
        {{-- {{$folder->name}}--}}
        {{-- </a>--}}
        {{-- <a class="mb-0 flex-grow-1 ms-3" style="text-decoration: none">--}}
        {{-- {{$folder->name}}--}}
        {{-- </a>--}}
        {{-- <i class="icon icon-dots"></i>--}}
        {{-- </div>--}}
        {{-- @endforeach--}}
        {{-- </div>--}}
        {{-- social media end   --}}
        <div class="card">
            <div class="card-body">
                <h3 class="card-title">Images</h3>
                <div class="d-flex justify-content-center align-items-center">
                    <a class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#upload_image">Add Images</a>
                    <a class="btn btn-outline-primary ms-2" data-bs-toggle="modal" data-bs-target="#upload_from_gallery">Add from existing</a>
                </div>
            </div>
        </div>
        {{-- search filter start   --}}
        <div class="d-flex align-items-center justify-content-between pt-3 pb-1">
            <p class="mb-0 fw-700">Total Images: {{count($product->files)}}</p>
            <p class="d-none" id="selected-image-count">Selected Images: </p>
            <button id="delete-selected" class="btn btn-outline-danger " data-bs-toggle="modal" data-bs-target="#delete-selected-modal">Unlink Selected</button>
        </div>


        <div class="d-flex mb-4 d-none">
            <div class="input-group">
                <input type="text" class="form-control" value="{{request('q')}}" name="q" placeholder="Search" aria-label="Search by SKU" aria-describedby="search">
                @if(request()->has('q'))
                <a href="{{ \Illuminate\Support\Facades\Request::url() }}" class="ripplelink btn search_btn_close">
                    <i class="fa fa-close"></i>
                </a>
                @endif
                <div class="input-group-append">
                    <button class="search" type="submit" id="search">
                        <img src="{{asset('media/retailer-dashboard/Search-2.png')}}" alt="">
                    </button>
                </div>
            </div>
            <button class="btn btn-dark ms-3"><img src="{{asset('assets/images/filter_list.svg')}}" style="height:15px;" alt="svg" /> Filter</button>
        </div>
        {{-- search filter end   --}}
        {{-- table start   --}}
        <table class="table" id="sortable">
            <thead>
                <tr>

                    <th scope="col"><input type="checkbox" id="select-all"></th>
                    <th scope="col" class="equal-width">Image</th>
                    <th scope="col" class="equal-width">Image Quality</th>
                    <th scope="col" class="equal-width">Type</th>
                    <th scope="col" class="equal-width">Size</th>
                    <th scope="col" class="equal-width">Dimension</th>
                    <th scope="col" class="text-center">Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($product->files as $file)

                <tr>
                    <td><input type="checkbox" class="file-checkbox" data-id="{{ $file->id }}"></td>
                    <td>
                        <div class="img-div-height my-1">
                            @if($file->bucket_url == null)
                            <div class="background-image-css product-image-link" data-src="{{ isset($file->link) ? $file->link : asset('img/apimio_default.jpg') }}"></div>
                            @else
                            <div class="background-image-css product-image-link" data-src="{{ isset($file->bucket_url) ? $file->bucket_url : asset('img/apimio_default.jpg') }}"></div>
                            @endif
                        </div>

                    </td>
                    <div id="imageOverlay" class="overlay">
                        <div class="overlay-content">
                            <span class="close-btn" id="closeOverlay">&times;</span>
                            <img id="enlargedImage" src="" alt="Enlarged Image">
                        </div>
                    </div>

                    <td>
                        <div class="img-div-height my-1">
                            <div class="">
                                <div class="media-section-popover" data-bs-toggle="popover" data-bs-trigger="hover focus" data-bs-placement="right" data-bs-html="true" data-bs-content="@if($file->width == null || $file->height == null || $file->size == null || $file->ext == null || $file->type == null)
                                                                    Image Processing.
                                                                    @else
                                                                    '{{trans('products_media.image_extension')}} 
                                                                    @if(in_array($file->ext, ['webp', 'jpg'])) ✔ @else ❌ @endif
                                                                    <br/>
                                                                    {{trans('products_media.image_size')}}
                                                                    @if($file->size <= 200 && $file->size > 0) ✔ @else ❌ @endif
                                                                    </br>
                                                                    {{trans('media.image_dimension')}}
                                                                    @if(($file->width >= 1080) && ($file->height >= 1080)) ✔ @else ❌ @endif'
                                                                    @endif">
                                    @if ($file->width == null || $file->height == null || $file->size == null || $file->ext == null || $file->type == null)
                                    <img class="media-image" src="{{asset('img/processing_image.png')}}" alt="">
                                    @else
                                    <img class="media-image" @if($product->imageQualityScoreByImage($file)['approve'] == 100)
                                    src="{{asset('img/icon_good.png')}}"
                                    @elseif($product->imageQualityScoreByImage($file)['warning'] == 100)
                                    src="{{asset('img/icon_Fair.png')}}"
                                    @elseif($product->imageQualityScoreByImage($file)['error'] == 100)
                                    src="{{asset('img/icon_bad.png')}}"
                                    @endif
                                    alt="">
                                    @endif
                                </div>
                            </div>

                        </div>
                    </td>
                    <td data-id="{{ $file->id }}" class="Roboto regular mt-3 text-start column-clickable" style="width: 200px">
                        @if($file->ext && $file->size && $file->width && $file->height)
                        {{ $file->ext }}
                        @else
                        -
                        @endif
                    </td>
                    <td data-id="{{ $file->id }}" class="Roboto regular mt-3 text-start column-clickable" style="width: 200px">
                        @if($file->ext && $file->size && $file->width && $file->height)
                        {{ $file->size . ' KB' }}
                        @else
                        -
                        @endif
                    </td>
                    <td data-id="{{ $file->id }}" class="Roboto regular mt-3 text-start column-clickable" style="width: 200px">
                        @if($file->ext && $file->size && $file->width && $file->height)
                        {{ $file->width . ' X ' . $file->height . ' px' }}
                        @else
                        -
                        @endif
                    </td>
                    <td class="text-center">
                        <a href="#" data-id="{{$file->id}}" data-retailer-name="" data-bs-toggle="modal" data-bs-target="#delete-modal-{{$file->id}}" class="btn-delete">
                            <img src="{{asset('media/retailer-dashboard/delete.png')}}" class="delete-btn-image" alt="">
                        </a>
                    </td>
                </tr>
                <x-assets.delete-modal url="{{route('delete.file', [$product->id, $file->id])}}" id="{{$file->id}}" text="Do you want to unlink this image from the product?" button="Unlink Image" title="Unlink Image" type="file" />
                @endforeach
            </tbody>
        </table>
        {{-- table end   --}}
    </div>
    <x-products.edit-product-image-scoring-widget id="{{$product->id}}" />
    <div class="modal fade" id="delete-selected-modal" tabindex="-1" aria-labelledby="delete-selected-modal-label" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="delete-selected-modal-label">Unlink Images</h3>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Do you want to unlink these images from the product?</p>
                </div>
                <div class="modal-footer">
                    <form id="delete-selected-form" method="GET">
                        @csrf
                        @method('DELETE')
                        <input type="hidden" name="file_ids" id="selected-file-ids">
                        <div class="d-flex gap-2 p-2">
                            <button type="button" data-bs-dismiss="modal" id="delete-cancel-btn" class="btn btn-light shadow-sm border">
                                {{trans('products_edit.cancel_btn')}}
                            </button>
                            <button type="button" class="btn btn-danger shadow unlinkall">Unlink Images</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>


</div>
{{-- new code end--}}
@endsection

@push('footer_scripts')
<script src="https://unpkg.com/filepond@^4/dist/filepond.js"></script>
<script src="https://unpkg.com/filepond-plugin-image-preview/dist/filepond-plugin-image-preview.js"></script>

<script src="https://unpkg.com/filepond-plugin-image-exif-orientation/dist/filepond-plugin-image-exif-orientation.js"></script>
<script src="{{asset('js/delete_product.js')}}"></script>
<script src="https://unpkg.com/filepond-plugin-file-validate-type/dist/filepond-plugin-file-validate-type.js"></script>
<script src="https://unpkg.com/filepond/dist/filepond.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.2/jquery-ui.min.js"></script>

<script>
    // Function to check and set background image for each file div
    function checkAndSetBackgroundImages() {
        // Select all divs with the class "background-image-css"
        var divs = document.querySelectorAll('.product-image-link');
        var defaultImg = '{{ asset("img/apimio_default.jpg") }}'; // Path to your default image
        var closeCircleImg = '{{ asset("img/close-circle.png") }}'; // Path to the close circle image

        // Loop through each div
        divs.forEach(function(div, index) {
            // Get the data-src attribute value which contains the image URL
            var imageUrl = div.getAttribute('data-src');

            var img = new Image();
            img.onload = function() {
                // If image exists, set it as background
                div.style.backgroundImage = 'url(' + imageUrl + ')';
                // Set the corresponding image in the second column
                updateSecondColumnImage(index, true);
            };
            img.onerror = function() {
                // If image doesn't exist, set default image as background
                div.style.backgroundImage = 'url(' + defaultImg + ')';
                // Update the second column image to close circle if default image is used
                updateSecondColumnImage(index, false);
                // Update popover message for invalid or missing URLs
                updatePopoverContent(index, false);
            };
            img.src = imageUrl; // Attempt to load the image
        });
    }

    function updateSecondColumnImage(index, hasValidImage) {
        var secondColumnImages = document.querySelectorAll('.media-image');
        if (secondColumnImages[index]) {
            secondColumnImages[index].src = hasValidImage ? secondColumnImages[index].src : '{{ asset("img/close-circle.png") }}';
        }
    }

    function updatePopoverContent(index, hasValidImage) {
        var popovers = document.querySelectorAll('.media-section-popover');
        if (popovers[index]) {
            var message = hasValidImage ? 'Image is valid' : 'Image not found.<br> The URL is invalid or missing.';
            popovers[index].setAttribute('data-bs-content', message);
        }
    }

    // Call the function once the document is loaded
    document.addEventListener('DOMContentLoaded', function() {
        checkAndSetBackgroundImages();
    });

</script>



<script>
    /**
     * Popover trigger
     * */
    function initializeEventListeners() {
        document.querySelectorAll('.background-image-css').forEach(item => {
            item.removeEventListener('click', handleImageClick);
            item.addEventListener('click', handleImageClick);
        });

        document.getElementById('closeOverlay').removeEventListener('click', handleCloseClick);
        document.getElementById('closeOverlay').addEventListener('click', handleCloseClick);
    }

    function handleImageClick(event) {
        const imgSrc = event.target.getAttribute('data-src');
        document.getElementById('enlargedImage').src = imgSrc;
        document.getElementById('imageOverlay').style.display = 'block';
    }

    function handleCloseClick() {
        document.getElementById('imageOverlay').style.display = 'none';
    }



    $(document).ready(function() {


        const media_section = document.querySelectorAll('.media-section-popover');
        media_section.forEach(function(element) {
            var popover = new bootstrap.Popover(element, {
                content: function() {
                    return "This is the dynamic content";
                }
            });
        });

        $(function() {
            $("#sortable tbody").sortable({
                cursor: "move"
                , placeholder: "sortable-placeholder"
                , helper: function(e, tr) {
                    var $originals = tr.children();
                    var $helper = tr.clone();
                    $helper.children().each(function(index) {
                        // Set helper cell sizes to match the original sizes
                        $(this).width($originals.eq(index).width());
                    });
                    return $helper;
                }
                , update: function(event, ui) {
                    initializeEventListeners(); // Reinitialize event listeners after sorting
                }

            }).disableSelection();
            initializeEventListeners();

        });
    });

</script>
<script>
    $(document).ready(function() {
        var selectedFileIds = [];

        $('.file-checkbox').on('change', function() {
            var fileId = $(this).data('id');
            if ($(this).is(':checked')) {
                selectedFileIds.push(fileId);
            } else {
                selectedFileIds = selectedFileIds.filter(id => id !== fileId);
            }

            $('#selected-file-ids').val(selectedFileIds.join(','));
        });

        $('#select-all').on('change', function() {
            if ($(this).is(':checked')) {
                $('.file-checkbox').prop('checked', true).trigger('change');
            } else {
                $('.file-checkbox').prop('checked', false).trigger('change');
            }
        });
    });


    document.addEventListener('DOMContentLoaded', function() {
        const selectedFileIds = new Set();
        const fileCheckboxes = document.querySelectorAll('.file-checkbox');
        const selectAllCheckbox = document.getElementById('select-all');
        const deleteSelectedButton = document.getElementById('delete-selected');
        const selectedFileIdsInput = document.getElementById('selected-file-ids');
        const selectedImagesCountParagraph = document.getElementById('selected-image-count');




        function updateDeleteButtonVisibility() {
            if (selectedFileIds.size > 0) {
                deleteSelectedButton.style.display = 'inline-block';
                updateSelectedImagesCount(selectedFileIds.size);

            } else {
                deleteSelectedButton.style.display = 'none';
                updateSelectedImagesCount(0);
            }
        }

        function updateSelectedImagesCount(count) {
            if (count > 0) {
                selectedImagesCountParagraph.textContent = 'Selected Images: ' + count;
                selectedImagesCountParagraph.classList.remove('d-none');
            } else {
                selectedImagesCountParagraph.classList.add('d-none');
            }
        }

        fileCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const fileId = this.getAttribute('data-id');
                if (this.checked) {
                    selectedFileIds.add(fileId);
                } else {
                    selectedFileIds.delete(fileId);
                }
                updateDeleteButtonVisibility();
                console.log(Array.from(selectedFileIds));
            });
        });

        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            fileCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
                const fileId = checkbox.getAttribute('data-id');
                if (isChecked) {
                    selectedFileIds.add(fileId);
                } else {
                    selectedFileIds.clear();
                }
            });
            updateDeleteButtonVisibility();
            console.log(Array.from(selectedFileIds));
        });
        $(document).on('click', '.unlinkall', function(event) {
            event.preventDefault();

            var selected_fields = $("#selected-file-ids").val();

            // var joinedFields = arrayOfFields.join(',');
            var actionUrl = "{{ route('delete.file', [$product->id, 'NoCode']) }}";
            var newActionUrl = actionUrl.replace('NoCode', selected_fields);
            // Set the action attribute of the form
            $("#delete-selected-form").attr('action', newActionUrl);

            // Submit the form
            $("#delete-selected-form").submit();
        });
    });

</script>

<script src="https://cdn.tiny.cloud/1/oh0o94omie4zyuas0tk96qk319s6ltqf8eq9er20jhn3d7r7/tinymce/5/tinymce.min.js" referrerpolicy="origin"></script>
@endpush
