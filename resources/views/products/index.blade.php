@extends('layouts.app_new')
@section('titles','Products')
@section('content')
    {{-- new code --}}
    <div class="row row-cols-1 row-cols-lg-2">
        <div class="col d-flex flex-column align-items-start justify-content-center mb-4">
            <h2 class="mb-0">{{trans('products.page_title')}}</h2>
            <p class="mb-0">{{trans('products.page_description')}}</p>
        </div>
        <div class="col d-flex justify-content-lg-end justify-content-md-start mt-4 mt-lg-1 mt-1">
            @can('add_and_edit_product',[\App\Models\Organization\OrganizationUserPermission::class,auth()->user()->organization_id])
                   @can('SubscriptionAccess', "product")
                        @can('create-product', \App\Models\Product\Variant::query())
                    <a href="{{route('products.create')}}" class="btn btn-primary" data-bs-toggle="modal"
                    data-bs-target="#create_product">{{trans('products.add_product_btn')}}</a>
                        @else
                        <a href="javascript:void(0)"
                            id="disabled-button"
                            class="btn btn-primary float-lg-right float-md-right "
                            data-bs-toggle="tooltip"
                            data-bs-placement="top"
                            disabled
                            title="Please upgrade to access this feature">
                               {{trans('products.add_product_btn')}}
                        </a>
                        @endcan
                   @endcan
                   @can('SubscriptionAccess',"import")
                <a href="{{ route("import.csv.step1") }}"
                   class="btn btn-outline-primary ms-2">{{trans('products.bulk_import_btn')}}</a>
                   @endcan
            @endcan

            {{-- @if(Gate::allows('primeOrPaidUser')) --}}
            <a href="{{route("export.exportOne")}}" class="btn btn-outline-dark ms-2">{{trans('products.export_btn')}}</a>
            {{-- @else
            <a href="javascript:void(0)" data-bs-toggle="tooltip" data-bs-placement="right" title="Export feature is available for paid plans only. Upgrade to a paid plan to enable this feature." class="btn btn-outline-dark ms-2">{{trans('products.export_btn')}} <i class="fa fa-lock text-gray ms-2"></i></a>
            @endif --}}
        </div>
    </div>


    {{-- for progress (import export shopify bulk)--}}
    <!-- {{--@if(isset($batches_progress) && count($batches_progress) > 0)--}}
        <div>
            <ul class="p-0 row">
                {{--@foreach($batches_progress as $batch_progress)--}}
                    {{--@if($batch_progress->batch()['batch_progress'] != 100)--}}
                        <div class="col-main-progress-js col-12 col-md-6 col-lg-4 col-xl-3" data-batchId="{{--{{$batch_progress->batch()['id']}}--}}">
                            <div class="main-progress-js mt-3">
                                <p class="mb-0 fs-12 fw-bold">{{--{{$batch_progress->title()}}--}}</p>
                                <div class="d-flex align-items-center">
                                    <div class="progress" style="height: 1rem;width: 100%;">
                                        <div class="batch-progress-js progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: {{--{{$batch_progress->batch()['batch_progress']}}--}}%"></div>
                                    </div>
                                    <div class="ms-3">
                                        <span class="status-publish p-1 px-2 rounded fs-12 batch-progress-number-js">{{--{{$batch_progress->batch()['batch_progress']}}--}}%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                   {{-- @endif--}}
                {{--@endforeach--}}
            </ul>
        </div>
    {{--@endif--}} -->


        {{--        <!--List Table-->--}}
                    <input type="hidden" id="filter_total_product_count" value="0" />
                    <input type="hidden" class="filter_bulk_productIds" value="" />
                    <input type="hidden" id="filter_total_product_array" value="" />
                    <div id="listingTable" data-delete-product-route="{{ route('products.delete.json', ':id') }}" data-orgId="{{$org_id}}"></div>
                     @vite("resources/js/components/productListing/ListingTable.jsx")
        {{-- <x-products.product-listing-table isfilters="true" issearch="true" isdelete="true" isaction="1" orgId="{{$org_id}}" isbulkable="true" /> --}}


        {{--        <!-- Modal -->--}}
        <x-products.add-product  orgId="{{$org_id}}" />


        @push('footer_scripts')
            <script>
                $(document).ready(function() {

                    /*
                     * Open product popup if their is any exception
                     * */
                    @error("sku")
                    $("#create_product").modal("show");
                    @enderror
                    @error("name")
                    $("#create_product").modal("show");
                    @enderror
                })
            </script>
        @endpush
        @endsection
