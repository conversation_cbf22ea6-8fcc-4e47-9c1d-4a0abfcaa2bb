<div class="row">
    <div class="col-12 ">
        <div class="gy-4 d-flex flex-wrap align-items-stretch">
            <div class=" {{$isBasicRange == false  ? 'col-sm-4' : 'col-sm-3' }} d-flex align-items-center">
                <div>
                    <h2>
                        Plans
                    </h2>
                    <p>You can upgrade to a different pricing plans, and choose add ons.</p>
                </div>
            </div>

            {{-- Basic PIM Card --}}
            @if($isBasicRange)
            <div class="{{ $isBasicRange == false  ? 'col-sm-4' : 'col-sm-3' }} col-12 border d-flex flex-column">
                <div class="p-2 flex-grow-1 d-flex flex-column">
                    <h2>Basic PIM</h2>
                    <img src="{{ asset('/media/billing/community.png') }}" class="plans-images" alt="Grow plan">

                    <ul class="mt-3 plans-cards-ul px-0">
                        <li><i class="fa fa-check me-2" aria-hidden="true"></i>Upto 1 store</li>
                        <li><i class="fa fa-check me-2" aria-hidden="true"></i>
                            <div><span>Upto 1000 SKUs included.</span><a class="text-primary" data-bs-toggle="modal"
                                    data-bs-target="#additional-sku" style="cursor: pointer;"> Additional SKUs</a>
                            </div>
                        </li>
                        <li><i class="fa fa-check me-2" aria-hidden="true"></i> Email and live chat support.</li>
                    </ul>
                    <div class="plans-text mt-auto  text-dark">
                        $199
                        <sub style="font-size: 16px">/mo</sub><br>
                        <span class="small-text Roboto bold text-dark"> <br> </span>
                    </div>
                    <input type="hidden" name="handle">
                    @if (isset($subscribedPlan))
                        @if ($subscribedPlan->handle == $plans[0]->handle)
                            <a class="btn btn-success w-100" href="{{ route('subscription.info') }}">view
                                subscription</a>
                        @else
                            <a class="btn btn-danger w-100" href="javascript:void(1)">Plan Already Upgraded </a>
                        @endif
                    @else
                        @if ($user->shopify_shop_id)
                            <a class="btn btn-warning w-100 text-decoration-none"
                                href="{{ route('channel.shopify.bill.store', ['plan_handle' => $plans[0]->handle, 'shopify_channel_id' => $channel->id ?? null]) }}">{{ $plans[0]->name }}</a>
                        @else
                            <form action="{{ route('subscribe.post') }}" method="post">
                                <input type="hidden" name="organization_id" value="{{ $organization_id }}">
                                <input type="hidden" name="handle" value="{{ $plans[0]->handle }}">
                                <button type="submit" class="btn btn-warning w-100"
                                    style="text-decoration: none; color: inherit;"
                                    style="cursor: pointer;">{{ $plans[0]->name }}</button>
                            </form>
                        @endif
                    @endif
                </div>
            </div>
            @endif
            {{-- Advanced PIM Card --}}
            <div class="{{ $isBasicRange == false  ? 'col-sm-4' : 'col-sm-3' }} col-12 border position-relative d-flex flex-column">
                <div class="bg-warning text-center p-1"
                    style="position: absolute;right: 4px;top: 5px;border-radius: 5px;font-size: 12px;font-weight: bold;">
                    RECOMMENDED</div>
                <div class="p-2 flex-grow-1 d-flex flex-column">
                    <h2>Advanced PIM</h2>
                    <img src="{{ asset('/media/billing/starter1.png') }}" class="plans-images" alt="Expand Plan">

                    <ul class="mt-3 plans-cards-ul px-0">
                        <li><i class="fa fa-check me-2" aria-hidden="true"></i>
                            <div><span>Includes 1 store.</span><a class="text-primary" data-bs-toggle="modal"
                                    data-bs-target="#additional-stores" style="cursor: pointer;"> Additional
                                    Stores</a>
                            </div>
                        </li>
                        <li><i class="fa fa-check me-2" aria-hidden="true"></i>
                            <div><span>Upto 1000 SKUs included.</span><a class="text-primary" data-bs-toggle="modal"
                                    data-bs-target="#additional-sku" style="cursor: pointer;"> Additional SKUs</a>
                            </div>
                        </li>
                        <li><i class="fa fa-check me-2" aria-hidden="true"></i> Dedicated Account Manager.</li>
                    </ul>
                    <div class="plans-text mb-auto  text-dark">
                        $399
                        <sub style="font-size: 16px">/mo</sub><br>
                        <span class="small-text Roboto bold text-dark"> <br> </span>
                    </div>
                    @if (isset($subscribedPlan))
                        @if ($subscribedPlan->handle == $plans[1]->handle)
                            <a class="btn btn-success w-100" href="{{ route('subscription.info') }}">view
                                subscription</a>
                        @else
                            @if ($user->shopify_shop_id)
                            <a class="btn btn-warning w-100" style="text-decoration: none; color: inherit;"
                            data-bs-toggle="modal" data-bs-target="#sub-confirmation-popup"
                            style="cursor: pointer;">{{ $plans[1]->name }}</a>
                            @else
                                <a class="btn btn-warning w-100" style="text-decoration: none; color: inherit;"
                                    data-bs-toggle="modal" data-bs-target="#sub-confirmation-popup"
                                    style="cursor: pointer;">{{ $plans[1]->name }}</a>
                            @endif
                        @endif
                    @else
                        @if ($user->shopify_shop_id)
                            <a class="btn btn-dark w-100 text-decoration-none"
                                href="{{ route('channel.shopify.bill.store', ['plan_handle' => $plans[1]->handle, 'shopify_channel_id' => $channel->id ?? null]) }}">{{ $plans[1]->name }}</a>
                        @else
                            <form action="{{ route('subscribe.post') }}" method="post">
                                <input type="hidden" name="organization_id" value="{{ $organization_id }}">
                                <input type="hidden" name="handle" value="{{ $plans[1]->handle }}">
                                <button type="submit" class="btn btn-dark w-100"
                                    style="text-decoration: none;"
                                    style="cursor: pointer;">{{ $plans[1]->name }}</button>
                            </form>
                        @endif
                    @endif
                </div>

            </div>

            {{-- Enterprise PIM Card --}}
            <div class="{{ $isBasicRange == false  ? 'col-sm-4' : 'col-sm-3' }} col-12 border d-flex flex-column">
                <div class="p-2 flex-grow-1 d-flex flex-column">
                    <h2>Enterprise PIM</h2>
                    <img src="{{ asset('/media/billing/business.png') }}" class="plans-images" alt="Enterprise Plan">

                    <ul class="mt-3 plans-cards-ul px-0">
                        <li><i class="fa fa-check me-2" aria-hidden="true"></i>Unlimited Store.</li>
                        <li><i class="fa fa-check me-2" aria-hidden="true"></i>
                            Unlimited SKUs included.
                        </li>
                        <li><i class="fa fa-check me-2" aria-hidden="true"></i>Dedicated Account Manager.</li>
                    </ul>
                    <div class="plans-text mt-auto text-dark">
                        $999
                        <sub style="font-size: 16px">/mo</sub><br>
                        <span class="small-text Roboto bold text-dark"> <br> </span>
                    </div>
                </div>

                <div class="px-2">
                    <a href="https://apimio.com/demo/" target="_blank"
                        class="btn btn-primary w-100 text-decoration-none mb-2 mt-auto">
                        Contact Sales
                    </a>
                </div>
            </div>

        </div>
        <div class=" mt-5">
            <table class="table text-center table-bordered">
                <thead>
                    <tr>
                        <th scope="col" class="col-3">Compare Features</th>
                        @if($isBasicRange)
                            <th scope="col" class="col-3">Basic PIM</th>
                        @endif
                        <th scope="col" class="col-3">Advanced PIM</th>
                        <th scope="col" class="col-3">Enterprise PIM</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="col-3 text-primary">SKU Management</td>
                         @if($isBasicRange)
                        <td class="col-3">
                            <div><span>Upto 1000 SKUs included.</span><a class="text-primary" data-bs-toggle="modal"
                                    data-bs-target="#additional-sku" style="cursor: pointer;"> Additional SKUs</a>
                            </div>
                        </td>
                        @endif
                        <td class="col-3">
                            <div><span>Upto 1000 SKUs included.</span><a class="text-primary" data-bs-toggle="modal"
                                    data-bs-target="#additional-sku" style="cursor: pointer;"> Additional SKUs</a>
                            </div>
                        </td>
                        <td class="col-3">Unlimited SKUs included.</td>
                    </tr>
                    <tr>
                        <td class="col-3 text-primary">Store Connections</td>
                         @if($isBasicRange)
                        <td class="col-3">1 Store</td>
                        @endif
                        <td class="col-3">
                            <div><span>Includes 1 store.</span><a class="text-primary" data-bs-toggle="modal"
                                    data-bs-target="#additional-stores" style="cursor: pointer;"> Additional
                                    Stores</a></div>
                        </td>
                        <td class="col-3">Unlimited SKUs included.</td>
                    </tr>
                    <tr>
                        <td class="col-3 text-primary">Brand Portal</td>
                         @if($isBasicRange)
                        <td class="col-3">-</td>
                        @endif
                        <td class="col-3">
                            $100 Each
                        </td>
                        <td class="col-3">$100 Each</td>
                    </tr>
                    <tr>
                        <td class="col-3 text-primary">Storage</td>
                         @if($isBasicRange)
                        <td class="col-3">50 GB</td>
                        @endif
                        <td class="col-3">1 TB</td>
                        <td class="col-3">Unlimited</td>
                    </tr>
                    <tr>
                        <td class="col-3 text-primary">Product Data Validations and Insights</td>
                         @if($isBasicRange)
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                        @endif
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                    </tr>
                    <tr>
                        <td class="col-3 text-primary">Image Quality Score</td>
                         @if($isBasicRange)
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                        @endif
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                    </tr>
                    <tr>
                        <td class="col-3 text-primary">CSV Import/Export</td>
                         @if($isBasicRange)
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                        @endif
                        <td class="col-3">Enhanced CSV Management</td>
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                    </tr>
                    <tr>
                        <td class="col-3 text-primary">Import/Export Template and Functions</td>
                         @if($isBasicRange)
                        <td class="col-3">-</td>
                        @endif
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                    </tr>
                    <tr>
                        <td class="col-3 text-primary">Bulk Edit Basic (Product, SEO, Attributes, Variants)</td>
                         @if($isBasicRange)
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                        @endif
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                    </tr>
                    <tr>
                        <td class="col-3 text-primary">Bulk Edit Advanced (Pricing, Discounts)</td>
                         @if($isBasicRange)
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                        @endif
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                    </tr>
                    <tr>
                        <td class="col-3 text-primary">Inventory Management</td>
                         @if($isBasicRange)
                        <td class="col-3">-</td>
                        @endif
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                    </tr>
                    <tr>
                        <td class="col-3 text-primary">Internationalization(Languages, Currencies)</td>
                         @if($isBasicRange)
                        <td class="col-3">-</td>
                        @endif
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                    </tr>
                    <tr>
                        <td class="col-3 text-primary">DAM (Digital Asset Management)</td>
                         @if($isBasicRange)
                        <td class="col-3">-</td>
                        @endif
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                    </tr>
                    <tr>
                        <td class="col-3 text-primary">Customer Support</td>
                         @if($isBasicRange)
                        <td class="col-3">Standard Support</td>
                        @endif
                        <td class="col-3">Premium Account Management Support</td>
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                    </tr>
                    <tr>
                        <td class="col-3 text-primary">Users, Roles and Permissions</td>
                         @if($isBasicRange)
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                        @endif
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                        <td class="col-3"><i class="fa fa-check me-2 text-primary" aria-hidden="true"></i></td>
                    </tr>
                    <tr>
                        <td class="col-3 text-primary"></td>
                         @if($isBasicRange)
                        <td class="col-3">
                            @if (isset($subscribedPlan))
                                @if ($subscribedPlan->handle == $plans[0]->handle)
                                    <a class="btn btn-success w-100" href="{{ route('subscription.info') }}">view
                                        subscription</a>
                                @else
                                    <a class="btn btn-danger w-100" href="javascript:void(1)">Plan Already Upgraded
                                    </a>
                                @endif
                            @else
                                @if ($user->shopify_shop_id)
                                    <a class="btn btn-warning w-100 text-decoration-none"
                                        href="{{ route('channel.shopify.bill.store', ['plan_handle' => $plans[0]->handle, 'shopify_channel_id' => $channel->id ?? null]) }}">{{ $plans[0]->name }}</a>
                                @else
                                    <form action="{{ route('subscribe.post') }}" method="post">
                                        <input type="hidden" name="organization_id" value="{{ $organization_id }}">
                                        <input type="hidden" name="handle" value="{{ $plans[0]->handle }}">
                                        <button type="submit" class="btn btn-warning w-100"
                                            style="text-decoration: none; color: inherit;"
                                            style="cursor: pointer;">{{ $plans[0]->name }}</button>
                                    </form>
                                @endif
                            @endif
                        </td>
                        @endif
                        <td class="col-3">
                            @if (isset($subscribedPlan))
                                @if ($subscribedPlan->handle == $plans[1]->handle)
                                    <a class="btn btn-success w-100" href="{{ route('subscription.info') }}">view
                                        subscription</a>
                                @else
                                    @if ($user->shopify_shop_id)
                                        <a class="btn btn-dark w-100 text-decoration-none"
                                            href="{{ route('channel.shopify.bill.store', ['plan_handle' => $plans[1]->handle, 'shopify_channel_id' => $channel->id ?? null]) }}">{{ $plans[1]->name }}</a>
                                    @else
                                        <a class="btn btn-warning w-100"
                                            style="text-decoration: none;" data-bs-toggle="modal"
                                            data-bs-target="#sub-confirmation-popup"
                                            style="cursor: pointer;">{{ $plans[1]->name }}</a>
                                    @endif

                                @endif
                            @else
                                @if ($user->shopify_shop_id)
                                    <a class="btn btn-dark w-100 text-decoration-none"
                                        href="{{ route('channel.shopify.bill.store', ['plan_handle' => $plans[1]->handle, 'shopify_channel_id' => $channel->id ?? null]) }}">{{ $plans[1]->name }}</a>
                                @else
                                    <form action="{{ route('subscribe.post') }}" method="post">
                                        <input type="hidden" name="organization_id" value="{{ $organization_id }}">
                                        <input type="hidden" name="handle" value="{{ $plans[1]->handle }}">
                                        <button type="submit" class="btn btn-dark w-100"
                                            style="text-decoration: none;"
                                            style="cursor: pointer;">{{ $plans[1]->name }}</button>
                                    </form>
                                @endif
                            @endif
                        </td>
                        <td class="col-3"><a href="https://apimio.com/demo/" target="_blank"
                                class="btn btn-primary w-100 text-decoration-none  ">
                                Contact Sales
                            </a></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    {{-- Additional SKU popup --}}
    <div class="modal  fade" id="additional-sku" tabindex="-1" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title text-center" id="exampleModalLabel">Additional SKUs</h2>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <table class="table ">
                        <thead>
                            <tr>
                                <th scope="col">SKUs</th>
                                <th scope="col " class="text-end">Cost</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>

                                <td>1000</td>
                                <td class="text-end">Included</td>
                            </tr>
                            <tr>

                                <td>1000-2500</td>
                                <td class="text-end">.05$ per SKU</td>
                            </tr>
                            <tr>

                                <td>2500-10,000</td>
                                <td class="text-end">.03$ per SKU</td>
                            </tr>
                            <tr>

                                <td>10,000-100,000</td>
                                <td class="text-end">.02$ per SKU</td>
                            </tr>
                            <tr>

                                <td>100,000+</td>
                                <td class="text-end">.01$ per SKU</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    {{-- Store add On popup --}}
    <div class="modal  fade" id="additional-stores" tabindex="-1" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title text-center" id="exampleModalLabel">Additional Stores</h2>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <table class="table ">
                        <thead>
                            <tr>
                                <th scope="col">Stores</th>
                                <th scope="col" class="text-end">Cost</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>

                                <td>1</td>
                                <td class="text-end">Included</td>
                            </tr>
                            <tr>

                                <td>2-4</td>
                                <td class="text-end">100$ per Store</td>
                            </tr>
                            <tr>

                                <td>5-10</td>
                                <td class="text-end">70$ per Store</td>
                            </tr>
                            <tr>

                                <td>10+</td>
                                <td class="text-end">50$ per Store</td>
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    {{-- Plan upgrade popup --}}
    <div class="modal  fade" id="sub-confirmation-popup" tabindex="-1" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title text-center" id="exampleModalLabel">Upgrade Plan</h2>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="mb-2 text-center fw-bold fs-5">Are you sure you want to upgrade the plan?</p>
                    @if ($user->shopify_shop_id)
                        <div class="mt-auto">
                            <a class="bg-dark py-2 px-4 text-white rounded text-decoration-none fs-6"
                                href="{{ route('channel.shopify.bill.store', ['plan_handle' => $plans[1]->handle, 'shopify_channel_id' => $channel->id ?? null]) }}">Confirm</a>
                        </div>
                    @else
                        <form action="{{ route('subscribe.post') }}" method="post">
                            <input type="hidden" name="organization_id" value="{{ $organization_id }}">
                            <input type="hidden" name="handle" value="{{ $plans[1]->handle }}">
                            <div class="form-group d-flex flex-col justify-content-center mt-2">
                                <label for="coupon" class="text-center mt-2 fw-bold fs-6">Coupon Code</label>
                                <input type="text" name="coupon" class="form-control"
                                    id="coupon-code" placeholder="Enter your coupon code">
                            </div>
                            <button type="submit" class="btn btn-primary w-100 mt-3"
                                style="text-decoration: none;"
                                style="cursor: pointer;">Confirm</button>
                        </form>
                    @endif
                    <button type="button" class="btn btn-outline-primary w-100 mt-2" data-bs-dismiss="modal">Close</button>
                </div>

                {{-- <div class="modal-footer">



                </div> --}}
            </div>
        </div>
    </div>


</div>

<script>
    async function syncCoupon() {
        let coupon = document.getElementById('coupon-code');
        code = coupon.value;
        console.log(coupon);
        console.log(code);
        handles = document.getElementsByClassName('plan-coupon')
        handles[0].value = code;
        handles[1].value = code;
        console.log(handles);
    }
</script>
