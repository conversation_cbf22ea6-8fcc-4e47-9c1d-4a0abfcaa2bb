<style>

    .left {
        inset: 0 auto auto 0;
        transform-origin: 100% 0;
        transform: translate(-29.3%) rotate(-45deg);
    }

    .right {
        inset: 0 0 auto auto;
        transform-origin: 0 0;
        transform: translate(29.3%) rotate(45deg);
    }

    .ribbon-2 {
        padding-top:10px !important;
        padding-bottom: 20px !important;
        --f: 10px; /* control the folded part*/
        --r: 15px; /* control the ribbon shape */
        --t: 20px; /* the top offset */

        position: absolute;
        inset: var(--t) calc(-1*var(--f)) auto auto;
        padding: 0 10px var(--f) calc(10px + var(--r));
        clip-path:
            polygon(0 0,100% 0,100% calc(100% - var(--f)),calc(100% - var(--f)) 100%,
            calc(100% - var(--f)) calc(100% - var(--f)),0 calc(100% - var(--f)),
            var(--r) calc(50% - var(--f)/2));
        background: #000;
        color:#fff;
        font-weight: bold;
        box-shadow: 0 calc(-1*var(--f)) 0 inset #0005;
    }

    .mt-14{
        margin-top: 14px !important;
    }
    .bi-check-circle-fill{
        font-size: 1.3em;
        color: #4FBA6F;
    }


    .bi-check-circle-fill{
        font-size: 1.3em;
        color: #4FBA6F;
    }

    .plus_plan_text {
        color: white;
    }

</style>

{{--/***************** new code start *****************/--}}

<div class="row">
    {{-- title start--}}
    {{-- title end--}}
    {{-- billing cards start--}}
    <div class="col-12">
        <div class="gy-4 d-flex flex-wrap align-items-stretch">
            @foreach($data['plans'] as $plan1)
            @if($plan1['name'] != 'Grow Plan')
                <div class="d-flex mb-4 ms-2 ms-lg-0">
                    <div class="card card-width-lg shadow-sm {{$plan1->handle == 'community_plan' ? 'bg-white-smoke' : ''}}{{$plan1->handle == 'standard_plan' ? 'bg-warning ms-2 ms-xl-5' : ''}}{{$plan1->handle == 'plus_plan' ? 'bg-primary ms-2 ms-xl-5' : ''}} p-2 p-lg-3 p-xl-4">
                        <h2 class="card-title mb-4"> {{ $plan1['name'] }}</h2>
                        @if((($plan1->handle == 'standard_plan' && $org->onTrial()) || ($plan1->handle == 'free_plan' && isset($is_invited) && !$org->is_subscribed())) && (!auth()->user()->shopify_shop_id))
                            <div class="ribbon-2">{{trans('billing.current_plan')}}</div>
                        @endif
                        <img src="{{ asset($plan1['icon_link']) }}" class="plans-images" alt="grow">
                        <div class="plans-text mt-2 {{$plan1->handle == 'community_plan' || $plan1->handle == 'standard_plan' ? 'text-dark' : ''}}{{$plan1->handle == 'plus_plan' ? 'text-white' : ''}}">
                            @if(auth()->user()->shopify_shop_id)

                                ${{$plan1['price_per_month']}}
                            @else
                                @if($plan1->handle != 'plus_plan')
                                    ${{ $plan1['price_per_month'] }} {{--Hiding price on scale plan--}}
                                @endif
                            @endif
                            @if($plan1->handle != 'plus_plan')
                                <sub style="font-size: 16px">{{ trans('billing.per_month') }}</sub>  {{--Hiding price on scale plan--}}
                            @endif
{{--                            <span class="h4">/mo</span>--}}
                            <br>
                            {{--TODO:: Showing plans yearly price--}}
                            @if(auth()->user()->shopify_shop_id)
                            @else
                                <span
                                    class="small-text Roboto bold {{$plan1->handle == 'community_plan' || $plan1->handle == 'standard_plan' ? 'text-dark' : ''}}{{$plan1->handle == 'plus_plan' ? 'text-white' : ''}}">
{{--                                    {{ trans('billing.billed_annually') }}--}}
                                <br>
{{--                                    {{ trans('billing.or_month_to_month', ['price_per_month' => $plan1['price_per_month']]) }}--}}
                            </span>
                            @endif
                        </div>
                        <div class="mt-3">
                            @if(auth()->user()->shopify_shop_id)
                                {{--TODO:: Showing plan select buttons text--}}
                                @if($plan1->handle == 'community_plan')
                                    {{--if monthly plan is coming null from backend then change button text--}}
                                    <a href="{{$plan1->btn_monthly_link}}" class="btn btn-primary w-100">
                                        @if(!$shopify_trial)
                                            Start Trial
                                        @else
                                            {{ trans('billing.shopify_upgrade_to_monthly_btn') }}
                                        @endif
                                    </a>
                                    <a href="{{$plan1->btn_monthly_link}}"
                                       class="btn btn-primary w-100{{$plan1->yearly_class}} {{auth()->user()->password ? 'disabled' : ''}}">

                                        {{--for ${{ $plan1['price_per_year'] }}--}}
                                    </a>

                                @elseif($plan1->handle == 'standard_plan')
                                    <a href="{{$plan1->btn_monthly_link}}"
                                       class="btn btn-primary w-100 {{$plan1->yearly_class}} {{in_array($plan1->id, $plan_ids) ? 'disabled' : ''}}">
                                        {{--if monthly plan is coming null from backend then change button text--}}
                                        @if(!$shopify_trial)
                                            Start Trial
                                        @else
                                            {{ trans('billing.shopify_upgrade_to_monthly_btn') }}
                                        @endif {{--for ${{ $plan1['price_per_year'] }}--}}
                                    </a>
                                @else
                                    {{--TODO:: yearly subsrciption buttons are disabled for the shopify users--}}
                                    {{--                                    <a href="{{$plan1->btn_monthly_link}}"--}}
                                    {{--                                       class="btn btn-block btn-dark yearly-btn {{$plan1->yearly_class}} {{in_array($plan1->id, $plan_ids) ? 'disabled' : ''}}">--}}
                                    {{--                                        --}}{{--if monthly plan is coming null from backend then change button text--}}
                                    {{--                                        {{ trans('billing.shopify_upgrade_to_monthly_btn') }} --}}{{--for ${{ $plan1['price_per_year'] }}--}}
                                    {{--                                    </a>--}}
                                @endif

                                {{--If user came through stripe--}}
                            @elseif(auth()->user()->user_organization()->subscription_type() == 'stripe')

                                @if($plan1->handle == 'free_plan')
                                    <a {{--href="{{$plan1->btn_monthly_link}}"--}}
                                       class="btn btn-block {{--{{$plan1->monthly_class}} {{(in_array($plan1->stripe_monthly_id, $plan_ids) || !$allowed_options) ? 'disabled' : ''}}--}}">
                                        {{--{{ trans('billing.upgrade_to_monthly_btn', ['price_per_month' => $plan1['price_per_month']]) }}--}}
                                    </a>
                                @elseif($plan1->handle == 'community_plan')
                                    {{--comunity plan loop iteration--}}
                                    <a href="{{$plan1->btn_monthly_link}}"
                                       class="btn btn-primary btn-block {{$plan1->monthly_class}} {{in_array($plan1->stripe_monthly_id, $plan_ids) ? 'disabled ' : ''}}">
                                        {{ trans('billing.upgrade_to_monthly_btn', ['price_per_month' => $plan1['price_per_month']]) }}
                                    </a>
                                @elseif($plan1->handle == 'standard_plan')

                                    {{--standard plan loop iteration--}}
                                    <a href="{{$plan1->btn_monthly_link}}"
                                       class="btn btn-primary w-100 {{$plan1->monthly_class}} {{in_array($plan1->stripe_monthly_id, $plan_ids) ? 'disabled ' : ''}}">
                                        {{ trans('billing.upgrade_to_monthly_btn', ['price_per_month' => $plan1['price_per_month']]) }}
                                    </a>
                                @else
                                    {{--plus plan loop iteration--}}
                                    <a href="https://apimio.com/demo/" target="_blank"
                                       class="btn btn-dark w-100 {{$plan1->monthly_class}} {{in_array($plan1->stripe_monthly_id, $plan_ids) ? 'disabled ' : ''}}">Contact Sales</a>
                                @endif
                            @else
                                {{--If user signsup for the 1st time--}}
                                {{--Loop iteration checking--}}
                                @if($plan1->handle == 'free_plan')
                                    <a {{--href="{{$plan1->btn_monthly_link}}"--}}
                                       class="btn btn-block {{--{{$plan1->monthly_class}} {{(in_array($plan1->stripe_monthly_id, $plan_ids) || !$allowed_options) ? 'disabled' : ''}}--}}">
                                        {{--{{ trans('billing.upgrade_to_monthly_btn', ['price_per_month' => $plan1['price_per_month']]) }}--}}
                                    </a>
                                @elseif($plan1->handle == 'community_plan')
                                    {{--Community plan loop iteration--}}
                                    {{--                                        <h3 class="Roboto font-18 text-muted py-4" style="margin-bottom: 28px">--}}
                                    {{--                                            {{ trans('billing.current_plan_btn') }}--}}
                                    {{--standard plan loop iteration--}}
                                    {{--                                                                                    <a href="{{$plan1->btn_yearly_link}}"--}}
                                    {{--                                                                                       class="btn btn-block btn-primary yearly-btn {{$plan1->yearly_class}} {{in_array($plan1->stripe_yearly_id, $plan_ids) ? 'disabled' : ''}}">--}}
                                    {{--                                                                                        {{ trans('billing.upgrade_to_yearly_btn', ['price_per_year' => $plan1['price_per_year']]) }}--}}
                                    {{--                                                                                    </a>--}}
                                    <a href="{{$plan1->btn_monthly_link}}"
                                       class="btn btn-block btn-primary {{$plan1->monthly_class}} {{(in_array($plan1->stripe_monthly_id, $plan_ids) || !$allowed_options) ? 'disabled' : ''}}">
                                        {{ trans('billing.upgrade_to_monthly_btn', ['price_per_month' => $plan1['price_per_month']]) }}
                                    </a>
                                    {{--                                        </h3>--}}
                                @elseif($plan1->handle == 'standard_plan')
                                    {{--standard plan loop iteration--}}
                                    <a href="{{$plan1->btn_monthly_link}}"
                                       class="btn btn-primary w-100 {{$plan1->monthly_class}} {{in_array($plan1->stripe_monthly_id, $plan_ids) ? 'disabled' : ''}}">
                                        {{ trans('billing.upgrade_to_monthly_btn', ['price_per_month' => $plan1['price_per_month']]) }}
                                    </a>
                                @else($plan1->handle == 'plus_plan')
                                    {{--plus plan loop iteration--}}
                                    <a href="https://apimio.com/demo/" target="_blank"
                                       class="btn btn-dark w-100 text-decoration-none {{$plan1->monthly_class}} {{in_array($plan1->stripe_monthly_id, $plan_ids) ? 'disabled' : ''}}">
                                        Contact Sales
                                    </a>
                                @endif
                            @endif



                            <ul class="mt-3 plans-cards-ul">
                                @if($plan1->handle == 'free_plan')
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.no_of_skus',['no_of_products' => $plan1['no_of_products']]) }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.no_of_retailers',['no_of_retailers' => $plan1['no_of_retailers']]) }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.catalogs_included') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.brand_portal') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.asset_management') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.no_of_variants') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.data_quality') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.storage', ['storage' => $plan1['storage']]) }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.language') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.currency') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.single_user') }}</li>
                                @elseif($plan1->handle == 'community_plan')
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.community_plan_no_of_skus',['no_of_products' => $plan1['no_of_products']]) }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.community_plan_catalogs_included') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.community_plan_allowed_templates') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.community_plan_data_importing') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.community_plan_import_templates') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.community_plan_products_variants') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.community_plan_family_attribute') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.community_plan_onboarding_crm') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.community_plan_completeness_score') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.community_plan_image_optimization') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.community_plan_validation_rules') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.community_plan_seo_validation') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.community_plan_channel_automation') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.community_plan_export_templates') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.community_plan_retailer_onboarding_crm') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.community_plan_support') }}</li>
                                @elseif($plan1->handle == 'standard_plan')
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.standard_plan_grow_plan_contents') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.standard_plan_no_of_skus') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.standard_plan_no_of_stores') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.standard_plan_allowed_templates') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.standard_plan_bulk_editing') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.standard_plan_advance_functions') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.standard_plan_DAM') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.standard_plan_team_collaboration') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.standard_plan_product_update_notifications') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.standard_plan_multi_language_support') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.standard_plan_multi_currency_support') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.standard_plan_shopify_magento') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.standard_plan_onboarding_guide') }}</li>
                                    <li><i class="fa fa-check me-2 text-primary"></i>{{ trans('billing.standard_plan_support') }}</li>
                                @else
                                    <li class="plus_plan_text"><i class="fa fa-check me-2 text-success"></i>{{ trans('billing.plus_plan_expand_plan_contents') }}</li>
                                    <li class="plus_plan_text"><i class="fa fa-check me-2 text-success"></i> {{ trans('billing.plus_plan_no_of_skus') }}</li>
                                    <li class="plus_plan_text"><i class="fa fa-check me-2 text-success"></i> {{ trans('billing.plus_plan_no_of_stores') }}</li>
                                    <li class="plus_plan_text"><i class="fa fa-check me-2 text-success"></i> {{ trans('billing.plus_plan_allowed_templates') }}</li>
                                    <li class="plus_plan_text"><i class="fa fa-check me-2 text-success"></i> {{ trans('billing.plus_plan_hierarchies_categories_brands') }}</li>
                                    <li class="plus_plan_text"><i class="fa fa-check me-2 text-success"></i> {{ trans('billing.plus_plan_advance_DAM') }}</li>
                                    <li class="plus_plan_text"><i class="fa fa-check me-2 text-success"></i> {{ trans('billing.plus_plan_account_manager') }}</li>
                                @endif
                            </ul>

                        </div>
                    </div>
                </div>
            @endif
            @endforeach
        </div>
    </div>
    {{-- billing cards end--}}
</div>
{{--/***************** new code end *****************/--}}

@push('footer_scripts')
@endpush

