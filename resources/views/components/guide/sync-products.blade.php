<div>
    <h3 class="Roboto mt-0 semibold text-dark" style="font-size: 20px">{{ trans('apimio_dashboard.catalog_title') }}</h3>
    <p class="Roboto regular">{{ trans('apimio_dashboard.catalog_description') }}</p>

    <!--empty catalog-->
    <div class="d-flex flex-column mb-3">
        <div class="p-2 align-self-center">
            <img src="{{asset('media/guide/no_products_found.png')}}" alt="">
        </div>
        <div class="p-2 align-self-center">
            <a href="{{route('channel.index')}}" style="width: 194px" id="add-catalog" class="btn btn-primary ripplelink disabled-with-text">
                {{ trans('apimio_dashboard.add_catalog_btn') }}
            </a>
        </div>
    </div>

    <!--connect and shared catalog-->
    {{--<div class="d-flex align-items-center justify-content-center">
        <div class="p-5">
            <a href="{{route('products.index')}}">
                <h5 id="vendor_counter"
                    class="m-0 Roboto semibold font-32 text-primary text-center">
                    1
                </h5>
                <p class="card-text mt-2 Roboto text-center text-dark">{{__("Catalogs Connected")}}</p>
            </a>
        </div>
        <div class="p-5">
            <a href="{{route('products.index')}}">
                <h5 id="collection_count"
                    class="m-0 Roboto semibold font-32 text-primary text-center">0</h5>
                <p class="card-text mt-2 Roboto text-dark text-center">{{__("Catalogs Shared")}} </p>
            </a>
        </div>
    </div>--}}
</div>
