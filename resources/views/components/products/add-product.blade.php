<div class="modal fade" id="create_product" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title Poppins " id="exampleModalLabel" style="font-size: 20px">
                    {{trans('products.add_product_modal_title')}}
                </h3>
                <button type="button" class="btn-sm border-0 bg-white fs-24 px-0 pt-0 cancel-product-modal-js" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="pro_addpro_form" action="{{ route("products.store") }}" method="post" class="formStyle">
                    @csrf
                    <div class="form-group mb-4">
                        <label for="sku">{{trans('products.product_label')}}&nbsp;<span style="color: #ff8178">*</span></label>
                        <input type="text" class="form-control @error('sku') is-invalid @enderror" id="sku" name="sku" value="{{ old("sku") }}" required>
                        @error('sku')
                        <span class="text-danger" role="alert">
                            <small>{{ $message }}</small>
                        </span>
                        @enderror
                    </div>
                    <div class="formStyle" data-value="channels" style="display: none;">
                        <label for="" class="ml-1 Roboto bold text-dark">{{trans('products_edit.store')}}</label>
                        <select data-confirm-before-leave="true" placeholder="Select..." id="channels" multiple="multiple" name="channels[][id]" class="form-control hide sumoselect @error('channels') is-invalid @enderror">
                            @foreach($channels as $channel)
                            <option value="{{$channel->id}}" class="Poppins regular text-color"
                                {{$loop->first?'selected':""}}
                                {{$channel->name}}
                            </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group mt-2">
                        <button id="pro_addpro_btn" type="submit" class="btn btn-primary btn-block">
                            {{trans('products.add_product_btn')}}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
