{{--
    @deprecated This file is deprecated and will be removed in the future.
--}}
    <div>
        <p class="Roboto bold">Would you like to use one of your previously saved templates for {{$templates->first()->type}}?</p>
        <div class="table-responsive d-flex flex-row" style="overflow-x: scroll;overflow-y: hidden;">
            @foreach($templates as $key => $template)
                <div class="card border-radius shadow-none border-color card-template mr-3" id="div_templates">
                    <label class="card-body cursor-pointer" for="radio_{{$key}}">
                        @if($template->name === "Shopify Template")
                            <input type="radio" id="radio_{{$key}}" name="template" value="shopify_template">
                        @elseif($template->name === "Magento Template")
                            <input type="radio" id="radio_{{$key}}" name="template" value="magento_template">
                        @elseif($template->name === "Apimio Default")
                            <input type="radio" id="radio_{{$key}}" name="template" value="apimio_default">
                        @else
                            <input type="radio" id="radio_{{$key}}" name="template" value="{{isset($template->id) ? $template->id : null}}">
                        @endif

                        <br>
                        <label>{{$template->name}}</label>
                    </label>
                </div>
            @endforeach
        </div>
    </div>

