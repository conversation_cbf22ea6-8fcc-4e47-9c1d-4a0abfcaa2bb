{{--
    @deprecated This file is deprecated and will be removed in the future.
--}}
<div class="col-6 col-md-7 col-lg-7 assign_formula" data-count="{{$row_count}}">
    <div class="row">
        @if($formula == "assign" || $formula == "slug")
            <div class="col-6 col-md-3 col-lg-3 p-0">
                <div class="form-group">
                    <label for="">Related Field</label>
                    <select name="nodes[data][{{$row_count}}][to][]" style="width: 215px" class="form-control">
                        @if($row_count == 0)
                            <option value="categories" class="Poppins regular text-color">Categories</option>
                        @elseif($row_count == 1)
                            <option value="sku" class="Poppins regular text-color">SKU</option>
                        @elseif($row_count == 2)
                            <option value="product_name" class="Poppins regular text-color">Product Name</option>
                        @else
                            <option value="" class="Poppins regular text-color">Select Field</option>
                            @foreach($heading as $attr_key => $attr_value)
                                @if(isset($row_node['to']))
                                    <option value="{{$attr_key}}" class="Poppins regular text-color" {{($row_node['to'][0] == $attr_key) ? 'selected' : null}} >{{$attr_value}}</option>
                                @else
                                    <option value="{{$attr_key}}" class="Poppins regular text-color">{{$attr_value}}</option>
                                @endif
                            @endforeach

                        @endif
                    </select>
                </div>
            </div>
            <div class="col-6 col-md-4 col-lg-4 p-0">

            </div>
            <div class="col-6 col-md-4 col-lg-4 p-0">

            </div>

        @elseif($formula == "split")
            <div class="col-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label for="">Separator</label>
                    @if(isset($row_node['with']))
                        <input type="text" name="nodes[data][{{$row_count}}][with]" value="{{$row_node['with']}}" style="width: 100%" class="form-control">
                    @else
                        <input type="text" name="nodes[data][{{$row_count}}][with]" style="width: 100%" class="form-control">
                    @endif
                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">Related Field (1)</label>
                    <select name="nodes[data][{{$row_count}}][to][]" style="width: 100%" class="form-control">
                        @if($row_count == 0)
                            <option value="categories" class="Poppins regular text-color">Categories</option>
                        @elseif($row_count == 1)
                            <option value="sku" class="Poppins regular text-color">SKU</option>
                        @elseif($row_count == 2)
                            <option value="product_name" class="Poppins regular text-color">Product Name</option>
                        @else
                            <option value="" class="Poppins regular text-color">Select Field</option>
                            @foreach($heading as $attr_key => $attr_value)
                                @if(isset($row_node['to']))
                                    <option value="{{$attr_key}}" class="Poppins regular text-color" {{($row_node['to'][0] == $attr_key) ? 'selected' : null}} >{{$attr_value}}</option>
                                @else
                                    <option value="{{$attr_key}}" class="Poppins regular text-color">{{$attr_value}}</option>
                                @endif
                            @endforeach
                        @endif
                    </select>
                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">Related Field (2)</label>
                    <select name="nodes[data][{{$row_count}}][to][]" style="width: 100%" class="form-control">
                        <option value="" class="Poppins regular text-color">Select Field</option>
                        @foreach($heading as $attr_key => $attr_value)
                            @if(isset($row_node['to']))
                                <option value="{{$attr_key}}" class="Poppins regular text-color" {{($row_node['to'][1] == $attr_key) ? 'selected' : null}} >{{$attr_value}}</option>
                            @else
                                <option value="{{$attr_key}}" class="Poppins regular text-color">{{$attr_value}}</option>
                            @endif
                        @endforeach
                    </select>
                </div>
            </div>

        @elseif($formula == "merge")
            <div class="col-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label for="">Glue</label>
                    @if(isset($row_node['with']))
                        <input type="text" name="nodes[data][{{$row_count}}][with]" value="{{$row_node['with']}}" style="width: 100%" class="form-control">
                    @else
                        <input type="text" name="nodes[data][{{$row_count}}][with]" style="width: 100%" class="form-control">
                    @endif

                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">Name in Import (2) </label>
                    <select name="nodes[data][{{$row_count}}][from][]" style="width: 100%" class="form-control">
                        <option value="" class="Poppins regular text-color">Select Column</option>
                        @foreach($csv_heading as $h_attribute)
                            @if(isset($row_node['from']))
                                <option value="{{$h_attribute}}" {{($row_node['from'][1] == trim($h_attribute)) ? 'selected' : null}} >{{$h_attribute}}</option>
                            @else
                                <option value="{{$h_attribute}}">{{$h_attribute}}</option>
                            @endif
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">Related Field</label>
                    <select name="nodes[data][{{$row_count}}][to][]" style="width: 100%" class="form-control">
                        @if($row_count == 0)
                            <option value="categories" class="Poppins regular text-color">Categories</option>
                        @elseif($row_count == 1)
                            <option value="sku" class="Poppins regular text-color">SKU</option>
                        @elseif($row_count == 2)
                            <option value="product_name" class="Poppins regular text-color">Product Name</option>
                        @else
                            <option value="" class="Poppins regular text-color">Select Field</option>
                            @foreach($heading as $attr_key => $attr_value)
                                @if(isset($row_node['to']))
                                    <option value="{{$attr_key}}" class="Poppins regular text-color" {{($row_node['to'][0] == $attr_key) ? 'selected' : null}} >{{$attr_value}}</option>
                                @else
                                    <option value="{{$attr_key}}" class="Poppins regular text-color">{{$attr_value}}</option>
                                @endif
                            @endforeach
                        @endif
                    </select>
                </div>
            </div>

        @elseif($formula == 'replace')
            <div class="col-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label for="">Search</label>
                    @if(isset($row_node['replace']))
                        <input type="text" name="nodes[data][{{$row_count}}][replace]" value="{{$row_node['replace']}}" style="width: 100%" class="form-control">
                    @else
                        <input type="text" name="nodes[data][{{$row_count}}][replace]" style="width: 100%" class="form-control">
                    @endif
                </div>
            </div>
            <div class="col-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label for="">With </label>
                    @if(isset($row_node['with']))
                        <input type="text" name="nodes[data][{{$row_count}}][with]" value="{{$row_node['with']}}" style="width: 100%" class="form-control">

                    @else
                        <input type="text" name="nodes[data][{{$row_count}}][with]" style="width: 100%" class="form-control">
                    @endif
                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">Related Field</label>
                    <select name="nodes[data][{{$row_count}}][to][]" style="width: 100%" class="form-control">
                        @if($row_count == 0)
                            <option value="categories" class="Poppins regular text-color">Categories</option>
                        @elseif($row_count == 1)
                            <option value="sku" class="Poppins regular text-color">SKU</option>
                        @elseif($row_count == 2)
                            <option value="product_name" class="Poppins regular text-color">Product Name</option>
                        @else
                            <option value="" class="Poppins regular text-color">Select Field</option>
                            @foreach($heading as $attr_key => $attr_value)
                                @if(isset($row_node['to']))
                                    <option value="{{$attr_key}}" class="Poppins regular text-color" {{($row_node['to'][0] == $attr_key) ? 'selected' : null}} >{{$attr_value}}</option>
                                @else
                                    <option value="{{$attr_key}}" class="Poppins regular text-color">{{$attr_value}}</option>
                                @endif
                            @endforeach
                        @endif
                    </select>
                </div>
            </div>

        @elseif($formula == 'vlookup')

            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">V-Lookup Column ( <a href="javascript:void(0);" data-toggle="modal" data-target="#vlookup_product">Add</a> )</label>
                    <select name="nodes[data][{{$row_count}}][with]" style="width: 100%" class="form-control vlookup_class">
                        <option value="" class="Poppins regular text-color">Select Field</option>
                        @foreach($vlookups as $vlookup)
                            <option value="{{$vlookup->id}}" class="Poppins regular text-color">{{$vlookup->name}}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">Related Field</label>
                    <select name="nodes[data][{{$row_count}}][to][]" style="width: 100%" class="form-control">
                        @if($row_count == 0)
                            <option value="categories" class="Poppins regular text-color">Categories</option>
                        @elseif($row_count == 1)
                            <option value="sku" class="Poppins regular text-color">SKU</option>
                        @elseif($row_count == 2)
                            <option value="product_name" class="Poppins regular text-color">Product Name</option>
                        @else
                            <option value="" class="Poppins regular text-color">Select Field</option>
                            @foreach($heading as $attr_key => $attr_value)
                                @if(isset($row_node['to']))
                                    <option value="{{$attr_key}}" class="Poppins regular text-color" {{($row_node['to'][0] == $attr_key) ? 'selected' : null}} >{{$attr_value}}</option>
                                @else
                                    <option value="{{$attr_key}}" class="Poppins regular text-color">{{$attr_value}}</option>
                                @endif
                            @endforeach
                        @endif
                    </select>
                </div>
            </div>

        @elseif($formula == 'calculate')
            <div class="col-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label for="">
                        With
                        <a href="#" data-toggle="tooltip" id="calculate-tooltip" data-html="true" data-placement="top" title="
                        <div class='text-left'>
                        <b>Example values:</b>
                        <li>+50  Addition</li>
                        <li>-50  Subtraction</li>
                        <li>50%  percentage</li>
                        <li>+50%  add percentage</li>
                        <li>-50%  sub percentage</li>
                        </div>">
                            ( e.g )
                        </a>
                    </label>

                    @if(isset($row_node['with']))
                        <input type="text" name="nodes[data][{{$row_count}}][with]" value="{{$row_node['with']}}" style="width: 100%" class="form-control">

                    @else
                        <input type="text" name="nodes[data][{{$row_count}}][with]" style="width: 100%" class="form-control">
                    @endif
                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">Related Field</label>
                    <select name="nodes[data][{{$row_count}}][to][]" style="width: 100%" class="form-control">
                        @if($row_count == 0)
                            <option value="categories" class="Poppins regular text-color">Categories</option>
                        @elseif($row_count == 1)
                            <option value="sku" class="Poppins regular text-color">SKU</option>
                        @elseif($row_count == 2)
                            <option value="product_name" class="Poppins regular text-color">Product Name</option>
                        @else
                            <option value="" class="Poppins regular text-color">Select Field</option>
                            @foreach($heading as $attr_key => $attr_value)
                                @if(isset($row_node['to']))
                                    <option value="{{$attr_key}}" class="Poppins regular text-color" {{($row_node['to'][0] == $attr_key) ? 'selected' : null}} >{{$attr_value}}</option>
                                @else
                                    <option value="{{$attr_key}}" class="Poppins regular text-color">{{$attr_value}}</option>
                                @endif
                            @endforeach
                        @endif
                    </select>
                </div>
            </div>

        @elseif($formula == 'expand')
            <div class="col-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label for="">Position</label>
                    <select name="nodes[data][{{$row_count}}][replace]" style="width: 100%" class="form-control">
                        <option value="" class="Poppins regular text-color">Select Field</option>
                        @foreach($expand_positions as $position_handle => $position_value)
                            @if(isset($row_node['replace']))
                                <option value="{{$position_handle}}" class="Poppins regular text-color" {{($row_node['replace'] == $position_handle) ? 'selected' : null}} >{{$position_value}}</option>
                            @else
                                <option value="{{$position_handle}}" class="Poppins regular text-color">{{$position_value}}</option>
                            @endif
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-12 col-md-3 col-lg-3">
                <div class="form-group">
                    <label for="">With </label>
                    @if(isset($row_node['with']))
                        <input type="text" name="nodes[data][{{$row_count}}][with]" value="{{$row_node['with']}}" style="width: 100%" class="form-control">
                    @else
                        <input type="text" name="nodes[data][{{$row_count}}][with]" style="width: 100%" class="form-control">
                    @endif
                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-4">
                <div class="form-group">
                    <label for="">Related Field</label>
                    <select name="nodes[data][{{$row_count}}][to][]" style="width: 100%" class="form-control">
                        @if($row_count == 0)
                            <option value="categories" class="Poppins regular text-color">Categories</option>
                        @elseif($row_count == 1)
                            <option value="sku" class="Poppins regular text-color">SKU</option>
                        @elseif($row_count == 2)
                            <option value="product_name" class="Poppins regular text-color">Product Name</option>
                        @else
                            <option value="" class="Poppins regular text-color">Select Field</option>
                            @foreach($heading as $attr_key => $attr_value)
                                @if(isset($row_node['to']))
                                    <option value="{{$attr_key}}" class="Poppins regular text-color" {{($row_node['to'][0] == $attr_key) ? 'selected' : null}} >{{$attr_value}}</option>
                                @else
                                    <option value="{{$attr_key}}" class="Poppins regular text-color">{{$attr_value}}</option>
                                @endif
                            @endforeach
                        @endif
                    </select>
                </div>
            </div>

        @else
            <div class="col-lg-4">
                <b class="align-middle">Coming soon</b>
            </div>

        @endif




        @if($row_count != 0 && $row_count != 1 && $row_count != 2)
            <div class="col-6 col-md-1 col-lg-1 ml-auto">
                <div class="d-flex align-items-center pt-4 ml-auto float-right">
                    <a class="btn btn-delete" onclick="delete_row(this)">
                        <img src="{{asset('media/retailer-dashboard/delete.png')}}" alt="">
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>
