@extends('layouts.app_new' ,['sidebar_display'=>false])
@section('titles','Google Linking')
@section('content')

    <div class="row">

        <!--Left side-->
        <div class="col-xs-12 col-md-12 col-sm-12 col-lg-5 col-xl-4 d-flex align-items-center auth-screen-width" style="margin:auto;margin-top:80px;">
            <div class="flex-column-fluid d-flex flex-column p-10 text-center mt-2">
                <div class="row text-center justify-content-center">
                    <!--Logo-->
                    <div class="d-flex flex-column flex-column-auto mt-3 d-lg-none d-block ">
                        <a href="{{env('APIMIO_URL')}}">
                            <img src="{{asset('media/logo.png')}}" alt="Logo">
                        </a>
                    </div>
                    <div class="col-12 col-sm-10 col-lg-12 col-xl-12 col-md-9 mt-3 page-width">
                        <div class="pb-4">
                            <div class="text-center mb-5">
                                <h1 class="text-center text-dark ">{{ trans('google_and_manual_linking.page_title') }}</h1>
                            </div>

                            <div class="d-flex justify-content-center mb-5">
                                <div class="form-group">
                                    <img src="{{asset('media/Apimio.jpg')}}" alt="logo" style="width: 53px">
                                    <img src="{{asset('media/icons8-link-100.png')}}" alt="link">
                                    <img src="{{asset('media/icons8-google-48.png')}}" alt="google">
                                </div>
                            </div>

                            <div class="d-flex justify-content-center mb-4 mt-4">
                                <div class="form-group">
                                    <p class="text text-center clr-grey mb-0">
                                        {{ trans('google_and_manual_linking.already_linked') }}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-4">
                            <a href="#javascript:void(0)" class="form-control btn btn-primary" onclick="linkGoogle()">
                                {{ trans('google_and_manual_linking.continue_btn') }}
                            </a>
                            <form method="post" action="{{ route('link.user') }}" id="google_linking">
                                @csrf
                                <input type="hidden" value="{{$user->id}}" name="google_id">
                                <input type="hidden" value="{{$existingUser->id}}" name="id">
                            </form>
                        </div>

                        <div class="form-group text-center mt-3">
                            <a href="{{route('home')}}"
                               class="btn btn-outline-primary text-decoration-none w-100">
                                {{ trans('google_and_manual_linking.cancel_btn') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@push('footer_scripts')
    <script>
        function linkGoogle() {
            $('#google_linking').submit();
        }
    </script>
@endpush
