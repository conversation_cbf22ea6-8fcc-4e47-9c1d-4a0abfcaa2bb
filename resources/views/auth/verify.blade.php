@extends('layouts.app_new',['sidebar_display'=>false])
@section('titles','Verify')
@section('content')
<div class="row h-100">
    <div class="col-12 mt-5 pt-4">
        <!--Logo-->
        <div class="d-flex flex-column flex-column-auto text-center">
            <a href="{{env('APIMIO_URL')}}">
                <img src="{{asset('/media/logo.png')}}" alt="Logo">
            </a>
        </div>

        <div class="row justify-content-center">
            <div class="col-12 px-3 mt-3">
                <div class="text-center">
                    <div class="mb-4">
                        <h1 class="text-dark">{{ trans('email_verify.page_title') }}&nbsp;</h1>
                    </div>

                    <div class="form-group mb-5 text-break">
                        <label class="mb-4 text-dark">
                            {{ trans('email_verify.thank_you', ['app_name' => config('app.name')]) }}.
                            <img class="mb-1" src="https://img.icons8.com/color/35/ffffff/confetti.png"
                                alt="sprinkle image" />
                            {{--                                &#127881;--}}
                            <br><br>
                            {{ trans('email_verify.sent_verify_link') }}
                            <br>
                            {{ trans('email_verify.verify_account') }} {{config('app.name')}}.
                        </label>
                    </div>

                    <div class="d-flex flex-column flex-column-auto mb-5">
                        <span>
                            <img src="{{asset('/media/onboarding/verify-email.png')}}" alt="Logo">
                        </span>
                    </div>

                </div>

                <div class="form-group mb-1 text-center">
                    <label class="m-0 text-break text-dark fw-400 fs-16">
                        {{ trans('email_verify.link_not_received') }}
                    </label>
                    <form class="d-inline" method="POST" action="{{ route('verification.resend') }}">
                        @csrf
                        <button type="submit" class="inline-block clr-blue border-0 bg-white fw-700 mb-3"
                            id="verify_resend_btn">
                            {{ trans('email_verify.resend_btn') }}
                        </button>

                    </form>
                </div>
                <div class="form-group mb-4 text-center">
                    <label class="m-0 text-break text-dark fw-400 fs-16">
                        {{ trans('email_verify.change_email') }}
                        <strong>{{ \Illuminate\Support\Facades\Auth::user()->email }}</strong>?
                    </label>
                    <form class="d-inline" method="POST" action="{{ route('logout') }}">
                        @csrf
                        <button type="submit" class="inline-block clr-blue border-0 bg-white fw-700"
                            id="verify_signup_btn">
                            {{ trans('email_verify.signup_again_btn') }}
                        </button>
                    </form>
                </div>
                <div class="form-group text-center">
                    <label class="text-dark text-break fw-400 fs-16">
                        {{ trans('email_verify.need_help') }}
                    </label>
                    <a href="{{('https://support.apimio.com/')}}" target="_blank"
                        class="inline-block clr-blue text-decoration-none fw-700">
                        {{ trans('email_verify.support_team') }}</a>
                </div>
                </form>
            </div>

        </div>
    </div>
</div>

@endsection
