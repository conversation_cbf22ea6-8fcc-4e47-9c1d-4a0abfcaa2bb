@extends('layouts.app_new' ,['sidebar_display'=>false])
@section('titles','Forgot Password')
@section('content')
@push('meta_tags')
<meta name="title" content="Recover your Forgotten Password - Apimio">
<meta name="description" content="Recover your password for your Apimio account password.">
<meta name="keywords" content="Apimio , password recovery, PIM solution">
@endpush
<div class="row">
    <div class="col-12 pt-4 mt-5">
        <!--Logo-->
        <div class="d-flex flex-column flex-column-auto text-center">
            <a href="{{env('APIMIO_URL')}}">
                <img src="{{asset('/media/logo.png')}}" class="h-40" alt="Logo">
            </a>
        </div>

        {{--    style="max-width: 900px; margin: 0 auto"--}}
        <div class="row d-flex justify-content-center">
            <div class="col-12 col-sm-9 col-md-9 col-lg-6 col-xl-4 col-xxl-4 mt-3 auth-screen-width">
                <form id="pass_forget_form" method="POST" class="formStyle" action="{{ route('password.email') }}">
                    @csrf
                    <div class="text-left">
                        <h1 class="text-center mb-3">{{ trans('password_reset.page_title') }}</h1>
                        <p class="text-center clr-grey">{{ trans('password_reset.page_description') }}</p>
                        <div class="mb-2">
                            @if (session('status'))
                            <div class="alert alert-success" role="alert">
                                {{ session('status') }}
                            </div>
                            @endif
                        </div>
                        <div class="form-group">
                            <label for="email">{{ trans('password_reset.email') }}</label>
                            &nbsp;<span class="text-danger">*</span>
                            <input type="email" class="form-control @error('email') is-invalid @enderror" id="email"
                                name="email" placeholder="Email" required autofocus />
                            @error('email')
                            <span class="text-danger" role="alert">
                                <small>{{ $message }}</small>
                            </span>
                            @enderror

                        </div>
                        <input type="hidden" name="role_id"
                            value="{{Request::get('role')?((Request::get('role')=='vendor')?2:3):(old('role_id'))}}"
                            readonly>
                    </div>

                    <div class="form-group mt-40 text-center">
                        <button class="btn btn-primary w-100" id="pass_forget_btn">
                            {{ trans('password_reset.reset_link_btn') }}
                        </button>
                    </div>

                    <div class="form-group text-center mt-2 d-flex align-items-center justify-content-center">
                        <p class="fw-400 mb-0">
                            {{ trans('password_reset.remember_pass') }}
                        </p>
                        <a href="{{route('login')}}" class="inline-block ms-2">
                            {{ trans('password_reset.login_btn') }}
                        </a>
                    </div>

                </form>
            </div>

        </div>

    </div>
</div>

@endsection