<?php ?>
@extends('layouts.app_new')
@section('titles','Billing')
@section('content')

    <!--TODO do not remove code below-->
    <div class="row ">
        <!--mid space-->
        <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-9 col-xxl-7">

            <x-products.page-title name="{{trans('billing.page_title')}}" description="{{trans('billing.page_description')}}"
                                   links="false" button="false"/>

            <div class="mt-4">
                <span class="Roboto semibold text-dark">
                    {{ trans('billing.current_plan') }}
                </span>
            </div>

            <div class="card mt-3 border-0">
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <p class="card-title mb-1 Roboto bold">
                                @if($org->subscription('default') || \Illuminate\Support\Facades\Auth::user()->shopify_shop_id)
                                    {{isset($data['meta_info']->name) ? $data['meta_info']->name : trans('billing.free_plan')}}
                                @else
                                    {{ trans('billing.free') }}
                                @endif
                            </p>
                            <small class="text-muted Roboto">
                                @if(isset($data['yearly_price']))
                                    {{ trans('billing.lifetime_subscription',['time'=> 'Yearly']) }}
                                @else
                                    {{ trans('billing.lifetime_subscription',['time'=> 'Monthly']) }}
                                @endif
                            </small>
                        </div>
                        <div class="col-6">
                            <div class="d-flex flex-row-reverse">
                                <div class="p-2">
                                    @if(!isset($data['max_plan']))
                                        <a href="{{isset($user->shopify_shop_id) ? route('channel.shopify.bill', $channel_id->id) : route('update.billing')}}"
                                           class="btn btn-primary float-right ripplelink">
                                            {{ trans('billing.upgrade_plan_btn') }}
                                        </a>
                                    @endif
                                </div>
                                <div class="p-2">
                                    @if(!isset($user->shopify_shop_id))
                                        <a href="{{route('billing.portal')}}" class="btn btn-outline-primary float-right ripplelink hovereffect">
                                            {{ trans('billing.subscription_details') }}
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="mt-4">
                <span class="Roboto semibold text-dark">
                    {{trans('billing.payment_method')}}
                </span>
            </div>

            <div class="card mt-3 border-0">
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <p class="card-title mb-1 Roboto bold">
                                @if(isset($payment_method))
                                    <span>{{trans('billing.default_card')}}</span>
                                    <img src="{{asset('media/billing/visa-5.png')}}" alt="">
                                    **** **** **** {{ $payment_method->last4 }}
                                @else
                                    {{trans('billing.empty')}}
                                @endif
                            </p>
                        </div>
                    </div>

                    <hr class="mt-3 mb-3 divider">

                    <p class="Roboto my-2">
                        {{trans('billing.accepted_card_types')}}
                    </p>
                </div>
            </div>
        </div>

        <div class="col-0 mx-3" >
            <div class="vertical-divider"></div>
        </div>

    @if($data['meta_info'])
        <!--right side space-->
            <div class="col-12 col-sm-12 col-md-12 col-lg-4 col-xl-4 mb-4">

                <hr class="divider mt-1 d-block d-sm-block d-md-block d-lg-none">

                <div class="mt-5">
                    <h3 class="Roboto semibold text-dark m-0">
                        {{trans('billing.breakdown_summary')}}
                    </h3>
                </div>
                <div class="row mt-3">
                    <div class="col-9">
                        <div class="mt-2">
                            <span class="Roboto semibold text-primary">
                                {{$data['meta_info']->name}}
                            </span>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="mt-2">
                            <span class="Roboto semibold text-primary float-right">
                                 @if(isset($data['yearly_price']))
                                    ${{ $data['yearly_price']*12}}
                                @else
                                    ${{$data['monthly_price']}}
                                @endif
                            </span>
                        </div>
                    </div>
                </div>

                <hr class="mt-3 divider divider-bold">

                <p class="Roboto mt-3 mb-2">
                    {{trans('billing.total_SKU', ['no_of_products' => $data['meta_info']->no_of_products])}}
                </p>
                <p class="Roboto mt-3 mb-2">
                    {{trans('billing.total_store', ['no_of_channels' => $data['meta_info']->no_of_channels])}}
                </p>
                <p class="Roboto mb-2">
                @if($data['meta_info']->no_of_vendors === 0)
                    {{trans('billing.charges_per_vendor')}}
                @else
                    {{trans('billing.vendors', ['no_of_vendors' => $data['meta_info']->no_of_vendors])}}
                @endif
                <!--TODO by Nimra: previous code that I changed to if/else statement above-->
                    {{--Vendors: {{$meta_info->no_of_vendors == 0 ? '$10 will be charge extra for each vendor.' : $meta_info->no_of_vendors}}--}}
                </p>
                <p class="Roboto mb-2">
                @if($data['meta_info']->no_of_retailers === 0)
                    {{trans('billing.charges_per_retailer')}}
                @else
                    {{trans('billing.retailers', ['no_of_retailers' => $data['meta_info']->no_of_retailers])}}
                @endif
                <!--TODO by Nimra: previous code that I changed to if/else statement above-->
                    {{--Retailers: {{$meta_info->no_of_retailers == 0 ? '$10 will be charge extra for each retailer.' : $meta_info->no_of_retailers}}--}}
                </p>
                <p class="Roboto mb-2">
                @if($data['meta_info']->no_of_languages === 0)
                    {{trans('billing.unlimited_languages')}}
                @else
                    {{trans('billing.language')}}
                @endif
                <!--TODO by Nimra: previous code that I changed to if/else statement above-->
                    {{--Retailers: {{$meta_info->no_of_retailers == 0 ? '$10 will be charge extra for each retailer.' : $meta_info->no_of_retailers}}--}}
                </p>

                <p class="Roboto mb-2">
                    {{trans('billing.integrations')}}
                    <img class="ml-1" src="{{asset('media/billing/shopiffy.png')}}" alt="">
                </p>

                <hr class="mt-3 divider divider-bold">

                <div class="row mt-2">
                    <div class="col-6">
                        <div class="mt-2">
                            <span class="Roboto semibold text-primary">{{trans('billing.total_price')}}</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="mt-2">
                        <span class="Roboto semibold text-primary float-right">
                            @if(isset($data['yearly_price']))
                                ${{ $data['yearly_price']*12}}
                            @else
                                ${{$data['monthly_price']}}
                            @endif
                        </span>
                        </div>
                    </div>
                </div>

            </div>
        @endif
    </div>
@endsection
@push('footer_scripts')
@endpush
