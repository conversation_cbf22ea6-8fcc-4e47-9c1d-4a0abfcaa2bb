<?php ?>
@extends('layouts.app_new')
@section('titles','Catalog Products')
@section('content')
    <div>
        <div class="card border-radius shadow-none">
            <div class="card-body p-0">
                <div class="row">
                    <div class="col-12">
                        <div class="mt-4">
                            <h3>
                                <a href="{{route('retailer.index')}}" class="font-24 Poppins m-0 text-light-grey">
                                    {{__("Retailers")}}
                                </a>
                                <span class="font-24 Poppins regular m-0 text-light-grey"> / </span>
                                <span class="font-24 Poppins semibold m-0">
                                    {{__("Name")}}
                                </span>
                            </h3>
                            <div class="mt-3 Roboto regular">{{__("Lorem Ipsum")}}</div>
                        </div>
                    </div>


                </div>
            </div>
        </div>

        <hr class="mt-1 mb-4 divider">

        <form action="" method="GET">
            <div class="row">
                {{--                @if($filter)--}}
                <div class="col-12 col-md-12 col-lg-12 col-xl-3">
                    <div class="d-flex flex-row">
                        <div class="input-group">
                            <input
                                class="form-control search-input"
                                type="search" name="q" placeholder="Search by SKU"
                                value="{{--{{ $request->has("q") ? $request->get("q") : null }}--}}">
                            <button
                                class="p-2 btn btn-dark btn-search ripplelink">
                                <img src="{{asset('media/retailer-dashboard/Search-2.png')}}" alt="">
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-12 col-md-12 col-lg-12 col-xl-9">

                    <ul class="nav nav-pills float-left float-xl-right" id="pills-tab" role="tablist">
                        <li class="nav-item pr-2 py-2" role="presentation">
                            <select name="clone[]" id="Clone" multiple="multiple"
                                    class="btn btn-outline-light regular"
                                    style="height: 38px!important;padding: 10px!important;">
                                {{--                                    @foreach($families as $family)--}}
                                <option value="{{--{{ $family->id }}--}}" class="Poppins regular text-color" {{--{{ $request->has("families") ? (in_array($family->id, $request->get("families")) ? "selected" : null) : null }}--}}>{{--{{ $family->name }}--}}</option>
                                {{--                                    @endforeach--}}
                            </select>
                        </li>
                        <li class="nav-item pr-2 py-2" role="presentation">
                            <select name="catalog[]" id="Catalog" multiple="multiple"
                                    class="btn btn-outline-light regular"
                                    style="height: 38px!important;padding: 10px!important;">
                                {{--                                    @foreach($families as $family)--}}
                                <option value="{{--{{ $family->id }}--}}" class="Poppins regular text-color" {{--{{ $request->has("families") ? (in_array($family->id, $request->get("families")) ? "selected" : null) : null }}--}}>{{--{{ $family->name }}--}}</option>
                                {{--                                    @endforeach--}}
                            </select>
                        </li>
                        <li class="nav-item pr-2 py-2" role="presentation">
                            <select name="families[]" id="AttributeSet" multiple="multiple"
                                    class="btn btn-outline-light regular"
                                    style="height: 38px!important;padding: 10px!important;">
                                {{--                                    @foreach($families as $family)--}}
                                <option value="{{--{{ $family->id }}--}}" class="Poppins regular text-color" {{--{{ $request->has("families") ? (in_array($family->id, $request->get("families")) ? "selected" : null) : null }}--}}>{{--{{ $family->name }}--}}</option>
                                {{--                                    @endforeach--}}
                            </select>
                        </li>
                        <li class="nav-item pr-2 py-2" role="presentation">
                            <select name="vendors[]" id="Vendor" multiple="multiple"
                                    class="btn btn-outline-light regular"
                                    style="height: 38px!important;padding: 10px!important;">
                                {{--                                    @foreach($vendors as $vendor)--}}
                                <option value="{{--{{ $vendor->id }}--}}" class="Poppins regular text-color" {{--{{ $request->has("vendors") ? (in_array($vendor->id, $request->get("vendors")) ? "selected" : null) : null }}--}}>{{--{{ $vendor->name }}--}}</option>
                                {{--                                    @endforeach--}}
                            </select>
                        </li>
                        <li class="nav-item pr-2 py-2" role="presentation">
                            <select name="brands[]" id="Brand" multiple="multiple"
                                    class="btn btn-outline-light regular"
                                    style="height: 38px!important;padding: 10px!important;">
                                {{--                                    @foreach($brands as $brand)--}}
                                <option value="{{--{{ $brand->id }}--}}" class="Poppins regular text-color"  {{--{{ $request->has("brands") ? (in_array($brand->id, $request->get("brands")) ? "selected" : null) : null }}--}}>{{--{{ $brand->name }}--}}</option>
                                {{--                                    @endforeach--}}
                            </select>
                        </li>
                        <li class="nav-item pr-2 py-2" role="presentation">
                            <select name="categories[]" id="Category" multiple="multiple"
                                    class="btn btn-outline-light regular"
                                    style="height: 38px!important;padding: 10px!important;">
                                {{--                                    @foreach($categories as $category)--}}
                                <option value="{{--{{ $category->id }}--}}" class="Poppins regular text-color" {{--{{ $request->has("categories") ? (in_array($category->id, $request->get("categories")) ? "selected" : null) : null }}--}}>{{--{{ $category->name }}--}}</option>
                                {{--                                    @endforeach--}}
                            </select>
                        </li>
                        <li class="nav-item pr-2 py-2" role="presentation">
                            <button class="btn btn-outline-dark hovereffect ripplelink px-3" type="submit">
                                {{__("Apply")}}
                            </button>
                        </li>

                        <li class="nav-item py-2" role="presentation">
                            <a href="{{ route("products.index") }}" class="btn btn-dark-tertiary regular"  style="height: 38px!important;padding: 9px!important;width: 70px">
                                {{__("Clear All")}}
                            </a>
                        </li>
                    </ul>
                </div>
                {{--                @endif--}}
            </div>
        </form>

        <div class="card border-radius shadow-none">
            <div class="card-body">
                <div class="d-flex flex-row mb-3">
                    <div class="p-3">
                        <label for="exampleCheck1"></label><input type="checkbox" class="form-check-input" id="exampleCheck1">
                    </div>
                    <div class="p-2">
                        <button class="btn btn-primary ripplelink" type="submit" style="width: 160px" >{{__("Clone All")}}</button>
                    </div>
                </div>
            </div>
        </div>

        {{--        @foreach()--}}
        <div class="card border-radius border-color shadow-none">
            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <ul class="nav nav-tabs float-left" id="myTab" role="tablist">
                            <li class="px-3 py-2">
                                <label for="exampleCheck1"></label><input type="checkbox" class="form-check-input" id="exampleCheck1">
                            </li>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link active Roboto" style="border-bottom: 0!important;" id="general-tab"
                                   data-toggle="tab" href="#general" role="tab" aria-controls="general" aria-selected="true">
                                    {{__("General Info")}}
                                </a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link Roboto" style="border-bottom: 0!important;" id="images-tab" data-toggle="tab" href="#images" role="tab" aria-controls="images" aria-selected="false">
                                    {{__("Images (25)")}}
                                </a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link Roboto" style="border-bottom: 0!important;" id="variations-tab" data-toggle="tab" href="#variations" role="tab" aria-controls="variations" aria-selected="false">
                                    {{__("Variations (2)")}}
                                </a>
                            </li>
                        </ul>
                        <ul class="float-left float-md-right mt-3 m-md-0">
                            <li class="list-group-item p-0 mb-2">
                                <a href="#" class="btn btn-outline-primary hovereffect">{{__("Preview Original")}}</a>&nbsp;
                                <a href="#" class="btn btn-primary ripplelink" style="width: 130px">{{__("Clone")}}</a>
                            </li>
                        </ul>
                        <hr class="my-3 divider">

                        <div class="tab-content" id="myTabContent">
                            <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                                <div class="row">
                                    <div class="col-12 col-md-10">
                                        <div class="media">
                                            <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3" alt="image" width="160" height="160">
                                            <div class="media-body">
                                                <p class="Roboto text-dark">Luminous Keychain Stress Relief Squishy Pops It Fidget Toys Octopus Push Bubble Pops Fidget Sensory Toy For Autism Special</p>
                                                <p class="Roboto text-grey">Category1, Category2, Category3</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-2">
                                        <p class="Roboto mb-1">{{__("Vendor:")}} <span class="bold">Lorem Ipsum</span></p>
                                        <p class="Roboto">{{__("Brand:")}} <span class="bold">Lorem Ipsum</span></p>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="images" role="tabpanel" aria-labelledby="images-tab">
                                <div class="row">
                                    <div class="col-12">
                                        <h4 class="Roboto bold m-0">{{__("Product Images")}}</h4>
                                    </div>
                                    <div class="col-12">
                                        {{--@foreach()--}}
                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3 mt-3" alt="image" width="160" height="160">
                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3 mt-3" alt="image" width="160" height="160">
                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3 mt-3" alt="image" width="160" height="160">
                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3 mt-3" alt="image" width="160" height="160">
                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3 mt-3" alt="image" width="160" height="160">
                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3 mt-3" alt="image" width="160" height="160">
                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3 mt-3" alt="image" width="160" height="160">
                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3 mt-3" alt="image" width="160" height="160">
                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3 mt-3" alt="image" width="160" height="160">
                                        {{--@endforeach--}}
                                    </div>

                                    <div class="col-12">
                                        <h4 class="Roboto bold mt-4 mb-0">{{__("Social Images")}}</h4>
                                    </div>
                                    <div class="col-12">
                                        {{--@foreach()--}}
                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3 mt-3" alt="image" width="160" height="160">
                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3 mt-3" alt="image" width="160" height="160">
                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3 mt-3" alt="image" width="160" height="160">
                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3 mt-3" alt="image" width="160" height="160">
                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3 mt-3" alt="image" width="160" height="160">
                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3 mt-3" alt="image" width="160" height="160">
                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3 mt-3" alt="image" width="160" height="160">
                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3 mt-3" alt="image" width="160" height="160">
                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" class="align-self-start mr-3 mt-3" alt="image" width="160" height="160">
                                        {{--@endforeach--}}
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="variations" role="tabpanel" aria-labelledby="variations-tab">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="table-responsive">
                                            <table class="table table-borderless text-center">
                                                <thead class="thead-light" style="height: 57px;">
                                                <tr>
                                                    <th scope="col" class="Roboto bold text-dark border-radius-left">
                                                        {{__("IMAGE")}}
                                                    </th>
                                                    <th scope="col" class="Roboto bold text-dark w-10">{{__("SKU")}}
                                                    </th>
                                                    <th scope="col" class="Roboto bold text-dark">{{__("QUANTITY")}}
                                                    </th>
                                                    <th scope="col" class="Roboto bold text-dark">{{__("COLOR")}}
                                                    </th>
                                                    <th scope="col" class="Roboto bold text-dark">{{__("SIZE")}}
                                                    </th>
                                                    <th scope="col" class="Roboto bold text-dark border-radius-right">
                                                        {{__("PRIICE")}}
                                                    </th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                {{--@foreach()--}}
                                                <tr>
                                                    <td class="Roboto regular mt-3">
                                                        <img src="{{asset('media/new-flow/dummyimage.png')}}" alt="image" width="70" height="70">
                                                    </td>
                                                    <td class="Roboto regular mt-3">Navy-2x8</td>
                                                    <td class="Roboto regular mt-3">
                                                        2
                                                    </td>
                                                    <td class="Roboto regular">Navy</td>
                                                    <td class="Roboto regular">2x8</td>
                                                    <td class="Roboto mt-3">
                                                        $42
                                                    </td>
                                                </tr>
                                                {{--@endforeach--}}
                                                </tbody>
                                                {{--                                For empty table--}}
                                                {{--<tbody>
                                                <tr>
                                                    <th scope="row"><img
                                                            src="{{asset('./media/<EMAIL>')}}"
                                                            alt=""></th>
                                                </tr>
                                                <tr>
                                                    <th scope="row"><img
                                                            src="{{asset('./media/<EMAIL>')}}"
                                                            alt=""></th>
                                                </tr>
                                                <tr>
                                                    <th scope="row"><img
                                                            src="{{asset('./media/<EMAIL>')}}"
                                                            alt=""></th>
                                                </tr>
                                                <tr>
                                                    <th scope="row"><img
                                                            src="{{asset('./media/<EMAIL>')}}"
                                                            alt=""></th>
                                                </tr>
                                                </tbody>--}}
                                            </table>
                                        </div>
                                    </div>

                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {{--        @endforeach--}}

    </div>
@endsection
@push('footer_scripts')
    <script>
        $(document).ready(function () {
            $('#Clone').SumoSelect({
                placeholder: 'Clone',
                csvDispCount: 1,
                captionFormat: 'Clone ({0})',
                captionFormatAllSelected: 'Clone ({0})',
                okCancelInMulti: true,
                isClickAwayOk: true,
                selectAll: true,
                search: false,
                searchText: 'Search here',
                noMatch: 'No matches for {0}',

            });
            $('#Catalog').SumoSelect({
                placeholder: 'Catalog',
                csvDispCount: 1,
                captionFormat: 'Catalog ({0})',
                captionFormatAllSelected: 'Catalog ({0})',
                okCancelInMulti: true,
                isClickAwayOk: true,
                selectAll: true,
                search: false,
                searchText: 'Search here',
                noMatch: 'No matches for {0}',

            });
            $('#AttributeSet').SumoSelect({
                placeholder: 'Attribute Set',
                csvDispCount: 1,
                captionFormat: 'Attribute Set ({0})',
                captionFormatAllSelected: 'Attribute Set ({0})',
                okCancelInMulti: true,
                isClickAwayOk: true,
                selectAll: true,
                search: false,
                searchText: 'Search here',
                noMatch: 'No matches for {0}',

            });
            $('#Vendor').SumoSelect({
                placeholder: 'Vendor',
                csvDispCount: 1,
                captionFormat: 'Vendor ({0})',
                captionFormatAllSelected: 'Vendor ({0})',
                okCancelInMulti: true,
                isClickAwayOk: true,
                selectAll: true,
                search: false,
                searchText: 'Search here',
                noMatch: 'No matches for {0}',

            });
            $('#Brand').SumoSelect({
                placeholder: 'Brand',
                csvDispCount: 1,
                captionFormat: 'Brand ({0})',
                captionFormatAllSelected: 'Brand ({0})',
                okCancelInMulti: true,
                isClickAwayOk: true,
                selectAll: true,
                search: false,
                searchText: 'Search here',
                noMatch: 'No matches for {0}',

            });
            $('#Category').SumoSelect({
                placeholder: 'Category',
                csvDispCount: 1,
                captionFormat: 'Category ({0})',
                captionFormatAllSelected: 'Category ({0})',
                okCancelInMulti: true,
                isClickAwayOk: true,
                selectAll: true,
                search: false,
                searchText: 'Search here',
                noMatch: 'No matches for {0}',

            });
            $('#Channel').SumoSelect({
                placeholder: 'Channel',
                csvDispCount: 1,
                captionFormat: 'Channel ({0})',
                captionFormatAllSelected: 'Channel ({0})',
                okCancelInMulti: true,
                isClickAwayOk: true,
                selectAll: true,
                search: false,
                searchText: 'Search here',
                noMatch: 'No matches for {0}',

            });
        });
    </script>
@endpush
