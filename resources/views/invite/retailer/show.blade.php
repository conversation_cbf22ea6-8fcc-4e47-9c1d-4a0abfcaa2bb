{{--<?php ?>--}}

@extends('layouts.app_new')
@section('titles','View Vendor')
@section('content')
    {{--    <x-invite.assets.disconnect-modal text="All catalogs shared with the vendor will be paused. You can connect again anytime." button="Disconnect Vendor" title="Disconnect vendor"/>--}}
    <div class="row">
        <div class="col-12 col-sm-12 col-md-6 col-lg-6">
            <div>
                <h1>
                    <a href="{{route('retailer.index')}}" class="mb-0 clr-grey text-decoration-none">
                        {{trans('retailer_show.page_title')}}
                    </a>
                    <span class="clr-grey"> / </span>
                    <span class="mb-0">
                       {{$data['retailer']->fname  . " " . $data['retailer']->lname}}
                    </span>
                </h1>
                <div class="my-3">{{__($data["retailer"]->designation)}}</div>
            </div>
        </div>
        <div class="col-12 col-sm-12 col-md-6 col-lg-6">
            @switch($data["retailer"]->get_status())
                @case(1)
                <div class="d-flex flex-row mt-3 float-sm-left float-md-right float-lg-right">
                    <div class="p-2">
                        <a href="#" data-id="{{$data['retailer']->id}}" data-retailer-name=""
                           data-toggle="modal" data-target="#disconnect-modal"
                           class="btn btn-outline-danger px-4 hovereffect ripplelink">
                            {{trans('retailer_show.disconnect_btn')}}
                        </a>
                    </div>
                </div>
                @break

                @case(2)
                @if($data['retailer']->email == auth()->user()->email)
                    <div class="d-flex flex-row mt-3 float-sm-left float-md-right float-lg-right">
                        <div class="p-2">
                            <a href="{{ route("invite.accept",$data["retailer"]->id) }}"
                               class="btn btn-outline-success px-4 hovereffect ripplelink">
                                {{trans('retailer_show.connect_btn')}}
                            </a>
                        </div>
                    </div>
                @else
                    <div class="d-flex flex-row mt-3 float-sm-left float-md-right float-lg-right">
                        <div class="p-2">
                            <a href="{{ route("invite.resend",$data["retailer"]->id) }}"
                               class="btn btn-outline-success px-4 hovereffect ripplelink">
                                {{trans('retailer_show.resend_invite_btn')}}
                            </a>
                        </div>
                    </div>
                @endif
                @break

                @case(3)
                <div class="d-flex flex-row mt-3 float-sm-left float-md-right float-lg-right">
                    <div class="p-2">
                        <a href="{{ route("invite.accept", $data["retailer"]->id) }}"
                           class="btn btn-outline-success px-4">{{trans('retailer_show.accept_btn')}}</a>
                    </div>
                    <div class="p-2">
                        <a href="{{ route("invite.decline", $data["retailer"]->id) }}"
                           class="btn btn-outline-danger px-4">{{trans('retailer_show.decline_btn')}}</a>
                    </div>
                </div>

                @break
                @case(4)
                <div class="d-flex flex-column align-items-lg-end">
                    {{--If Vendor email exists show token          --}}
                    @if($data["retailer"]->token)
                        <div>
                            <label class="mb-1">{{trans('retailer_show.copy_link_description')}}</label>
                            <div class="input-group formStyle">
                                <input type="text" class="form-control" id="myInput"
                                       value="{{ route("invite", $data["retailer"]->token??'') }}"
                                       placeholder="{{ route("invite", $data["retailer"]->token??'') }}">
                                <span class="tooltip1">
                                                <span class="tooltiptext" id="myTooltip">Copy to clipboard</span>
                                                <div class="input-group-append ">
                                                        <button type="button"
                                                                class="btn btn-dark shadow-0"
                                                                onclick="copyText()"
                                                                onmouseout="outFunc()"
                                                                style="z-index: 0">
                                                            {{trans('retailer_show.copy_btn')}}
                                                        </button>
                                                </div>
                                     @endif
                                    </span>
                            </div>
                        </div>
                </div>
                @break
                @case(5)
                @if($data['retailer']->email == auth()->user()->email)
                    <div class="d-flex flex-row mt-3 float-sm-left float-md-right float-lg-right">
                        <div class="p-2">
                            <a href="{{ route("invite.accept",$data["retailer"]->id) }}"
                               class="btn btn-outline-success">
                                {{trans('retailer_show.connect_btn')}}
                            </a>
                        </div>
                    </div>
                @else
                    <div class="d-flex flex-row mt-3 float-sm-left float-md-right float-lg-right">
                        <div class="p-2">
                            <a href="{{ route("invite.resend",$data["retailer"]->id) }}"
                               class="btn btn-outline-success">
                                {{trans('retailer_show.resend_invite_btn')}}
                            </a>
                        </div>
                    </div>
                @endif
            @endswitch
        </div>
    </div>
    <div class="card border-radius shadow-none border-color mb-2 mt-3">
        <div class="card-body">
            <div class="row">
                <div class="col-12 col-md-6">
                    <div class="mt-1 mb-3">
                        <h3 class=m-0">{{trans('retailer_show.retailer_detail')}}</h3>
                    </div>
                </div>
                <div class="col-12 col-md-6">
                </div>
                <div class="col-12">
                    <div class="table-responsive">
                        <table class="table">
                            <tbody>
                            <tr>
                                <td>{{trans('retailer_show.first_name')}}</td>
                                <td>
                                    {{$data['retailer']->fname }}
                                </td>

                                <td>{{trans('retailer_show.last_name')}}</td>
                                <td>
                                    {{$data['retailer']->lname }}
                                </td>

                            </tr>
                            <tr>
                                <td>{{trans('retailer_show.phone_number')}}</td>
                                <td>
                                    {{$data['retailer']->phone }}
                                </td>
                                <td>{{trans('retailer_show.shared_catalogs')}}</td>
                                <td>{{ $data["retailer"]->get_catalogs() }}</td>
                            </tr>
                            <tr>
                                <td>{{trans('retailer_show.email')}}</td>
                                <td>{{ $data['retailer']->email }}</td>

                                <td>{{trans('retailer_show.status')}}</td>
                                <td>{!! $data["retailer"]->get_status_badge() !!}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @if(count($data["products"]) > 0)
        @if($data['retailer']->get_status() != 2 )
            @foreach($data["products"] as $product)
                <div class="card border-radius border-color shadow-none">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12">
                                <ul class="nav nav-tabs float-left" id="myTab" role="tablist">
                                    {{--<li class="px-3 py-2">
                                    <label for="exampleCheck1"></label><input type="checkbox" class="form-check-input" id="exampleCheck1">
                                    </li>--}}
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link active Roboto"
                                           id="general-tab"
                                           data-toggle="tab"
                                           href="#general_{{$product->id}}"
                                           role="tab"
                                           aria-controls="general"
                                           aria-selected="true">
                                            {{trans('retailer_show.general_info')}}
                                        </a>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link Roboto"
                                           id="images-tab"
                                           data-toggle="tab"
                                           href="#images_{{$product->id}}"
                                           role="tab"
                                           aria-controls="images"
                                           aria-selected="false">
                                            {{trans('retailer_show.images', ['image_count' => $product->files->count()])}}
                                        </a>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link Roboto"
                                           style="border-bottom: 0!important;"
                                           id="brands-tab"
                                           data-toggle="tab"
                                           href="#brands_{{$product->id}}"
                                           role="tab"
                                           aria-controls="brands"
                                           aria-selected="false">
                                            {{trans('retailer_show.brands', ['brand_count' => $product->brands->count()])}}
                                            {{--{{__("Brands (".$product->brands->count().")")}}--}}
                                        </a>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <a class="nav-link Roboto"
                                           style="border-bottom: 0!important;"
                                           id="variations-tab"
                                           data-toggle="tab"
                                           href="#variations_{{$product->id}}"
                                           role="tab"
                                           aria-controls="variations"
                                           aria-selected="false">
                                            {{trans('retailer_show.variations', ['variants_count' => $product->variants->count()])}}
                                            {{--{{__("Variations (".$product->variants->count().")")}}--}}
                                        </a>
                                    </li>
                                </ul>
                                <ul class="float-left float-md-right mt-3 m-md-0 pl-0">
                                    <li class="list-group-item p-0 mb-2">
                                        @php
                                            $status =  ($product->channels()->cloneAction($product->channels[0]->id,$product))
                                        @endphp
                                        @if($status == 'cloned')
                                            @if($data["vendor"]->get_status() == 5 || $data["vendor"]->get_status() == 3 )
                                                <a class="btn btn-light ripplelink" style="width: 130px">
                                                    {{trans('retailer_show.clone_btn')}}
                                                </a>
                                            @else
                                                <a href="{{ route("vendor.product.clone", [$data["vendor"]->id, $product->id]) }}"
                                                   class="btn btn-success ripplelink" style="width: 130px">
                                                    {{trans('retailer_show.clone_btn')}}
                                                </a>
                                            @endif
                                        @else
                                            @if($data["vendor"]->get_status() == 5  || $data["vendor"]->get_status() == 3)
                                                <a class="btn btn-light ripplelink text-capitalize"
                                                   style="width: 130px">
                                                    {{__($status)}}
                                                </a>
                                            @else
                                                <a href="{{ route("vendor.product.clone", [$data["vendor"]->id, $product->id]) }}"
                                                   class="btn btn-primary ripplelink text-capitalize"
                                                   style="width: 130px">
                                                    {{__($status)}}
                                                </a>
                                            @endif
                                        @endif
                                    </li>
                                </ul>
                                <hr class="my-3 divider">

                                <div class="tab-content" id="myTabContent">
                                    <div class="tab-pane fade show active" id="general_{{$product->id}}" role="tabpanel"
                                         aria-labelledby="general-tab">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="media">
                                                    <img
                                                        src="{{ isset($product->files[0]) ? $product->files[0]->link : asset("img/apimio_default.jpg")}}"
                                                        class="align-self-start mr-3" alt="image" width="160"
                                                        height="160">
                                                    <div class="media-body">
                                                        <p class="Roboto bold text-dark">{{ $product->get_name() }}</p>
                                                        <p class="Roboto bold text-dark">{{ $product->sku }}</p>
                                                        <p class="Roboto text-grey">
                                                            @foreach($product->categories as $cat)
                                                                {{ $cat-> name}} @if($loop->last) , @endif
                                                            @endforeach
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade"
                                         id="images_{{$product->id}}"
                                         role="tabpanel"
                                         aria-labelledby="images-tab">
                                        <div class="row">
                                            <div class="col-12">
                                                <h4 class="Roboto bold m-0">{{trans('retailer_show.product_images')}}</h4>
                                            </div>
                                            <div class="col-12">
                                                <div class="row">
                                                    @foreach($product->files as $file)
                                                        <div class="col-6 col-sm-4 col-md-6 col-lg-2 p-2 pl-3">
                                                            <div class="border-radius">
                                                                <img src="{{ $file->link }}"
                                                                     width="100%"
                                                                     class="border-radius vendor-img">
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade"
                                         id="vendors_{{$product->id}}"
                                         role="tabpanel"
                                         aria-labelledby="vendors-tab">
                                        <div class="row">
                                            <div class="col-12">
                                                <ul>
                                                    @foreach($product->vendors as $v)
                                                        <li>
                                                            {{$v->name}}
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade"
                                         id="brands_{{$product->id}}"
                                         role="tabpanel"
                                         aria-labelledby="brands-tab">
                                        <div class="row">
                                            <div class="col-12">
                                                <ul>
                                                    @foreach($product->brands as $brand)
                                                        <li>
                                                            {{$brand->name}}
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="variations_{{$product->id}}" role="tabpanel"
                                         aria-labelledby="variations-tab">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="table-responsive">
                                                    <table class="table table-borderless text-center">
                                                        <thead class="thead-light">
                                                        <tr>
                                                            <th scope="col">
                                                                {{__("IMAGE")}}
                                                            </th>
                                                            <th scope="col">{{__("SKU")}}
                                                            </th>
                                                            <th scope="col">{{__("NAME")}}
                                                            </th>
                                                            <th scope="col">{{__("PRICE")}}
                                                            </th>
                                                            <th scope="col">{{__("QUANTITY")}}
                                                            </th>
                                                            <th scope="col">
                                                                {{__("BARCODE")}}
                                                            </th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        @foreach($product->variants as $variant)
                                                            <tr>
                                                                <td>
                                                                    <img
                                                                        src="{{$variant->file?$variant->file->link:''}}"
                                                                        alt="image" width="70" height="70">
                                                                </td>
                                                                <td>{{$variant->sku}}</td>
                                                                <td>
                                                                    {{$variant->name}}
                                                                </td>
                                                                <td>$ {{$variant->price}}</td>
                                                                <td>{{$variant->quantity}}</td>
                                                                <td>
                                                                    {{$variant->barcode}}
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
                {{(sizeof($data["products"]) >0)?$data["products"]->links():'' }}
                </div>
                @endif
                @else
                    <x-general.empty-page description="{{trans('retailer_show.Products_empty')}}"/>
                @endif
                <!-- Modal DELETE-->
                <div class="modal fade" id="delete-modal" tabindex="-1" aria-labelledby="deleteModalLabel"
                     aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title Poppins semibold"
                                    id="exampleModalLabel">{{trans('retailer_show.modal_title')}}</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <p class="Poppins regular">
                                    {{trans('retailer_show.modal_description')}}
                                </p>

                                <div class="modal-footer">
                                    <button type="button" data-dismiss="modal" id="delete-cancel-btn"
                                            class="btn Roboto bold float-left btn-black"
                                            style="color: #101010!important;width: 120px;">
                                        {{trans('retailer_show.cancel_btn')}}
                                    </button>
                                    <a href=""
                                       style="width: 196px;" id="delete-vendor"
                                       class="add-btn btn del-btn">
                                        {{trans('retailer_show.delete_btn')}}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <x-invite.disconnect-modal text="Are you sure you want to disconnect vendor?" button="Disconnect"
                                           title="Disconnect Vendor" :account="$data['retailer']"/>
@endsection
@push('footer_scripts')
    <script>
        function copyText() {
            var copyText = document.getElementById("myInput");
            /* Select the text field */
            copyText.select();
            copyText.setSelectionRange(0, 99999); /* For mobile devices */

            /* Copy the text inside the text field */
            navigator.clipboard.writeText(copyText.value);
            var tooltip = document.getElementById("myTooltip");
            tooltip.innerHTML = "Copied Link ";
        }
        function outFunc() {
            var tooltip = document.getElementById("myTooltip");
            tooltip.innerHTML = "Copy to clipboard";
        }
    </script>
@endpush


{{--@extends('layouts.app_new')--}}
{{--@section('titles','View Retailer')--}}
{{--@section('content')--}}
{{--    <div class="d-none">--}}
{{--        <div class="card border-radius shadow-none">--}}
{{--            <div class="card-body p-0">--}}
{{--                <div class="row">--}}
{{--                    <div class="col-12 col-sm-12 col-md-6 col-lg-6">--}}
{{--                        <div class="mt-4">--}}
{{--                            <h3>--}}
{{--                                <a href="{{route('retailer.index')}}" class="font-24 Poppins m-0 text-light-grey">--}}
{{--                                    {{trans('retailer_show.page_title')}}--}}
{{--                                </a>--}}
{{--                                <span class="font-24 Poppins regular m-0 text-light-grey"> / </span>--}}
{{--                                <span class="font-24 Poppins semibold m-0">--}}
{{--                                    {{$data["retailer"]->get_status()== 4?$data["retailer"]->fname:$organization_user->fname)--}}
{{--                                        ." ".--}}
{{--                                        __($data["retailer"]->get_status()== 4?$data["retailer"]->lname:$organization_user->lname}}--}}
{{--                                    </span>--}}
{{--                            </h3>--}}
{{--                            <div class="my-3 Roboto regular">{{__($data["retailer"]->designation)}}</div>--}}
{{--                        </div>--}}
{{--                    </div>--}}

{{--                    <div class="col-12 col-sm-12 col-md-6 col-lg-6">--}}
{{--                        @switch($data["retailer"]->get_status())--}}
{{--                            @case(1)--}}
{{--                            <div class="d-flex flex-row mt-3 float-sm-left float-md-right float-lg-right">--}}
{{--                                <div class="p-2">--}}
{{--                                    <a href="#" data-id="{{$data['retailer']->id}}" data-retailer-name=""--}}
{{--                                       data-toggle="modal" data-target="#disconnect-modal"--}}
{{--                                       class="btn btn-outline-danger px-4 hovereffect ripplelink">--}}
{{--                                        {{trans('retailer_show.disconnect_btn')}}--}}
{{--                                    </a>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                            @break--}}

{{--                            @case(2)--}}
{{--                            @if($data['retailer']->email == auth()->user()->email)--}}
{{--                                <div class="d-flex flex-row mt-3 float-sm-left float-md-right float-lg-right">--}}
{{--                                    <div class="p-2">--}}
{{--                                        a href="{{ route("invite.AcceptVendor",$data["vendor"]->id) }}" class="btn--}}
{{--                                        btn-outline-success px-4 hovereffect ripplelink">--}}
{{--                                        {{trans('retailer_show.connect_btn')}}--}}
{{--                                        </a>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            @else--}}
{{--                                <div class="d-flex flex-row mt-3 float-sm-left float-md-right float-lg-right">--}}
{{--                                    <div class="p-2">--}}
{{--                                        <a href="{{ route("invite.resend",$data["retailer"]->id) }}"--}}
{{--                                           class="btn btn-outline-success px-4 hovereffect ripplelink">--}}
{{--                                            {{trans('retailer_show.resend_invite_btn')}}--}}
{{--                                        </a>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            @endif--}}
{{--                            @break--}}

{{--                            @case(3)--}}
{{--                            <div class="d-flex flex-row mt-3 float-sm-left float-md-right float-lg-right">--}}
{{--                                <div class="p-2">--}}
{{--                                    <a href="#" data-id="{{$data['retailer']->id}}"--}}
{{--                                       data-toggle="modal" data-target="#accept"--}}
{{--                                       class="btn btn-outline-success px-4 hovereffect ripplelink">--}}
{{--                                        {{trans('vendors_show.accept_btn')}}--}}
{{--                                    </a>--}}
{{--                                </div>--}}
{{--                                <div class="p-2">--}}
{{--                                    <a href="{{ route("invite.decline", $data["retailer"]->id) }}"--}}
{{--                                       class="btn btn-outline-danger px-4 hovereffect ripplelink">{{trans('vendors_show.decline_btn')}}</a>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                            @break--}}
{{--                            @case(4)--}}
{{--                            <div class="d-flex flex-column float-sm-left float-md-right float-lg-right">--}}
{{--                                <div class="py-2 Roboto">--}}
{{--                                    {{trans('retailer_show.copy_link_description')}}--}}
{{--                                </div>--}}


{{--                                <div class="input-group" style="width: 29rem">--}}
{{--                                    <input type="text" class="form-control" id="myInput"--}}
{{--                                           value="{{ route("invite", $data["retailer"]->token) }}"--}}
{{--                                           placeholder="{{ route("invite", $data["retailer"]->token) }}">--}}
{{--                                    <span class="tooltip1">--}}
{{--                                            <span class="tooltiptext" id="myTooltip">Copy to clipboard</span>--}}
{{--                                            <div class="input-group-append">--}}
{{--                                                <button type="button" class="btn btn-dark ripplelink shadow-0"--}}
{{--                                                        onclick="copyText()"--}}
{{--                                                        onmouseout="outFunc()"--}}
{{--                                                        style="z-index: 0">--}}
{{--                                                    {{trans('retailer_show.copy_btn')}}--}}
{{--                                                </button>--}}
{{--                                            </div>--}}
{{--                                        </span>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                            @break--}}
{{--                            @case(5)--}}
{{--                            @if($data['retailer']->email == auth()->user()->email)--}}
{{--                                <div class="d-flex flex-row mt-3 float-sm-left float-md-right float-lg-right">--}}
{{--                                    <div class="p-2">--}}
{{--                                        <a href="{{ route("invite.accept",$data["retailer"]->id) }}"--}}
{{--                                           class="btn btn-outline-success px-4 hovereffect ripplelink">--}}
{{--                                            {{trans('retailer_show.connect_btn')}}--}}
{{--                                        </a>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            @else--}}
{{--                                <div class="d-flex flex-row mt-3 float-sm-left float-md-right float-lg-right">--}}
{{--                                    <div class="p-2">--}}
{{--                                        <a href="{{ route("invite.resend",$data["retailer"]->id) }}"--}}
{{--                                           class="btn btn-outline-success px-4 hovereffect ripplelink">--}}
{{--                                            {{trans('retailer_show.resend_invite_btn')}}--}}
{{--                                        </a>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            @endif--}}
{{--                            @break--}}
{{--                        @endswitch--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}

{{--        <div class="card border-radius shadow-none border-color mb-2">--}}
{{--            <div class="card-body">--}}
{{--                <div class="row">--}}
{{--                    <div class="col-12">--}}
{{--                        <div class="mt-1 mb-3">--}}
{{--                            <h3 class="Poppins semibold m-0">{{trans('retailer_show.retailer_detail')}}</h3>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                    <div class="col-12">--}}
{{--                        <div class="table-responsive">--}}
{{--                            <table class="table">--}}
{{--                                <tbody>--}}
{{--                                <tr>--}}
{{--                                    <td class="border-top-0 Roboto bold pl-0"--}}
{{--                                        style="width: 9rem">{{trans('retailer_show.first_name')}}</td>--}}
{{--                                    <td class="border-top-0 Roboto regular pl-0 text-capitalize">{{ $data["retailer"]->get_status()==4?$data["retailer"]->fname:$organization_user->fname }}</td>--}}

{{--                                    <td class="border-top-0 Roboto bold">{{trans('retailer_show.last_name')}}</td>--}}
{{--                                    <td class="border-top-0 Roboto regular text-capitalize">{{ $data["retailer"]->get_status()==4?$data["retailer"]->lname:$organization_user->lname }}</td>--}}

{{--                                </tr>--}}
{{--                                <tr>--}}
{{--                                    <td class="border-top-0 Roboto bold pl-0">{{trans('retailer_show.phone_number')}}</td>--}}
{{--                                    <td class="border-top-0 Roboto regular pl-0">{{ $data["retailer"]->get_status()==4?$data["retailer"]->phone:$organization_user->phone }}</td>--}}

{{--                                    <td class="border-top-0 Roboto bold">{{trans('retailer_show.shared_catalogs')}}</td>--}}
{{--                                    <td class="border-top-0 Roboto regular text-capitalize">{{ $data["retailer"]->get_catalogs()}}</td>--}}
{{--                                </tr>--}}
{{--                                <tr>--}}
{{--                                    <td class="border-top-0 Roboto bold pl-0">{{trans('retailer_show.email')}}</td>--}}
{{--                                    <td class="border-top-0 Roboto regular pl-0">{{ $data["retailer"]->get_status()==4?$data["retailer"]->email:$organization_user->email}}</td>--}}

{{--                                    <td class="border-top-0 Roboto bold">{{trans('retailer_show.status')}}</td>--}}
{{--                                    <td class="border-top-0 Roboto regular">{!! $data["retailer"]->get_status_badge() !!}</td>--}}
{{--                                </tr>--}}

{{--                                </tbody>--}}
{{--                            </table>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--        @if($data["products"] != [] )--}}
{{--            @foreach($data["products"] as $product)--}}
{{--                <div class="card border-radius border-color shadow-none">--}}
{{--                    <div class="card-body">--}}
{{--                        <div class="row">--}}
{{--                            <div class="col-12">--}}
{{--                                <ul class="nav nav-tabs float-left" id="myTab" role="tablist">--}}
{{--                                    --}}{{--<li class="px-3 py-2">--}}
{{--                                    <label for="exampleCheck1"></label><input type="checkbox" class="form-check-input" id="exampleCheck1">--}}
{{--                                    </li>--}}
{{--                                    <li class="nav-item" role="presentation">--}}
{{--                                        <a class="nav-link active Roboto" style="border-bottom: 0!important;"--}}
{{--                                           id="general-tab"--}}
{{--                                           data-toggle="tab" href="#general_{{$product->id}}" role="tab"--}}
{{--                                           aria-controls="general"--}}
{{--                                           aria-selected="true">--}}
{{--                                            {{trans('vendors_show.general_info')}}--}}
{{--                                        </a>--}}
{{--                                    </li>--}}
{{--                                    <li class="nav-item" role="presentation">--}}
{{--                                        <a class="nav-link Roboto" style="border-bottom: 0!important;" id="images-tab"--}}
{{--                                           data-toggle="tab" href="#images_{{$product->id}}" role="tab"--}}
{{--                                           aria-controls="images"--}}
{{--                                           aria-selected="false">--}}
{{--                                            {{trans('vendors_show.images', ['image_count' => $product->files->count()])}}--}}
{{--                                            --}}{{--{{__("Images (".$product->files->count().")")}}--}}
{{--                                        </a>--}}
{{--                                    </li>--}}
{{--                                    --}}{{--                                <li class="nav-item" role="presentation">--}}
{{--                                    --}}{{--                                    <a class="nav-link Roboto" style="border-bottom: 0!important;" id="vendors-tab"--}}
{{--                                    --}}{{--                                       data-toggle="tab" href="#vendors_{{$product->id}}" role="tab" aria-controls="vendors"--}}
{{--                                    --}}{{--                                       aria-selected="false">--}}
{{--                                    --}}{{--                                        {{trans('vendors_show.vendors', ['vendor_count' => $product->vendors->count()])}}--}}
{{--                                    --}}{{--                                        --}}{{----}}{{--{{__("Vendors (".$product->vendors->count().")")}}--}}
{{--                                    --}}{{--                                    </a>--}}
{{--                                    --}}{{--                                </li>--}}
{{--                                    <li class="nav-item" role="presentation">--}}
{{--                                        <a class="nav-link Roboto" style="border-bottom: 0!important;" id="brands-tab"--}}
{{--                                           data-toggle="tab" href="#brands_{{$product->id}}" role="tab"--}}
{{--                                           aria-controls="brands"--}}
{{--                                           aria-selected="false">--}}
{{--                                            {{trans('vendors_show.brands', ['brand_count' => $product->brands->count()])}}--}}
{{--                                            --}}{{--{{__("Brands (".$product->brands->count().")")}}--}}
{{--                                        </a>--}}
{{--                                    </li>--}}
{{--                                    <li class="nav-item" role="presentation">--}}
{{--                                        <a class="nav-link Roboto" style="border-bottom: 0!important;"--}}
{{--                                           id="variations-tab"--}}
{{--                                           data-toggle="tab" href="#variations_{{$product->id}}" role="tab"--}}
{{--                                           aria-controls="variations"--}}
{{--                                           aria-selected="false">--}}
{{--                                            {{trans('vendors_show.variations', ['variants_count' => $product->variants->count()])}}--}}
{{--                                            --}}{{--{{__("Variations (".$product->variants->count().")")}}--}}
{{--                                        </a>--}}
{{--                                    </li>--}}
{{--                                </ul>--}}
{{--                                <ul class="float-left float-md-right mt-3 m-md-0 pl-0">--}}
{{--                                    <li class="list-group-item p-0 mb-2">--}}
{{--                                        @php--}}
{{--                                            $status =  ($product->channels()->cloneAction($product->channels[0]->id,$product))--}}
{{--                                        @endphp--}}
{{--                                        @if($status == 'cloned')--}}
{{--                                            @if($data["retailer"]->get_status() == 5)--}}
{{--                                                <a class="btn btn-light ripplelink" style="width: 130px">--}}
{{--                                                    {{trans('vendors_show.clone_btn')}}--}}
{{--                                                </a>--}}
{{--                                            @else--}}
{{--                                                <a href="{{ route("vendor.product.clone", [$data["retailer"]->id, $product->id]) }}"--}}
{{--                                                   class="btn btn-success ripplelink" style="width: 130px">--}}
{{--                                                    {{trans('vendors_show.clone_btn')}}--}}
{{--                                                </a>--}}
{{--                                            @endif--}}
{{--                                        @else--}}
{{--                                            @if($data["retailer"]->get_status() == 5)--}}
{{--                                                <a class="btn btn-light ripplelink text-capitalize"--}}
{{--                                                   style="width: 130px">--}}
{{--                                                    {{__($status)}}--}}
{{--                                                </a>--}}
{{--                                            @else--}}
{{--                                                <a href="{{ route("vendor.product.clone", [$data["retailer"]->id, $product->id]) }}"--}}
{{--                                                   class="btn btn-primary ripplelink text-capitalize"--}}
{{--                                                   style="width: 130px">--}}
{{--                                                    {{__($status)}}--}}
{{--                                                </a>--}}
{{--                                            @endif--}}
{{--                                        @endif--}}
{{--                                    </li>--}}
{{--                                </ul>--}}
{{--                                <hr class="my-3 divider">--}}

{{--                                <div class="tab-content" id="myTabContent">--}}
{{--                                    <div class="tab-pane fade show active" id="general_{{$product->id}}" role="tabpanel"--}}
{{--                                         aria-labelledby="general-tab">--}}
{{--                                        <div class="row">--}}
{{--                                            <div class="col-12">--}}
{{--                                                <div class="media">--}}
{{--                                                    <img--}}
{{--                                                        src="{{ isset($product->files[0]) ? $product->files[0]->link : asset("img/apimio_default.jpg")}}"--}}
{{--                                                        class="align-self-start mr-3" alt="image" width="160"--}}
{{--                                                        height="160">--}}
{{--                                                    <div class="media-body">--}}
{{--                                                        <p class="Roboto bold text-dark">{{ $product->get_name() }}</p>--}}
{{--                                                        <p class="Roboto bold text-dark">{{ $product->sku }}</p>--}}
{{--                                                        <p class="Roboto text-grey">--}}
{{--                                                            @foreach($product->categories as $cat)--}}
{{--                                                                {{ $cat-> name}} @if($loop->last) , @endif--}}
{{--                                                            @endforeach--}}
{{--                                                        </p>--}}
{{--                                                    </div>--}}
{{--                                                </div>--}}
{{--                                            </div>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                    <div class="tab-pane fade" id="images_{{$product->id}}" role="tabpanel"--}}
{{--                                         aria-labelledby="images-tab">--}}
{{--                                        <div class="row">--}}
{{--                                            <div class="col-12">--}}
{{--                                                <h4 class="Roboto bold m-0">{{trans('vendors_show.product_images')}}</h4>--}}
{{--                                            </div>--}}
{{--                                            <div class="col-12">--}}
{{--                                                <div class="row">--}}
{{--                                                    @foreach($product->files as $file)--}}
{{--                                                        <div class="col-6 col-sm-4 col-md-6 col-lg-2 p-2 pl-3">--}}
{{--                                                            <div class="border-radius">--}}
{{--                                                                <img src="{{ $file->link }}" width="100%"--}}
{{--                                                                     class="border-radius"--}}
{{--                                                                     style="width: 160px;height: 160px;object-fit: contain;"--}}
{{--                                                                     alt="link not found">--}}
{{--                                                            </div>--}}
{{--                                                        </div>--}}
{{--                                                    @endforeach--}}
{{--                                                </div>--}}
{{--                                            </div>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                    <div class="tab-pane fade" id="vendors_{{$product->id}}" role="tabpanel"--}}
{{--                                         aria-labelledby="vendors-tab">--}}
{{--                                        <div class="row">--}}
{{--                                            <div class="col-12">--}}
{{--                                                <ul>--}}
{{--                                                    @foreach($product->vendors as $v)--}}
{{--                                                        <li>--}}
{{--                                                            {{$v->name}}--}}
{{--                                                        </li>--}}
{{--                                                    @endforeach--}}
{{--                                                </ul>--}}
{{--                                            </div>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                    <div class="tab-pane fade" id="brands_{{$product->id}}" role="tabpanel"--}}
{{--                                         aria-labelledby="brands-tab">--}}
{{--                                        <div class="row">--}}
{{--                                            <div class="col-12">--}}
{{--                                                <ul>--}}
{{--                                                    @foreach($product->brands as $brand)--}}
{{--                                                        <li>--}}
{{--                                                            {{$brand->name}}--}}
{{--                                                        </li>--}}
{{--                                                    @endforeach--}}
{{--                                                </ul>--}}
{{--                                            </div>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                    <div class="tab-pane fade" id="variations_{{$product->id}}" role="tabpanel"--}}
{{--                                         aria-labelledby="variations-tab">--}}
{{--                                        <div class="row">--}}
{{--                                            <div class="col-12">--}}
{{--                                                <div class="table-responsive">--}}
{{--                                                    <table class="table table-borderless text-center">--}}
{{--                                                        <thead class="thead-light" style="height: 57px;">--}}
{{--                                                        <tr>--}}
{{--                                                            <th scope="col"--}}
{{--                                                                class="Roboto bold text-dark border-radius-left">--}}
{{--                                                                {{__("IMAGE")}}--}}
{{--                                                            </th>--}}
{{--                                                            <th scope="col"--}}
{{--                                                                class="Roboto bold text-dark w-10">{{__("SKU")}}--}}
{{--                                                            </th>--}}
{{--                                                            <th scope="col" class="Roboto bold text-dark">{{__("NAME")}}--}}
{{--                                                            </th>--}}
{{--                                                            <th scope="col"--}}
{{--                                                                class="Roboto bold text-dark">{{__("PRICE")}}--}}
{{--                                                            </th>--}}
{{--                                                            <th scope="col"--}}
{{--                                                                class="Roboto bold text-dark">{{__("QUANTITY")}}--}}
{{--                                                            </th>--}}
{{--                                                            <th scope="col"--}}
{{--                                                                class="Roboto bold text-dark border-radius-right">--}}
{{--                                                                {{__("BARCODE")}}--}}
{{--                                                            </th>--}}
{{--                                                        </tr>--}}
{{--                                                        </thead>--}}
{{--                                                        <tbody>--}}
{{--                                                        @foreach($product->variants as $variant)--}}
{{--                                                            <tr>--}}
{{--                                                                <td class="Roboto regular mt-3">--}}
{{--                                                                    <img--}}
{{--                                                                        src="{{$variant->file?$variant->file->link:''}}"--}}
{{--                                                                        alt="image" width="70" height="70">--}}
{{--                                                                </td>--}}
{{--                                                                <td class="Roboto regular mt-3">{{$variant->sku}}</td>--}}
{{--                                                                <td class="Roboto regular mt-3">--}}
{{--                                                                    {{$variant->name}}--}}
{{--                                                                </td>--}}
{{--                                                                <td class="Roboto regular">$ {{$variant->price}}</td>--}}
{{--                                                                <td class="Roboto regular">{{$variant->quantity}}</td>--}}
{{--                                                                <td class="Roboto mt-3">--}}
{{--                                                                    {{$variant->barcode}}--}}
{{--                                                                </td>--}}
{{--                                                            </tr>--}}
{{--                                                        @endforeach--}}
{{--                                                        </tbody>--}}
{{--                                                        --}}{{--For empty table--}}
{{--                                                        --}}{{--<tbody>--}}
{{--                                                        <tr>--}}
{{--                                                            <th scope="row"><img--}}
{{--                                                                    src="{{asset('./media/<EMAIL>')}}"--}}
{{--                                                                    alt=""></th>--}}
{{--                                                        </tr>--}}
{{--                                                        <tr>--}}
{{--                                                            <th scope="row"><img--}}
{{--                                                                    src="{{asset('./media/<EMAIL>')}}"--}}
{{--                                                                    alt=""></th>--}}
{{--                                                        </tr>--}}
{{--                                                        <tr>--}}
{{--                                                            <th scope="row"><img--}}
{{--                                                                    src="{{asset('./media/<EMAIL>')}}"--}}
{{--                                                                    alt=""></th>--}}
{{--                                                        </tr>--}}
{{--                                                        <tr>--}}
{{--                                                            <th scope="row"><img--}}
{{--                                                                    src="{{asset('./media/<EMAIL>')}}"--}}
{{--                                                                    alt=""></th>--}}
{{--                                                        </tr>--}}
{{--                                                        </tbody>--}}
{{--                                                    </table>--}}
{{--                                                </div>--}}
{{--                                            </div>--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            @endforeach--}}
{{--        @else--}}
{{--            <x-general.empty-page description="{{trans('vendors_show.Products_empty')}}"/>--}}
{{--        @endif--}}
{{--        <x-invite.disconnect-modal text="Are you sure you want to disconnect retailer?" button="Disconnect"--}}
{{--                                   title="Disconnect Retailer" :account="$data['retailer']"/>--}}
{{--        <x-invite.accept text="Are you sure you want to Accept invite?" button="Accept" title="Accept Invite"--}}
{{--                         :account="$data['retailer']"/>--}}
{{--        {{(sizeof($data["products"]) >0)?$data["products"]->links():'' }}--}}

{{--        --}}{{--        <div class="card border-radius shadow-none mb-2">--}}
{{--        --}}{{--            <div class="card-body">--}}
{{--        --}}{{--                <div class="d-flex flex-row mb-0">--}}
{{--        --}}{{--                    <div class="px-3">--}}
{{--        --}}{{--                        <label for="exampleCheck1"></label>--}}
{{--        --}}{{--                        <input type="checkbox" class="form-check-input" id="exampleCheck1">--}}
{{--        --}}{{--                    </div>--}}
{{--        --}}{{--                    <div class="p-2">--}}
{{--        --}}{{--                        <button class="btn btn-primary ripplelink" type="submit"--}}
{{--        --}}{{--                                style="width: 160px">{{__("Clone All")}}</button>--}}
{{--        --}}{{--                    </div>--}}
{{--        --}}{{--                </div>--}}
{{--        --}}{{--            </div>--}}
{{--        --}}{{--        </div>--}}

{{--        --}}{{--        --}}{{----}}{{--@foreach()--}}
{{--        --}}{{--        <div class="card border-radius border-color shadow-none">--}}
{{--        --}}{{--            <div class="card-body">--}}
{{--        --}}{{--                <div class="row">--}}
{{--        --}}{{--                    <div class="col-12">--}}
{{--        --}}{{--                        <ul class="nav nav-tabs float-left" id="myTab" role="tablist">--}}
{{--        --}}{{--                            <li class="px-3">--}}
{{--        --}}{{--                                <label for="exampleCheck1"></label>--}}
{{--        --}}{{--                                <input type="checkbox" class="form-check-input" style="margin-top: -2px"--}}
{{--        --}}{{--                                       id="exampleCheck1">--}}
{{--        --}}{{--                            </li>--}}
{{--        --}}{{--                            <li class="nav-item" role="presentation">--}}
{{--        --}}{{--                                <a class="nav-link active Roboto" style="border-bottom: 0!important;" id="general-tab"--}}
{{--        --}}{{--                                   data-toggle="tab" href="#general" role="tab" aria-controls="general"--}}
{{--        --}}{{--                                   aria-selected="true">--}}
{{--        --}}{{--                                    {{trans('retailer_show.general_info')}}--}}
{{--        --}}{{--                                </a>--}}
{{--        --}}{{--                            </li>--}}
{{--        --}}{{--                            <li class="nav-item" role="presentation">--}}
{{--        --}}{{--                                <a class="nav-link Roboto" style="border-bottom: 0!important;" id="images-tab"--}}
{{--        --}}{{--                                   data-toggle="tab" href="#images" role="tab" aria-controls="images"--}}
{{--        --}}{{--                                   aria-selected="false">--}}
{{--        --}}{{--                                    {{trans('retailer_show.images')}}--}}
{{--        --}}{{--                                </a>--}}
{{--        --}}{{--                            </li>--}}
{{--        --}}{{--                            <li class="nav-item" role="presentation">--}}
{{--        --}}{{--                                <a class="nav-link Roboto" style="border-bottom: 0!important;" id="vendors-tab"--}}
{{--        --}}{{--                                   data-toggle="tab" href="#vendors" role="tab" aria-controls="vendors"--}}
{{--        --}}{{--                                   aria-selected="false">--}}
{{--        --}}{{--                                    {{trans('retailer_show.vendors')}}--}}
{{--        --}}{{--                                </a>--}}
{{--        --}}{{--                            </li>--}}
{{--        --}}{{--                            <li class="nav-item" role="presentation">--}}
{{--        --}}{{--                                <a class="nav-link Roboto" style="border-bottom: 0!important;" id="brands-tab"--}}
{{--        --}}{{--                                   data-toggle="tab" href="#brands" role="tab" aria-controls="brands"--}}
{{--        --}}{{--                                   aria-selected="false">--}}
{{--        --}}{{--                                    {{trans('retailer_show.brands')}}--}}
{{--        --}}{{--                                </a>--}}
{{--        --}}{{--                            </li>--}}
{{--        --}}{{--                            <li class="nav-item" role="presentation">--}}
{{--        --}}{{--                                <a class="nav-link Roboto" style="border-bottom: 0!important;" id="variations-tab"--}}
{{--        --}}{{--                                   data-toggle="tab" href="#variations" role="tab" aria-controls="variations"--}}
{{--        --}}{{--                                   aria-selected="false">--}}
{{--        --}}{{--                                    {{trans('retailer_show.variations')}}--}}
{{--        --}}{{--                                </a>--}}
{{--        --}}{{--                            </li>--}}
{{--        --}}{{--                        </ul>--}}
{{--        --}}{{--                        <ul class="float-left float-md-right mt-3 m-md-0 pl-0">--}}
{{--        --}}{{--                            <li class="list-group-item p-0 mb-2">--}}
{{--        --}}{{--                                <a href="#" class="btn btn-primary ripplelink" style="width: 130px">{{trans('retailer_show.clone_btn')}}</a>--}}
{{--        --}}{{--                            </li>--}}
{{--        --}}{{--                        </ul>--}}
{{--        --}}{{--                        <hr class="my-3 divider">--}}

{{--        --}}{{--                        <div class="tab-content" id="myTabContent">--}}
{{--        --}}{{--                            <div class="tab-pane fade show active" id="general" role="tabpanel"--}}
{{--        --}}{{--                                 aria-labelledby="general-tab">--}}
{{--        --}}{{--                                <div class="row">--}}
{{--        --}}{{--                                    <div class="col-12">--}}
{{--        --}}{{--                                        <div class="media">--}}
{{--        --}}{{--                                            <img src="{{asset('media/new-flow/dummyimage.png')}}"--}}
{{--        --}}{{--                                                 class="align-self-start mr-3" alt="image" width="160" height="160">--}}
{{--        --}}{{--                                            <div class="media-body">--}}
{{--        --}}{{--                                                <p class="Roboto bold text-dark">Luminous Keychain Stress Relief Squishy--}}
{{--        --}}{{--                                                    Pops--}}
{{--        --}}{{--                                                    It Fidget Toys Octopus Push Bubble Pops Fidget Sensory Toy For--}}
{{--        --}}{{--                                                    Autism Special</p>--}}
{{--        --}}{{--                                                <p class="Roboto bold text-dark">--}}
{{--        --}}{{--                                                    SKU: 543556--}}
{{--        --}}{{--                                                </p>--}}
{{--        --}}{{--                                                <p class="Roboto text-dark ">--}}
{{--        --}}{{--                                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Justo--}}
{{--        --}}{{--                                                    pharetra facilisi sollicitudin turpis duis molestie consequat.--}}
{{--        --}}{{--                                                    Dictum lobortis a tellus nisi ac eu libero nunc. Purus dui donec--}}
{{--        --}}{{--                                                    aliquam ornare cras a accumsan gravida. Id faucibus massa tellus--}}
{{--        --}}{{--                                                    porttitor quis a vitae id purus. Pharetra nulla vel a nullam--}}
{{--        --}}{{--                                                    ultrices turpis--}}
{{--        --}}{{--                                                </p>--}}
{{--        --}}{{--                                                <p class="Roboto text-grey">Category1, Category2, Category3</p>--}}
{{--        --}}{{--                                            </div>--}}
{{--        --}}{{--                                        </div>--}}
{{--        --}}{{--                                    </div>--}}
{{--        --}}{{--                                </div>--}}
{{--        --}}{{--                            </div>--}}
{{--        --}}{{--                            <div class="tab-pane fade" id="images" role="tabpanel" aria-labelledby="images-tab">--}}
{{--        --}}{{--                                <div class="row">--}}
{{--        --}}{{--                                    <div class="col-12">--}}
{{--        --}}{{--                                        <h4 class="Roboto bold m-0">{{trans('retailer_show.product_images')}}</h4>--}}
{{--        --}}{{--                                    </div>--}}
{{--        --}}{{--                                    --}}{{----}}{{----}}{{----}}{{--@foreach()--}}
{{--        --}}{{--                                    <div class="col-6 col-sm-4 col-md-6 col-lg-2 p-2 pl-3">--}}
{{--        --}}{{--                                        <div class="border-radius">--}}
{{--        --}}{{--                                            <img src="{{asset('media/new-flow/dummyimage.png')}}" width="100%" class="border-radius"--}}
{{--        --}}{{--                                                 style="width: 160px;height: 160px;object-fit: contain;">--}}
{{--        --}}{{--                                        </div>--}}
{{--        --}}{{--                                    </div>--}}
{{--        --}}{{--                                    --}}{{----}}{{----}}{{----}}{{--@endforeach--}}
{{--        --}}{{--                                    <div class="col-12">--}}
{{--        --}}{{--                                        <h4 class="Roboto bold mt-4 mb-0">{{__("Social Images")}}</h4>--}}
{{--        --}}{{--                                    </div>--}}
{{--        --}}{{--                                    --}}{{----}}{{----}}{{----}}{{--@foreach()--}}
{{--        --}}{{--                                    <div class="col-6 col-sm-4 col-md-6 col-lg-2 p-2 pl-3">--}}
{{--        --}}{{--                                        <div class="border-radius">--}}
{{--        --}}{{--                                            <img src="{{asset('media/new-flow/dummyimage.png')}}" width="100%" class="border-radius"--}}
{{--        --}}{{--                                                 style="width: 160px;height: 160px;object-fit: contain;">--}}
{{--        --}}{{--                                        </div>--}}
{{--        --}}{{--                                    </div>--}}
{{--        --}}{{--                                    --}}{{----}}{{----}}{{----}}{{--@endforeach--}}
{{--        --}}{{--                                </div>--}}
{{--        --}}{{--                            </div>--}}
{{--        --}}{{--                            <div class="tab-pane fade" id="vendors" role="tabpanel" aria-labelledby="vendors-tab">--}}
{{--        --}}{{--                                <div class="row">--}}
{{--        --}}{{--                                    <div class="col-12">--}}
{{--        --}}{{--                                        <ul>--}}
{{--        --}}{{--                                            <li>--}}
{{--        --}}{{--                                                Lorem ipsum dolor sit amet, consectetur adipiscing elit.--}}
{{--        --}}{{--                                            </li>--}}
{{--        --}}{{--                                            <li>--}}
{{--        --}}{{--                                                Lorem ipsum dolor sit amet, consectetur adipiscing elit.--}}
{{--        --}}{{--                                            </li>--}}
{{--        --}}{{--                                        </ul>--}}
{{--        --}}{{--                                    </div>--}}
{{--        --}}{{--                                </div>--}}
{{--        --}}{{--                            </div>--}}
{{--        --}}{{--                            <div class="tab-pane fade" id="brands" role="tabpanel" aria-labelledby="brands-tab">--}}
{{--        --}}{{--                                <div class="row">--}}
{{--        --}}{{--                                    <div class="col-12">--}}
{{--        --}}{{--                                        <ul>--}}
{{--        --}}{{--                                            <li>--}}
{{--        --}}{{--                                                Lorem ipsum dolor sit amet, consectetur adipiscing elit.--}}
{{--        --}}{{--                                            </li>--}}
{{--        --}}{{--                                            <li>--}}
{{--        --}}{{--                                                Lorem ipsum dolor sit amet, consectetur adipiscing elit.--}}
{{--        --}}{{--                                            </li>--}}
{{--        --}}{{--                                        </ul>--}}
{{--        --}}{{--                                    </div>--}}
{{--        --}}{{--                                </div>--}}
{{--        --}}{{--                            </div>--}}
{{--        --}}{{--                            <div class="tab-pane fade" id="variations" role="tabpanel" aria-labelledby="variations-tab">--}}
{{--        --}}{{--                                <div class="row">--}}
{{--        --}}{{--                                    <div class="col-12">--}}
{{--        --}}{{--                                        <div class="table-responsive">--}}
{{--        --}}{{--                                            <table class="table table-borderless text-center">--}}
{{--        --}}{{--                                                <thead class="thead-light" style="height: 57px;">--}}
{{--        --}}{{--                                                <tr>--}}
{{--        --}}{{--                                                    <th scope="col" class="Roboto bold text-dark border-radius-left">--}}
{{--        --}}{{--                                                        {{__("IMAGE")}}--}}
{{--        --}}{{--                                                    </th>--}}
{{--        --}}{{--                                                    <th scope="col" class="Roboto bold text-dark w-10">{{__("SKU")}}--}}
{{--        --}}{{--                                                    </th>--}}
{{--        --}}{{--                                                    <th scope="col" class="Roboto bold text-dark">{{__("QUANTITY")}}--}}
{{--        --}}{{--                                                    </th>--}}
{{--        --}}{{--                                                    <th scope="col" class="Roboto bold text-dark">{{__("COLOR")}}--}}
{{--        --}}{{--                                                    </th>--}}
{{--        --}}{{--                                                    <th scope="col" class="Roboto bold text-dark">{{__("SIZE")}}--}}
{{--        --}}{{--                                                    </th>--}}
{{--        --}}{{--                                                    <th scope="col" class="Roboto bold text-dark border-radius-right">--}}
{{--        --}}{{--                                                        {{__("PRIICE")}}--}}
{{--        --}}{{--                                                    </th>--}}
{{--        --}}{{--                                                </tr>--}}
{{--        --}}{{--                                                </thead>--}}
{{--        --}}{{--                                                <tbody>--}}
{{--        --}}{{--                                                --}}{{----}}{{----}}{{----}}{{--@foreach()--}}
{{--        --}}{{--                                                <tr>--}}
{{--        --}}{{--                                                    <td class="Roboto regular mt-3">--}}
{{--        --}}{{--                                                        <img src="{{asset('media/new-flow/dummyimage.png')}}"--}}
{{--        --}}{{--                                                             alt="image" width="70" height="70">--}}
{{--        --}}{{--                                                    </td>--}}
{{--        --}}{{--                                                    <td class="Roboto regular mt-3">Navy-2x8</td>--}}
{{--        --}}{{--                                                    <td class="Roboto regular mt-3">--}}
{{--        --}}{{--                                                        2--}}
{{--        --}}{{--                                                    </td>--}}
{{--        --}}{{--                                                    <td class="Roboto regular">Navy</td>--}}
{{--        --}}{{--                                                    <td class="Roboto regular">2x8</td>--}}
{{--        --}}{{--                                                    <td class="Roboto mt-3">--}}
{{--        --}}{{--                                                        $42--}}
{{--        --}}{{--                                                    </td>--}}
{{--        --}}{{--                                                </tr>--}}
{{--        --}}{{--                                                --}}{{----}}{{----}}{{----}}{{--@endforeach--}}
{{--        --}}{{--                                                </tbody>--}}
{{--        --}}{{--                                                --}}{{----}}{{----}}{{----}}{{--For empty table--}}
{{--        --}}{{--                                                --}}{{----}}{{----}}{{----}}{{--<tbody>--}}
{{--        --}}{{--                                                <tr>--}}
{{--        --}}{{--                                                    <th scope="row"><img--}}
{{--        --}}{{--                                                            src="{{asset('./media/<EMAIL>')}}"--}}
{{--        --}}{{--                                                            alt=""></th>--}}
{{--        --}}{{--                                                </tr>--}}
{{--        --}}{{--                                                <tr>--}}
{{--        --}}{{--                                                    <th scope="row"><img--}}
{{--        --}}{{--                                                            src="{{asset('./media/<EMAIL>')}}"--}}
{{--        --}}{{--                                                            alt=""></th>--}}
{{--        --}}{{--                                                </tr>--}}
{{--        --}}{{--                                                <tr>--}}
{{--        --}}{{--                                                    <th scope="row"><img--}}
{{--        --}}{{--                                                            src="{{asset('./media/<EMAIL>')}}"--}}
{{--        --}}{{--                                                            alt=""></th>--}}
{{--        --}}{{--                                                </tr>--}}
{{--        --}}{{--                                                <tr>--}}
{{--        --}}{{--                                                    <th scope="row"><img--}}
{{--        --}}{{--                                                            src="{{asset('./media/<EMAIL>')}}"--}}
{{--        --}}{{--                                                            alt=""></th>--}}
{{--        --}}{{--                                                </tr>--}}
{{--        --}}{{--                                                </tbody>--}}
{{--        --}}{{--                                            </table>--}}
{{--        --}}{{--                                        </div>--}}
{{--        --}}{{--                                    </div>--}}
{{--        --}}{{--                                </div>--}}
{{--        --}}{{--                            </div>--}}
{{--        --}}{{--                        </div>--}}
{{--        --}}{{--                    </div>--}}
{{--        --}}{{--                </div>--}}
{{--        --}}{{--            </div>--}}
{{--        --}}{{--        </div>--}}
{{--        --}}{{--        --}}{{----}}{{--@endforeach--}}

{{--    </div>--}}
{{--    <!-- Modal DELETE-->--}}
{{--    <div class="modal fade" id="delete-modal" tabindex="-1" aria-labelledby="deleteModalLabel"--}}
{{--         aria-hidden="true">--}}
{{--        <div class="modal-dialog modal-dialog-centered">--}}
{{--            <div class="modal-content">--}}
{{--                <div class="modal-header">--}}
{{--                    <h5 class="modal-title Poppins semibold"--}}
{{--                        id="exampleModalLabel">{{trans('retailer_show.modal_title')}}</h5>--}}
{{--                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">--}}
{{--                        <span aria-hidden="true">&times;</span>--}}
{{--                    </button>--}}
{{--                </div>--}}
{{--                <div class="modal-body">--}}
{{--                    <p class="Poppins regular">--}}
{{--                        {{trans('retailer_show.modal_description')}}--}}
{{--                    </p>--}}

{{--                    <div class="modal-footer p-0">--}}
{{--                        <button type="button" data-dismiss="modal" id="delete-cancel-btn"--}}
{{--                                class="btn btn-dark-tertiary float-left shadow"--}}
{{--                                style="color: #101010!important;width: 120px;">--}}
{{--                            {{trans('retailer_show.cancel_btn')}}--}}
{{--                        </button>--}}
{{--                        --}}{{--                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>--}}
{{--                        <a href=""--}}
{{--                           style="width: 196px;" id="delete-vendor"--}}
{{--                           class="btn btn-danger ripplelink shadow">{{trans('retailer_show.delete_btn')}}--}}
{{--                        </a>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--    </div>--}}



{{--    --}}{{--    /************************ new code start ***************************/--}}
{{--    <div class="row">--}}
{{--        --}}{{--   title start--}}
{{--        <div class="col-12 px-0">--}}
{{--            <div class="row d-flex align-items-center">--}}
{{--                <div class="col-12 col-md-6">--}}
{{--                    <h2 class="clr-grey mb-0">--}}
{{--                        <a href="http://localhost:8000/products/family" class="clr-grey text-decoration-none">--}}
{{--                            Retailer--}}
{{--                        </a>--}}
{{--                        /--}}
{{--                        <sapn class="text-dark"> James Danial</sapn>--}}
{{--                    </h2>--}}
{{--                    <p class="mb-0"> Lorem Ipsum </p>--}}
{{--                </div>--}}
{{--                <div class="col-12 col-md-6 d-flex justify-content-lg-end">--}}
{{--                    <a href="" class="btn btn-outline-success px-5 text-decoration-none">Accept</a>--}}
{{--                    <a href="" class="btn btn-outline-danger ms-2 px-5 text-decoration-none">Decline</a>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--        --}}{{--   title end--}}
{{--        --}}{{--        show card start--}}
{{--        <div class="col-12 border rounded mt-3 px-0">--}}
{{--            <div class="row p-3">--}}
{{--                <div class="col-12 mb-3"><h4 class="mb-0">Retailer DETAILS</h4></div>--}}
{{--                <div class="col-12 col-md-6">--}}
{{--                    <div class="d-flex"><p class="mb-3">First Name:</p>--}}
{{--                        <p class="ms-5 mb-3">james</p></div>--}}
{{--                    <div class="d-flex"><p class="mb-3">Phone Number:</p>--}}
{{--                        <p class="ms-5 mb-3">+9103793111</p></div>--}}
{{--                    <div class="d-flex"><p class="mb-3">Email ID:</p>--}}
{{--                        <p class="ms-5 mb-3"><EMAIL></p></div>--}}
{{--                </div>--}}
{{--                <div class="col-12 col-md-6">--}}
{{--                    <div class="d-flex"><p class="mb-3">Last Name:</p>--}}
{{--                        <p class="ms-5 mb-3">Danial</p></div>--}}
{{--                    <div class="d-flex"><p class="mb-3">Shared store::</p>--}}
{{--                        <p class="ms-5 mb-3">Default</p></div>--}}
{{--                    <div class="d-flex"><p class="mb-3">Status:</p>--}}
{{--                        <p class="ms-5 mb-3 status status-publish">Invite Sent</p></div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--        --}}{{--        show card end--}}
{{--        --}}{{--        sync product start--}}
{{--        <div class="col-12 mt-3">--}}
{{--            <div class="row">--}}
{{--                <div class="col-12 d-flex align-items-center px-0">--}}
{{--                    <input class="form-check-input mt-0" type="checkbox" value="" aria-label="sync product">--}}
{{--                    <a href="#" class="btn btn-sm btn-primary ms-3">Clone All</a>--}}
{{--                </div>--}}
{{--                <div class="col-12 border p-3 mt-2">--}}
{{--                    <div class="d-flex justify-content-between">--}}
{{--                        <ul class="nav nav-pills" id="pills-tab" role="tablist">--}}
{{--                            <li class="nav-item" role="presentation">--}}
{{--                                <button class="nav-link active general-info-css" id="general-info1"--}}
{{--                                        data-bs-toggle="pill" data-bs-target="#general-info" type="button" role="tab"--}}
{{--                                        aria-controls="general-info" aria-selected="true"><input--}}
{{--                                        class="form-check-input mt-0 me-2" type="checkbox" value=""--}}
{{--                                        aria-label="general-info"> General Info--}}
{{--                                </button>--}}
{{--                            </li>--}}
{{--                            <li class="nav-item" role="presentation">--}}
{{--                                <button class="nav-link" id="images1" data-bs-toggle="pill" data-bs-target="#images"--}}
{{--                                        type="button" role="tab" aria-controls="images" aria-selected="false">Images--}}
{{--                                    (25)--}}
{{--                                </button>--}}
{{--                            </li>--}}
{{--                            <li class="nav-item" role="presentation">--}}
{{--                                <button class="nav-link" id="vendors1" data-bs-toggle="pill" data-bs-target="#vendors"--}}
{{--                                        type="button" role="tab" aria-controls="vendors" aria-selected="false">Vendors--}}
{{--                                    (2)--}}
{{--                                </button>--}}
{{--                            </li>--}}
{{--                            <li class="nav-item" role="presentation">--}}
{{--                                <button class="nav-link" id="brands1" data-bs-toggle="pill" data-bs-target="#brands"--}}
{{--                                        type="button" role="tab" aria-controls="brands" aria-selected="false">Brands--}}
{{--                                    (14)--}}
{{--                                </button>--}}
{{--                            </li>--}}
{{--                            <li class="nav-item" role="presentation">--}}
{{--                                <button class="nav-link" id="variation1" data-bs-toggle="pill"--}}
{{--                                        data-bs-target="#variation" type="button" role="tab" aria-controls="variation"--}}
{{--                                        aria-selected="false">Variations (2)--}}
{{--                                </button>--}}
{{--                            </li>--}}
{{--                        </ul>--}}
{{--                        <a href="#" class="btn btn-sm btn-primary ms-3">Clone</a>--}}
{{--                    </div>--}}
{{--                    <hr class="m-0">--}}
{{--                    <div class="tab-content mt-2" id="pills-tabContent">--}}
{{--                        --}}{{--  general info--}}
{{--                        <div class="tab-pane fade show active" id="general-info" role="tabpanel"--}}
{{--                             aria-labelledby="general">--}}
{{--                            <div class="d-flex flex-column flex-md-row">--}}
{{--                                <div>--}}
{{--                                    <img src="{{asset('assets/images/image 34.png')}}"--}}
{{--                                         class="img-fluid card-img rounded-start" alt="img">--}}
{{--                                </div>--}}
{{--                                <div class="mt-3 mt-lg-0 ms-3">--}}
{{--                                    <h4>Easy-Going Reversible Couch Cover for 3 Cushion Couch Sofa Cover for Dogs Water--}}
{{--                                        Resistant Furniture Protector Cover with Foam</h4>--}}
{{--                                    <h4>SKU: 543556</h4>--}}
{{--                                    <p>Other Sizes Can Be Buy on the Product Description. Chair(25''), Loveseat(46''),--}}
{{--                                        Sofa (66”), Oversized Sofa (78”), Recliner(23''), Oversized Recliner(28''),--}}
{{--                                        Futon(70''), L Shape Sofa. Super durable 3-layers quilted fabric, elegant--}}
{{--                                        quilted texture, thick microfiber, colorfast, water resistant. Two color--}}
{{--                                        options. Double the use.</p>--}}
{{--                                    <p class="clr-grey">Category 1, Category2, Category 3, Category 4</p>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        --}}{{-- images--}}
{{--                        <div class="tab-pane fade" id="images" role="tabpanel" aria-labelledby="images">--}}
{{--                            <h4>Product Images</h4>--}}
{{--                            <div class="d-flex flex-column flex-md-row flex-wrap px-0">--}}
{{--                                <img src="{{asset('assets/images/image 34.png')}}"--}}
{{--                                     class="img-fluid card-img rounded-start me-3 mb-3" alt="img">--}}
{{--                                <img src="{{asset('assets/images/image 34.png')}}"--}}
{{--                                     class="img-fluid card-img rounded-start me-3 mb-3" alt="img">--}}
{{--                                <img src="{{asset('assets/images/image 34.png')}}"--}}
{{--                                     class="img-fluid card-img rounded-start me-3 mb-3" alt="img">--}}
{{--                                <img src="{{asset('assets/images/image 34.png')}}"--}}
{{--                                     class="img-fluid card-img rounded-start me-3 mb-3" alt="img">--}}
{{--                                <img src="{{asset('assets/images/image 34.png')}}"--}}
{{--                                     class="img-fluid card-img rounded-start me-3 mb-3" alt="img">--}}

{{--                            </div>--}}
{{--                            <h4 class="mt-3">Social Images</h4>--}}
{{--                            <div class="d-flex flex-column flex-lg-row flex-wrap px-0">--}}
{{--                                <img src="{{asset('assets/images/image 34.png')}}"--}}
{{--                                     class="img-fluid card-img rounded-start me-3 mb-3" alt="img">--}}
{{--                                <img src="{{asset('assets/images/image 34.png')}}"--}}
{{--                                     class="img-fluid card-img rounded-start me-3 mb-3" alt="img">--}}
{{--                                <img src="{{asset('assets/images/image 34.png')}}"--}}
{{--                                     class="img-fluid card-img rounded-start me-3 mb-3" alt="img">--}}
{{--                                <img src="{{asset('assets/images/image 34.png')}}"--}}
{{--                                     class="img-fluid card-img rounded-start me-3 mb-3" alt="img">--}}
{{--                                <img src="{{asset('assets/images/image 34.png')}}"--}}
{{--                                     class="img-fluid card-img rounded-start me-3 mb-3" alt="img">--}}
{{--                                <img src="{{asset('assets/images/image 34.png')}}"--}}
{{--                                     class="img-fluid card-img rounded-start me-3 mb-3" alt="img">--}}

{{--                            </div>--}}
{{--                        </div>--}}
{{--                        --}}{{-- vendors--}}
{{--                        <div class="tab-pane fade" id="vendors" role="tabpanel" aria-labelledby="vendors">--}}
{{--                            <ul>--}}
{{--                                <li class="mb-2 fw-400">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>--}}
{{--                                <li class="mb-2 fw-400">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>--}}
{{--                                <li class="mb-2 fw-400">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>--}}
{{--                                <li class="mb-2 fw-400">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>--}}
{{--                                <li class="mb-2 fw-400">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>--}}
{{--                            </ul>--}}
{{--                        </div>--}}
{{--                        --}}{{-- Brands--}}
{{--                        <div class="tab-pane fade" id="brands" role="tabpanel" aria-labelledby="brands">--}}
{{--                            <ul>--}}
{{--                                <li class="mb-2 fw-400">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>--}}
{{--                                <li class="mb-2 fw-400">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>--}}
{{--                                <li class="mb-2 fw-400">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>--}}
{{--                                <li class="mb-2 fw-400">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>--}}
{{--                                <li class="mb-2 fw-400">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>--}}
{{--                            </ul>--}}
{{--                        </div>--}}

{{--                        --}}{{-- variants--}}
{{--                        <div class="tab-pane fade" id="variation" role="tabpanel" aria-labelledby="variation">--}}
{{--                            <table class="table">--}}
{{--                                <thead>--}}
{{--                                <tr>--}}
{{--                                    <th scope="col">Image</th>--}}
{{--                                    <th scope="col">SKU</th>--}}
{{--                                    <th scope="col">Quantity</th>--}}
{{--                                    <th scope="col">Size</th>--}}
{{--                                    <th scope="col">Price</th>--}}
{{--                                    <th scope="col" class="text-end">Action</th>--}}
{{--                                </tr>--}}
{{--                                </thead>--}}
{{--                                <tbody>--}}
{{--                                <tr>--}}
{{--                                    <td>Mark</td>--}}
{{--                                    <td>Otto</td>--}}
{{--                                    <td>@mdo</td>--}}
{{--                                    <td>Otto</td>--}}
{{--                                    <td>@mdo</td>--}}
{{--                                    <td class="text-end"> <i class="fa-regular fa-trash-can fs-20 text-danger"></i></td>--}}
{{--                                </tr>--}}

{{--                                </tbody>--}}
{{--                            </table>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--        --}}{{--        sync product end--}}
{{--    </div>--}}
{{--    --}}{{--    /************************ new code end ***************************/--}}
{{--@endsection--}}
{{--@push('footer_scripts')--}}
{{--    <script>--}}
{{--        function copyText() {--}}
{{--            var copyText = document.getElementById("myInput");--}}

{{--            /* Select the text field */--}}
{{--            copyText.select();--}}
{{--            copyText.setSelectionRange(0, 99999); /* For mobile devices */--}}

{{--            /* Copy the text inside the text field */--}}
{{--            navigator.clipboard.writeText(copyText.value);--}}
{{--            var tooltip = document.getElementById("myTooltip");--}}
{{--            tooltip.innerHTML = "Copied Link ";--}}

{{--        }--}}

{{--        function outFunc() {--}}
{{--            var tooltip = document.getElementById("myTooltip");--}}
{{--            tooltip.innerHTML = "Copy to clipboard";--}}
{{--        }--}}
{{--    </script>--}}
{{--@endpush--}}
