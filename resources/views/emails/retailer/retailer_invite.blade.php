<!-- Do not edit the below line -->
@extends('emails.layouts.app')
@section('content')

    <!--  Only edit text lines do not edit html if you do not have knowledge it will break functionality  -->
    <tr>
        <td style="padding: 0 30px; box-sizing: border-box; text-align: left;">

            <p style=" text-align: center; font-weight: 1000; font-family: Poppins; font-size: 24px;">You're
                Invited! 🎉</p>
            <p class="heading1">Hello
                @if(isset($retailer->fname))
                    {{ ucfirst($retailer->fname) }}
                @else
                    User
                @endif
            </p>
            <p class="text">
                @if(isset($user->fname))
                    {{ ucfirst($user->fname) }}
                @else
                    {{\Illuminate\Support\Facades\Auth::user()->fname}}
                @endif
                from
                @if(isset($user->fname))
                    {{ $user->fname }}
                @else
                    {{\Illuminate\Support\Facades\Auth::user()->fname}}
                @endif
                has invited you to join on
                Apimio.</p>
            <p class="text">Apimio is a centralized Product Information Management platform for
                manufacturers to collect product information and share it with their retailers.</p>

            <p class="heading1" style="padding-left: 0; margin-bottom: 0;">As a retailer on Apimio, you
                can:</p>

            <ul class="text" style="margin-top: 8px;">

                <li>View and organize product stores that you receive from your manufacturer
                </li>
                <li>Export product stores as CSV files
                </li>
                <li>Directly upload your product feeds to e-commerce platforms such as Shopify
                </li>
            </ul>
            <p class="text">To find out more about what you can do with
                Apimio, visit our
                <a href="https://apimio.com/" rel="noopener noreferrer"
                   data-auth="NotApplicable" target="_blank" style="color: #2c4bff;"> website</a>
            </p>

            <p class="text" style="text-align: center">Click the link below to start using Apimio.</p>
        </td>
    </tr>
    <tr>
        <td style="text-align: center; padding: 0 30px; box-sizing: border-box;">
            <!--<a style="color: #ffffff;" href="https://apimio.com/" class="button1">Join Team</a>-->
            <a href="{{ route('register.invite.retailer', [base64_encode($workspace_link??'0')]) }}"
               target="_blank"
               rel="noopener noreferrer"
               data-auth="NotApplicable" class="x_button"
               style="font-family:Poppins,Roboto,sans-serif; background-color:#2c4bff; text-decoration:none; border-radius:4px; color:white; display:inline-block; font-size:16px; text-align:center; margin:0 8px; padding:0 5px; border:10px solid #2c4bff;">Join
                Team</a>

            {{--            <a href="{{ route('retailer.register', [isset($user->domain) ? $user->domain : '#', isset($retailer->token) ? $retailer->token : '123']) }}"--}}
            {{--               rel="noopener noreferrer"--}}
            {{--               data-auth="NotApplicable"--}}
            {{--               class="link">--}}
            {{--            </a>--}}
{{--            <pre style="word-break: break-all">--}}
{{--                {{ route('retailer.register', [isset($user->domain) ? $user->domain : '#', isset($retailer->id) ? \Illuminate\Support\Facades\Crypt::encrypt($retailer->id) : '123']) }}--}}
{{--            </pre>--}}
            <p class="text" style="text-align: center;">If you weren't expecting an invite, please disregard
                this email. If you have any queries, contact us at
                <a href="https://support.apimio.com/"
                   rel="noopener noreferrer"
                   data-auth="NotApplicable"
                   target="_blank" style="color: #2c4bff;"> Support!</a>
            </p>
        </td>
    </tr>
@endsection
