<?php ?>
@extends('layouts.app_new')
@section('titles', 'Settings')
@section('content')
    @push('header_scripts')
        <style>
            .custom-control-input:checked~.custom-control-label::before {
                color: #fff;
                border-color: #5bc262;
                background-color: #5bc262;
            }
        </style>
    @endpush
    <div>



        <x-channel.channel-settings-page-title catalogname="{{ $channel->name }}"
            description="{{ trans('channel_edit.page_description') }}" />

        <nav>
            <div class="nav nav-tabs mb-3" id="nav-tab" role="tablist">
                <button class="nav-link active" id="product-setting-tab" data-bs-toggle="tab" data-bs-target="#product-setting"
                    type="button" role="tab" aria-controls="product-setting" aria-selected="true">Product
                    Settings</button>
                <button class="nav-link" id="location-mapping-tab" data-bs-toggle="tab" data-bs-target="#location-mapping"
                    type="button" role="tab" aria-controls="location-mapping" aria-selected="false">Location
                    Mapping</button>
                @if(isset($channel->shopify_channel->access_token) )
                <button class="nav-link" id="webhook-settings-tab" data-bs-toggle="tab" data-bs-target="#webhooks-settings"
                    type="button" role="tab" aria-controls="webhooks-settings" aria-selected="false">
                    Webhooks Settings</button>
                @else
                <button class="nav-link"
                    type="button" disabled>
                    Webhooks Settings</button>
                @endif
            </div>
        </nav>
        <form method="post" class="formStyle" action="{{ route('channel.update', $channel->id) }}">
            @csrf
            @method('PUT')
            <div class="tab-content p-3 border bg-light" id="nav-tabContent">
                <div class="row tab-pane fade active show" id="product-setting" role="tabpanel"
                    aria-labelledby="nav-home-tab">
                    <div class="col-12 col-md-6 ">
                        <div class="form-group mt-4">
                            <label for="name">{{ trans('channel_edit.channel_name') }} </label>&nbsp;
                            <input name="name" class="form-control @error('name') is-invalid @enderror"
                                value="{{ $channel->name }}" />

                        </div>
                        @error('name')
                            <span class="text-danger">
                                <small>{{ $message }}</small>
                            </span>
                        @enderror
                        <div class="form-group mt-4">
                            <label for="type">{{ trans('channel_edit.channel_type') }} </label>&nbsp;
                            <input name="type" type="text" class="form-control @error('type') is-invalid @enderror"
                                value="{{ $channel->type }}" readonly />

                        </div>
                        @error('type')
                            <span class="text-danger">
                                <small>{{ $message }}</small>
                            </span>
                        @enderror

                        <div class="form-group mt-4">
                            <label for="product_update">{{ trans('channel_edit.product_update') }} </label>&nbsp;
                            <select name="product_update"
                                class="form-control bg-white-smoke @error('product_update') is-invalid @enderror">
                                <option value="manual" {{ $channel->product_update == 'manual' ? 'selected' : '' }}
                                    class="Roboto regular text-color">{{ __('Manual') }}</option>
                            </select>
                        </div>
                        @error('product_update')
                            <span class="text-danger">
                                <small>{{ $message }}</small>
                            </span>
                        @enderror

                        <div class="form-group mt-4">
                            <label for="versions">Create Product Shopify Webhook</label>&nbsp;
                            <select placeholder="Create Product Shopify Webhook" name="is_create_product_webhook_enabled"
                                class="form-control bg-white-smoke @error('is_create_product_webhook_enabled') is-invalid @enderror">
                                <option value="1"
                                    {{ $channel->is_create_product_webhook_enabled == '1' ? 'selected' : '' }}>Enable</option>
                                <option value="0"
                                    {{ $channel->is_create_product_webhook_enabled == '0' ? 'selected' : '' }}>Disable</option>
                            </select>
                        </div>
                        @error('is_create_product_webhook_enabled')
                            <span class="text-danger">
                                <small>{{ $message }}</small>
                            </span>
                        @enderror

                        <div class="form-group mt-4">
                            <label for="versions">Update Product Shopify Webhook</label>&nbsp;
                            <select placeholder="Language" name="is_product_update_webhook_enabled"
                                class="form-control bg-white-smoke @error('is_product_update_webhook_enabled') is-invalid @enderror">
                                <option value="1"
                                    {{ $channel->is_product_update_webhook_enabled == '1' ? 'selected' : '' }}>Enable</option>
                                <option value="0"
                                    {{ $channel->is_product_update_webhook_enabled == '0' ? 'selected' : '' }}>Disable</option>
                            </select>
                        </div>
                        @error('is_product_update_webhook_enabled')
                            <span class="text-danger">
                                <small>{{ $message }}</small>
                            </span>
                        @enderror
                        <div class="form-group mt-4">
                            <label for="versions">Delete Product Shopify Webhook</label>&nbsp;
                            <select placeholder="Create Product Shopify Webhook" name="is_product_delete"
                                class="form-control bg-white-smoke @error('is_product_delete') is-invalid @enderror">
                                <option value="1" {{ $channel->is_product_delete == '1' ? 'selected' : '' }}>Enable
                                </option>
                                <option value="0" {{ $channel->is_product_delete == '0' ? 'selected' : '' }}>Disable
                                </option>
                            </select>
                        </div>
                        @error('is_product_delete')
                            <span class="text-danger">
                                <small>{{ $message }}</small>
                            </span>
                        @enderror
                        <div class="form-group mt-4">
                            <label for="versions">{{ trans('channel_edit.language') }}</label>&nbsp;
                            <select placeholder="Language" name="versions[]"
                                class="form-control bg-white-smoke @error('versions') is-invalid @enderror">
                                <option value="">Select Language</option>
                                @foreach ($versions as $version)
                                    <option value="{{ $version->id }}"
                                        @foreach ($channel->channel_versions as $ver)
                                    {{ $ver->version_id == $version->id ? 'selected' : '' }} @endforeach>
                                        {{ $version->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        @error('versions')
                            <span class="text-danger">
                                <small>{{ $message }}</small>
                            </span>
                        @enderror
                        {{--                    TODO location edit for channel --}}
                        {{--                    <div class="form-group mt-4"> --}}
                        {{--                        <label for="locations">{{ trans('channel_edit.location') }}</label>&nbsp; --}}
                        {{--                        <select name="locations[]" id="locations" multiple="multiple" --}}
                        {{--                                class="form-control sumoselect bg-white-smoke @error('locations') is-invalid @enderror"> --}}
                        {{--                            @foreach ($unassigned_locations as $location) --}}
                        {{--                                <option value="{{ $location->id }}" {{ in_array($location->id, old('locations', $channel->locations->pluck('id')->toArray())) ? 'selected' : '' }}> --}}
                        {{--                                    {{ $location->name }} --}}
                        {{--                                </option> --}}
                        {{--                            @endforeach --}}

                        {{--                            @foreach ($current_locations as $location) --}}
                        {{--                                <option value="{{ $location->id }}" {{ in_array($location->id, old('locations', $channel->locations->pluck('id')->toArray())) ? 'selected' : '' }}> --}}
                        {{--                                    {{ $location->name }} --}}
                        {{--                                </option> --}}
                        {{--                            @endforeach --}}

                        {{--                            @foreach ($assigned_locations as $assignedLocation) --}}
                        {{--                                <option value="{{ $assignedLocation->id }}" disabled> --}}
                        {{--                                    {{ $assignedLocation->name }} (Assigned) --}}
                        {{--                                </option> --}}
                        {{--                            @endforeach --}}
                        {{--                        </select> --}}
                        {{--                    </div> --}}
                        {{--                    <div class="form-group mt-4"> --}}
                        {{--                        <label for="locations">{{trans('channel_edit.location')}} </label>&nbsp; --}}
                        {{--                        <input name="locations" type="text" class="form-control @error('locations') is-invalid @enderror" value="{{$current_locations}}" readonly/> --}}
                        {{--                    </div> --}}
                        <div class="form-group mt-4">
                            <label for="locations">{{ trans('channel_edit.location') }}</label>&nbsp;
                            @if ($current_locations->isEmpty())
                                <input type="text" class="form-control " value="No location found" readonly />
                            @else
                                <input name="locations" type="text"
                                    class="form-control @error('locations') is-invalid @enderror"
                                    value="{{ $current_locations->pluck('name')->implode(', ') }}" readonly />
                            @endif
                        </div>
                        @error('locations')
                            <span class="text-danger">
                                <small>{{ $message }}</small>
                            </span>
                        @enderror

                        <div class="form-group mt-4">
                            <label for="type">{{ trans('channel_edit.channel_type') }} </label>&nbsp;
                            <input name="type" type="text" class="form-control @error('type') is-invalid @enderror"
                                value="{{ $channel->type }}" readonly />

                        </div>

                        <div class="form-group mt-4">
                            <label for="versions">{{ trans('channel_edit.syncing_method') }}</label>&nbsp;
                            <select placeholder="Syncing Method" name="syncing_method"
                                class="form-control bg-white-smoke @error('syncing_method') is-invalid @enderror">
                                <option value="update" {{ $channel->syncing_method == 'update' ? 'selected' : '' }}>Update
                                    Product</option>
                                <option value="skip" {{ $channel->syncing_method == 'skip' ? 'selected' : '' }}>Skip
                                    Product</option>
                                <option value="new" {{ $channel->syncing_method == 'new' ? 'selected' : '' }}>Create New
                                    Product</option>
                            </select>
                        </div>
                        @error('syncing_method')
                            <span class="text-danger">
                                <small>{{ $message }}</small>
                            </span>
                        @enderror

                    </div>
                </div>
                <div class="tab-pane fade" id="location-mapping" role="tabpanel" aria-labelledby="location-mapping-tab" >
                    <div>
                    @foreach($store_locations as $key => $store_location)
                          <div class="row align-items-center mb-2">
                              <div class="d-flex justify-content-between align-items-center">
                                  <div class="col-5">
                                      <h4 class="text-primary align-items-center">{{$store_location->location->name ?? ''}}</h4>
                                  </div>
                                  <div class="col-5">
                                          <select name="apimio[{{$store_location->id}}]" class="form-control sumoselect SumoUnder" id="shopify-locations">
                                              <option value="">Select Location</option>
                                              @foreach($apimio_locations as $key => $apimio_location)
                                              <option value="{{$apimio_location->id}}" {{--$store_location->location_id === $apimio_location->id ? 'selected="true"' : ''--}} >{{$apimio_location->name ?? ''}}</option>
                                              @endforeach
                                          </select>
                                  </div>
                              </div>
                              </div>
                      @endforeach
                      </div>
                </div>
                @if(isset($channel->shopify_channel->access_token) )
                <div class="tab-pane fade" id="webhooks-settings" role="tabpanel" aria-labelledby="webhooks-settings-tab" >
                    <div class="row">
                        <div class="col-12">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead class="thead-light">
                                        <tr>
                                            <th class="text-start">Name</th>
                                            <th class="text-center">Status</th>
                                            <th class="text-end">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($webhooks as $webhook)
                                        <tr>
                                            <td>
                                                {{$webhook['topic']}}
                                            </td>
                                            <td class="text-center">
                                                @if($webhook['status'] == 1)
                                                <span class="badge bg-success">Active</span>
                                                @else
                                                <span class="badge bg-danger">Inactive</span>
                                                @endif
                                            </td>
                                            <td class="text-end">
                                                @if($webhook['disable'] == false)
                                                @if($webhook['status'] == 0)
                                                <a href="javascript:void(0)" data-channel-id='{{$channel->id}}' data-topic-id="{{$webhook['topic'] }}" data-address-id="{{$webhook['address'] }}" class="btn btn-sm btn-outline-primary register-shopify-webhook">
                                                    Connect Webhook
                                                </a>
                                                @else
                                                <a href="{{ route('channel.disconnect.webhook', ['webhook_id' => $webhook['id'], 'channel_id' => $channel->id]) }}" class="btn btn-sm btn-outline-danger">
                                                    Unlink Webhook
                                                </a>
                                                @endif
                                                @else
                                                <span class="badge bg-secondary">Disable</span>
                                                @endif
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex flex-row-reverse mt-40">
                            <button type="submit" id="publish-btn" class="btn btn-primary ms-2">
                                {{ trans('channel_edit.save_btn') }}

                            </button>

                            <a href="{{ route('shopify.index', ['channels[]' => $channel->id]) }}" id="cancel-btn"
                                class="btn btn-outline-danger">
                                {{ trans('channel_edit.cancel_btn') }}

                            </a>

                        </div>
                    </div>
                </div>
            </div>

        </form>



    </div>

@endsection
@push('footer_scripts')
    <script>
        // $('.popover-product').popover({
        //     trigger: 'hover'
        // });
        // $('.popover-inventory').popover({
        //     trigger: 'hover'
        // });
        // $('.popover-status').popover({
        //     trigger: 'hover'
        // });
        // $('.popover-category').popover({
        //     trigger: 'hover'
        // });
        // $('.popover-price').popover({
        //     trigger: 'hover'
        // });
        // $('.popover-syncing_method').popover({
        //     trigger: 'hover'
        // });

   $(document).on('click', '.register-shopify-webhook', function() {

            var data = {
                channel_id: this.getAttribute('data-channel-id'),
                topic: this.getAttribute('data-topic-id'),
                address: this.getAttribute('data-address-id'),
            };

            var url = "{{ route('channel.connect.webhook') }}";

            fetch(url, {
                    method: "POST",
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data) // Convert data to JSON
                })
                .then(response => {
                    if (response.ok) {
                        window.location.reload();
                    }
                    throw new Error('Network response was not ok.');
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        });


    </script>
@endpush
