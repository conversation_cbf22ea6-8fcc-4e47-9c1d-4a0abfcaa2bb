@extends('layouts.app_new')
@section('titles','Catalog')
@section('content')
    @push('header_scripts')
    @endpush
    <div class="row">
        <div class="col-6">
            <h2 class="mb-0 clr-grey">
                <a href="{{route('channel.index')}}" class="mb-0 clr-grey text-decoration-none">
                    {{trans('channel.page_title')}}
                </a>
                {{__(" /")}}
                <sapn class="text-dark">{{ $channel->name }}</sapn>
            </h2>
            @if($channel->shopify_channels?(sizeof($channel->shopify_channels)>0)??false:false)
                <p class="label m-0"> Shop Name : <b>{{$channel->shopify_channels[0]->shop}}</b></p>
            @endif
            <p class="mb-0">{{trans('channel_shopify_channels.page_description')}}</p>
        </div>
        <div class="col-12 col-lg-6">
            <div class="d-flex justify-content-end">
                <a href="{{route('channel.edit', $channel->id)}}" id="view-product"
                   class="btn btn-outline-dark mx-4 mx-md-0">
                    Settings
                </a>
                @if($channel->shopify_channels?(sizeof($channel->shopify_channels)>0)??false:false)
                <a href="javascript:void(0)"  id="connect-to-shopify"
                   class="btn btn-outline-primary ms-3" data-bs-toggle="modal" data-bs-target="#delete-modal-{{$channel->id}}">
                    Fetch Products
                </a>
                @endif
                @if($channel->shopify_channels?(sizeof($channel->shopify_channels)>0)??false:false)
                    <a href="{{ route("channel.shopify.disconnect",$channel->id) }}" class="btn btn-danger ms-3">
                        {{trans('channel_shopify_channels.disconnect_shopify_btn')}}
                    </a>
                @else
                <a href="{{ route("shopify.show",$channel->id) }}" class="btn btn-outline-primary ms-3" id="connect-to-shopify">
                    &nbsp;
                    {{trans('channel.connect_to_shopify_btn')}}
                </a>
                @endif
            </div>
        </div>
    </div>


    {{--    *********************new code start *******************--}}
        <div class="row">
            <div class="col-12 col-xxl-6">
                <x-guide.view-product-status :channel="[$channel->id]"
                                             btnname="{{trans('channel_shopify_channels.settings_btn')}}"
                                             :hrefroutes="route('channel.edit', $channel->id)"/>
            </div>

                <x-gallery-dashboard-gallery-image-scoring></x-gallery-dashboard-gallery-image-scoring>

        </div>
            {{-- Images Quality Score end--}}

    {{--    *********************new code end *******************--}}
    <br>
    @if($channel->shopify_channel?$channel->shopify_channel->access_token != null:false)
        <x-products.product-listing-table :sync="true" :filter="true" :multiselect="true"/>
    @else
        <x-products.product-listing-table :sync="false" :filter="true"/>
        @endif
        </div>
@endsection
<x-assets.delete-modal text="Confirm syncing with Shopify? This will update all products based on SKUs and may take time based on your store's inventory."
                                                   button="Fetch Product" title="Fetch Product"
                                                   id="{{$channel->id}}"
                                                   url="{{route('channel.shopify.fetch_all_products',$channel->id)}}" type="fetch_shopify_products"/>
{{--@push('footer_scripts')--}}
{{--    <script>--}}
{{--        $('.popover-hover-2').popover({--}}
{{--            trigger: 'hover'--}}
{{--        });--}}
{{--    </script>--}}
{{--    <script>--}}
{{--        const productStatus = document.getElementById('productStatus');--}}
{{--        new Chart(ImageQuality2, {--}}
{{--            type: 'doughnut',--}}
{{--            data: {--}}
{{--                labels: [--}}
{{--                    'blue',--}}
{{--                    'dark',--}}

{{--                ],--}}
{{--                datasets: [{--}}
{{--                    data: [{{$data['published']}}, {{$data['draft']}}],--}}
{{--                    backgroundColor: [--}}
{{--                        '#2C4BFF',--}}
{{--                        '#6C757D',--}}
{{--                    ],--}}
{{--                }]--}}
{{--            },--}}
{{--            options: {--}}
{{--                cutout: 35,--}}
{{--                responsive: true,--}}
{{--                maintainAspectRatio: false,--}}
{{--                plugins: {--}}
{{--                    legend: {--}}
{{--                        display: false--}}
{{--                    }--}}
{{--                }--}}
{{--            },--}}
{{--        });--}}
{{--    </script>--}}
{{--@endpush--}}
