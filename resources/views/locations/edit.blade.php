
@extends('layouts.app_new')
@if(Request::segment(4) == 'edit')
    @section('titles','Edit Add Locations')
@else
    @section('titles','Add Locations')
@endif
@section('content')
    <div>

    <x-products.page-title name="{{trans('locations.edit_location')}}"
                               description="{{trans('locations.page_description')}}"
                               links="true"
                               button="true">
            <x-slot name="addbutton">

            </x-slot>
        </x-products.page-title>
        <form action="{{ route('locations.update', $location->id) }}" method="post">
            @csrf
            @method('PUT')


            <div class="form-group pt-0 px-3 mb-3 col-8">
                                    <label for="name">{{trans('locations.add_name')}}&nbsp;
                                        <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control  @error('name') is-invalid @enderror"
                                           id="name" name="name" minlength="3"
                                           value="{{ $location->name }}"

                                           autofocus>
                                    @error('name')
                                    <span class="text-danger error-message-text">
                                        <small class="error-message-text">{{$message}}</small>
                                    </span>
                                    @enderror
                                </div>
                                <div class="form-group pt-0 px-3 mb-3 col-8">
                                    <label for="address">{{trans('locations.add_address')}}&nbsp;
                                      </label>
                                    <textarea class="form-control  @error('address') is-invalid @enderror"
                                           id="address" name="address" minlength="3"
                                           value=""

                                           autofocus>{{ $location->address }}</textarea>
{{--                                    @error('address')--}}
{{--                                    <span class="text-danger error-message-text">--}}
{{--                                        <small class="error-message-text">{{$message}}</small>--}}
{{--                                    </span>--}}
{{--                                    @enderror--}}
                                </div>
                                <div class="form-group pt-0 px-3 mb-3 col-8">
                                    <label for="apartment">{{trans('locations.add_apartment')}}&nbsp;
                                        </label>
                                    <input type="text" class="form-control "
                                           id="apartment" name="apartment"
                                           value="{{ $location->apartment }}"

                                           autofocus>
{{--                                    <!-- @error('apartment')--}}
{{--                                    <span class="text-danger error-message-text">--}}
{{--                                        <small class="error-message-text">{{$message}}</small>--}}
{{--                                    </span>--}}
{{--                                    @enderror -->--}}
                                </div>
                                <div class="form-group pt-0 px-3 mb-3 col-8">
                                    <label for="postal-code">{{trans('locations.add_postal_code')}}&nbsp;
                                       </label>
                                    <input type="text" class="form-control  @error('postal_code') is-invalid @enderror"
                                           id="postal_code" name="postal_code" minlength="3"
                                           value="{{ $location->postal_code }}"

                                           autofocus>
{{--                                    @error('postal_code')--}}
{{--                                    <span class="text-danger error-message-text">--}}
{{--                                        <small class="error-message-text">{{$message}}</small>--}}
{{--                                    </span>--}}
{{--                                    @enderror--}}
                                </div>
                                <div class="form-group pt-0 px-3 mb-3 col-8">
                                    <label for="city">{{trans('locations.add_city')}}&nbsp;
                                      </label>
                                    <input type="text" class="form-control  @error('city') is-invalid @enderror"
                                           id="city" name="city" minlength="3"
                                           value="{{ $location->city }}"

                                           autofocus>
{{--                                    @error('city')--}}
{{--                                    <span class="text-danger error-message-text">--}}
{{--                                        <small class="error-message-text">{{$message}}</small>--}}
{{--                                    </span>--}}
{{--                                    @enderror--}}
                                </div>
                                <div class="form-group pt-0 px-3 mb-3 col-8">
                                    <label for="phone_number">{{trans('locations.add_phone_number')}}&nbsp;
                                       </label>

                                    <input name="phone_number" value="{{ $location->phone_number }}"
                                       id="phone_number" type="tel"
                                       oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
                                       class="form-control @error('phone_number') is-invalid @enderror"
                                       placeholder="0301 2345678"
                                       onkeypress="return searchKeyPress(event);"/>
{{--                                    @error('phone_number')--}}
{{--                                    <span class="text-danger error-message-text">--}}
{{--                                        <small class="error-message-text">{{$message}}</small>--}}
{{--                                    </span>--}}
{{--                                    @enderror--}}
                                </div>
                                <div class="form-group pt-0 px-3 mb-3 d-flex align-items-center clr-grey">
                                <input type="checkbox" class="me-2" name="fulfill_online_orders"  value="1" {{ $location->fulfill_online_orders ? 'checked' : '' }}>
                                    <label for="fulfill_online_orders">{{trans('locations.add_fullfill_online_orders')}}</label>
{{--                                    <!-- @error('name')--}}
{{--                                    <span class="text-danger error-message-text">--}}
{{--                                        <small class="error-message-text">{{$message}}</small>--}}
{{--                                    </span>--}}
{{--                                    @enderror -->--}}
                                </div>


            @if (!$location->default_location)
                <div class="form-group pt-0 px-3 mb-3 d-flex align-items-center clr-grey">
                    <input type="checkbox" class="me-2" name="default_location" value="1" {{ $location->default_location ? 'checked' : '' }}>
                    <label for="default_location">{{trans('locations.add_default_locations')}}</label>
                </div>
        @endif
            <!-- <label>Name: <input type="text" name="name" value="{{ $location->name }}"></label><br>
            <label>Address: <textarea name="address">{{ $location->address }}</textarea></label><br>
            <label>Apartment: <input type="text" name="apartment" value="{{ $location->apartment }}"></label><br>
            <label>Postal Code: <input type="text" name="postal_code" value="{{ $location->postal_code }}"></label><br>
            <label>City: <input type="text" name="city" value="{{ $location->city }}"></label><br>
            <label>Phone Number: <input type="text" name="phone_number" value="{{ $location->phone_number }}"></label><br>
            <label>Fulfill Online Orders:
                <input type="checkbox" name="fulfill_online_orders" value="1" {{ $location->fulfill_online_orders ? 'checked' : '' }}>
            </label><br>
            <label>Default Location:
                <input type="checkbox" name="default_location" value="1" {{ $location->default_location ? 'checked' : '' }}>
            </label><br> -->
            <!-- <input type="submit" value="Update Location"> -->
            <div class="d-flex">
                            <a href="{{ route('locations.index') }}" class="btn btn-outline-danger" id="cancel-btn">
                                Cancel
                            </a>
                            <button type="submit" id="update_location" class="btn btn-primary ms-2">
                                Update
                            </button>
                        </div>
        </form>


    </div>
@endsection
@push('footer_scripts')
<script>
    let input = document.querySelector("#phone_number");
        ipLookUp();

        // get current user country
        function ipLookUp() {
            $.ajax({
                dataType: 'json',
                url: 'https://api.hostip.info/get_json.php',
                success: function (data) {
                    let $ip = data['ip'],
                        $city = data['city'],
                        $countryCode = data['country_code'],
                        $countryName = data['country_name'];
                    window.intlTelInput(input, ({
                        separateDialCode: false,
                        // preferredCountries: ["us", "ca"],
                        utilsScript: "{{asset('public/js/utils.js')}}",
                        nationalMode: false,
                        initialCountry: $countryCode
                    }));
                },
                error: function (jqXHR, textStatus) {
                    window.intlTelInput(input, ({
                        separateDialCode: false,
                        // preferredCountries: [ "ca"],
                        utilsScript: "{{asset('public/js/utils.js')}}",
                        nationalMode: false,
                    }));
                }
            });
            return 0;
        }
     function searchKeyPress(e) {
            e = e || window.event;
            if (e.keyCode === 13) {
                document.getElementById('registeration_next_btn_id').click();
                return false;
            }
            return true;
        }

//         document.addEventListener("DOMContentLoaded", function() {
//     var inputElement = $("#phone_number");
//     // Get the text content from the element
//     var inputVal = inputElement.val();

//     // Use JavaScript to extract the last two characters (letters)
//     var selectedCountry = inputVal.slice(-2);

//     $(".iti__country").attr("aria-selected", "false");
//     $(".iti__country").each(function() {
//         // Check if the value of the "data-other-attr" attribute is "abc"
//         if ($(this).attr("data-country-code") === selectedCountry) {
//             // If it matches, set the "data-custom-attr" attribute to "true"
//             $(this).attr("aria-selected", "true");
//             var myDiv = $(".iti__selected-flag .iti__flag");

//             // Get the class that you want to keep
//             var classToKeep = "iti__flag"; // Change this to the class you want to keep

//             // Remove all other classes from the div
//             myDiv.attr("class", classToKeep);
//             selectedFlagClass= 'iti__'+ selectedCountry;
//             myDiv.addClass(selectedFlagClass)
//         }

//     });

//     $(document).on("click", ".iti__country", function() {
//         if ($(this).attr("aria-selected") === "true") {
//             // Get the data-country-code attribute value of the clicked <li>
//             var selectedLicode = $(this).attr("data-country-code");
//             alert(selectedLicode);
//             var inputElement = $("#phone_number");
//             var currentValue = inputElement.val();
//             // Append text to the current value
//             var appendedValue = currentValue + selectedLicode;

//             // Set the updated value back to the input
//             inputElement.val(appendedValue);
//         }
//     });
// });
</script>
@endpush
