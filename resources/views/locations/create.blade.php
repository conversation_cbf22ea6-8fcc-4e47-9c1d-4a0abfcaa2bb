
@extends('layouts.app_new')
@if(Request::segment(4) == 'edit')
    @section('titles','Edit Add Locations')
@else
    @section('titles','Add Locations')
@endif
@section('content')

    <div>
        <x-products.page-title name="{{trans('locations.create_new_location')}}"
                               description="{{trans('locations.page_description')}}"
                               links="true"
                               button="true">
            <x-slot name="addbutton">

            </x-slot>
        </x-products.page-title>

        <form action="{{ route('locations.store') }}" class="formStyle" method="post">
            @csrf

                                <div class="form-group pt-0 px-3 mb-3 col-8">
                                    <label for="name">{{trans('locations.add_name')}}&nbsp;
                                        <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control  @error('name') is-invalid @enderror"
                                           id="name" name="name" minlength="3"
                                           value="{{ old('name') }}"

                                           autofocus>
                                    @error('name')
                                    <span class="text-danger error-message-text">
                                        <small class="error-message-text">{{$message}}</small>
                                    </span>
                                    @enderror
                                </div>
                                <div class="form-group pt-0 px-3 mb-3 col-8">
                                    <label for="address">{{trans('locations.add_address')}}&nbsp;
                                        </label>
                                        <textarea class="form-control @error('address') is-invalid @enderror" id="address" name="address" minlength="3"  autofocus>{{ old('address') }}</textarea>
{{--                                    @error('address')--}}
{{--                                    <span class="text-danger error-message-text">--}}
{{--                                        <small class="error-message-text">{{$message}}</small>--}}
{{--                                    </span>--}}
{{--                                    @enderror--}}
                                </div>
                                <div class="form-group pt-0 px-3 mb-3 col-8">
                                    <label for="apartment">{{trans('locations.add_apartment')}}&nbsp;
                                        </label>
                                    <input type="text" class="form-control"
                                           id="apartment" name="apartment"
                                            value="{{ old('apartment') }}"

                                           autofocus>
{{--                                    <!-- @error('apartment')--}}
{{--                                    <span class="text-danger error-message-text">--}}
{{--                                        <small class="error-message-text">{{$message}}</small>--}}
{{--                                    </span>--}}
{{--                                    @enderror -->--}}
                                </div>
                                <div class="form-group pt-0 px-3 mb-3 col-8">
                                    <label for="postal-code">{{trans('locations.add_postal_code')}}&nbsp;
                                       </label>
                                    <input type="text" class="form-control  @error('postal_code') is-invalid @enderror"
                                           id="postal_code" name="postal_code" minlength="3"
                                           value="{{ old('postal_code') }}"

                                           autofocus>
{{--                                    @error('postal_code')--}}
{{--                                    <span class="text-danger error-message-text">--}}
{{--                                        <small class="error-message-text">{{$message}}</small>--}}
{{--                                    </span>--}}
{{--                                    @enderror--}}
                                </div>
                                <div class="form-group pt-0 px-3 mb-3 col-8">
                                    <label for="name">{{trans('locations.add_city')}}&nbsp;
                                      </label>
                                    <input type="text" class="form-control  @error('city') is-invalid @enderror"
                                           id="city" name="city" minlength="3"
                                           value="{{ old('city') }}"

                                           autofocus>
{{--                                    @error('city')--}}
{{--                                    <span class="text-danger error-message-text">--}}
{{--                                        <small class="error-message-text">{{$message}}</small>--}}
{{--                                    </span>--}}
{{--                                    @enderror--}}
                                </div>
                                <div class="form-group pt-0 px-3 mb-3 col-8">
                                    <label for="name">{{trans('locations.add_phone_number')}}&nbsp;
                                        </label>
                                        <input name="phone_number" value="{{ old('phone_number') }}"
                                       id="phone_number" type="tel"
                                       oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
                                       class="form-control @error('phone_number') is-invalid @enderror"
                                       placeholder="+92 301 2345678"
                                       onkeypress="return searchKeyPress(event);"/>
{{--                                    @error('phone_number')--}}
{{--                                    <span class="text-danger error-message-text">--}}
{{--                                        <small class="error-message-text">{{$message}}</small>--}}
{{--                                    </span>--}}
{{--                                    @enderror--}}
                                </div>
                                <div class="form-group pt-0 px-3 mb-3 d-flex align-items-center clr-grey">
                                <input type="checkbox" class="me-2" value="1" name="fulfill_online_orders" id="fulfill_online_orders">
                                    <label for="fulfill_online_orders">{{trans('locations.add_fullfill_online_orders')}}

                                </div>

                                <div class="form-group pt-0 px-3 mb-3 d-flex align-items-center clr-grey">
                                <input type="checkbox" class="me-2" value="1" name="default_location" id="default_location">
                                    <label for="default_location">{{trans('locations.add_default_locations')}}

                                </div>



            <!-- <label>Name: <input type="text" name="name"></label><br>
            <label>Address: <textarea name="address"></textarea></label><br>
            <label>Apartment: <input type="text" name="apartment"></label><br>
            <label>Postal Code: <input type="text" name="postal_code"></label><br>
            <label>City: <input type="text" name="city"></label><br>
            <label>Phone Number: <input type="text" name="phone_number"></label><br>
            <label>Fulfill Online Orders: <input type="checkbox" name="fulfill_online_orders" value="1"></label><br>
            <label>Default Location: <input type="checkbox" name="default_location" value="1"></label><br> -->
            <!-- <input type="submit" value="Add Location"> -->
            <div class="d-flex">
                            <a href="{{ route('locations.index') }}" class="btn btn-outline-danger" id="cancel-btn">
                                Cancel
                            </a>
                            <button type="submit" id="create_location" class="btn btn-primary ms-2">
                                Create

                            </button>
                        </div>
        </form>

    </div>
@endsection
@push('footer_scripts')
<script>
    let input = document.querySelector("#phone_number");
        ipLookUp();

        // get current user country
        function ipLookUp() {
            $.ajax({
                dataType: 'json',
                url: 'https://api.hostip.info/get_json.php',
                success: function (data) {
                    let $ip = data['ip'],
                        $city = data['city'],
                        $countryCode = data['country_code'],
                        $countryName = data['country_name'];
                    window.intlTelInput(input, ({
                        separateDialCode: false,
                        preferredCountries: ["us", "ca"],
                        utilsScript: "{{asset('public/js/utils.js')}}",
                        nationalMode: false,
                        initialCountry: $countryCode
                    }));
                },
                error: function (jqXHR, textStatus) {
                    window.intlTelInput(input, ({
                        separateDialCode: false,
                        preferredCountries: ["us", "ca"],
                        utilsScript: "{{asset('public/js/utils.js')}}",
                        nationalMode: false,
                    }));
                }
            });
            return 0;
        }
     function searchKeyPress(e) {
            e = e || window.event;
            if (e.keyCode === 13) {
                document.getElementById('registeration_next_btn_id').click();
                return false;
            }
            return true;
        }
</script>
@endpush
