
@extends('layouts.app_new')
@if(Request::segment(4) == 'edit')
    @section('titles',' Locations')
@else
    @section('titles','Locations')
@endif
@section('content')
    <div>
        <!-- <h1>Locations</h1>
        <p>Efficiently manage and organize your inventory across multiple locations</p>  -->
          <x-products.page-title name="{{trans('locations.page_title')}}"
                               description="{{trans('locations.page_description')}}"
                               links="true"
                               button="false">
{{--            <x-slot name="addbutton">--}}
{{--                <a href="{{route('locations.create')}}"--}}
{{--                   id="add-locations"--}}
{{--                   class="btn btn-primary float-lg-right float-md-right only-disabled">--}}
{{--                Add Location--}}
{{--                </a>--}}
{{--            </x-slot>--}}
        </x-products.page-title>
        @if(count($locations) > 0)
        <table class="table" >
            <thead >
            <tr style="width:100%">
                <th >{{trans('locations.index-location')}}</th>
                <th >Address</th>
                <!-- <th>Apartment</th> -->
                <!-- <th>Postal Code</th> -->
                <th>City</th>
                <!-- <th>Phone Number</th> -->
                <!-- <th>Fulfill Online Orders</th> -->
                <th >{{trans('locations.location-type')}}</th>
{{--                <th class="text-end">Actions</th>--}}
            </tr>
            </thead>
            <tbody>
            @foreach ($locations as $location)
                <tr>
                    <td >{{ $location->name }}</td>
                    <td>{{ $location->address }}</td>
                    <!-- <td>{{ $location->apartment }}</td> -->
                    <!-- <td>{{ $location->postal_code }}</td> -->
                     <td>{{ $location->city }}</td>
                    <!-- <td>{{ $location->phone_number }}</td> -->
                    <!-- <td>{{ $location->fulfill_online_orders ? 'Yes' : 'No' }}</td> -->
                    <td >{{ $location->default_location ? 'Default' : '' }}</td>
{{--                    <td class="text-end">--}}

{{--                        <a href="{{ route('locations.edit', $location->id) }}"><i class="fa-regular fa-pen-to-square fs-20"></i></a>--}}
{{--                        <!-- <form style="display:inline;" action="{{ route('locations.destroy', $location->id) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete?');">--}}
{{--                            @csrf--}}
{{--                            @method('DELETE')--}}
{{--                            <button type="submit" style="background:none;border:0;"><i class="fa-regular fa-trash-can fs-20 text-danger"></i></button>--}}
{{--                        </form> -->--}}

{{--                        @if (!$location->default_location)--}}
{{--                            <a href="#" data-id="{{ $location->id }}" data-retailer-name=""--}}
{{--                               data-bs-toggle="modal" data-bs-target="#delete-modal-{{ $location->id }}" class="btn-delete text-decoration-none">--}}
{{--                                <i class="fa-regular fa-trash-can fs-20 text-danger"></i>--}}
{{--                            </a>--}}
{{--                        @endif--}}

{{--                        <a href="#" data-id="{{$location->id}}" data-retailer-name=""--}}
{{--                                                   data-bs-toggle="modal" data-bs-target="#delete-modal-{{$location->id}}" class="btn-delete text-decoration-none">--}}
{{--                                                    <i class="fa-regular fa-trash-can fs-20 text-danger"></i>--}}
{{--                                                </a>--}}

{{--                    </td>--}}
                    <x-assets.delete-modal id="{{$location->id}}" text="Are you sure you want to delete this Location?" button="Delete location" title="Delete location" url="{{ route('locations.destroy', $location->id) }}" type="location"/>
                </tr>
            @endforeach
            </tbody>
        </table>

                        @else
                            <x-general.empty-page description="No Locations found"/>
                        @endif
        <!-- <a href="{{ route('locations.create') }}">Add New Location</a> -->

    </div>
@endsection
@push('footer_scripts')
@endpush
