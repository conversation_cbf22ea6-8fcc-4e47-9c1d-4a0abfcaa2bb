@extends('layouts.app_new')

@section('titles','Dashboard')
@section('content')
    {{--    <div class="row d-none">--}}
    {{--        --}}{{--          topbar of dashborad start          --}}
    {{--        <div class="col-12">--}}
    {{--            <div class="row bg-primary rounded">--}}
    {{--                <div class="col-12 col-sm-12 col-md-6 col-xl-8">--}}
    {{--                    <div class="mt-2">--}}
    {{--                        <h1 class="text-capitalize mb-0 text-white">--}}
    {{--                            <span--}}
    {{--                                class="greetings-js">Hi</span>! {{ /*trans('apimio_dashboard.heading')*/ \Illuminate\Support\Facades\Auth::user()->fname .' '.  \Illuminate\Support\Facades\Auth::user()->lname}}--}}
    {{--                        </h1>--}}
    {{--                        <p class="text-white mb-0">--}}
    {{--                            {{ trans('apimio_dashboard.subheading') }}--}}
    {{--                        </p>--}}
    {{--                    </div>--}}
    {{--                </div>--}}
    {{--                <div class="col-12 col-md-3 col-xl-4 my-2 my-sm-3 my-md-5 mt-lg-4 pt-md-2 d-flex justify-content-end">--}}
    {{--                    @can('add_and_edit_product' , [\App\Models\Organization\OrganizationUserPermission::class , auth()->user()->organization_id])--}}
    {{--                        <a href="#"--}}
    {{--                           class="btn btn-light float-md-right text-decoration-none"--}}
    {{--                           id="dashboard-add-vendor-top" data-bs-toggle="modal" data-bs-target="#create_product">--}}
    {{--                            {{ trans('apimio_dashboard.add_product_btn') }}--}}
    {{--                        </a>--}}
    {{--                    @endcan--}}
    {{--                    <a href="{{ route("import.csv.step1") }}"--}}
    {{--                       class="btn btn-outline-light float-right disabled-with-text text-decoration-none ms-2"--}}
    {{--                       id="dashboard-bulk-import-top">--}}
    {{--                        {{ trans('apimio_dashboard.bulk_import_btn') }}--}}
    {{--                    </a>--}}


    {{--                </div>--}}
    {{--            </div>--}}
    {{--        </div>--}}
    {{--        --}}{{--           topbar of dashborad end            --}}
    {{--        <div class="row row-cols-1 row-cols-sm-1 row-cols-md-1 row-cols-lg-2 mt-4 gx-4">--}}
    {{--            <div--}}
    {{--                @can('perform_billing' , [\App\Models\Organization\OrganizationUserPermission::class , auth()->user()->organization_id])--}}
    {{--                class="col border rounded"--}}
    {{--                @else--}}
    {{--                class="col-lg-12 border rounded"--}}
    {{--                @endcan >--}}
    {{--                <h3 class="Poppins mt-2 semibold text-dark">--}}
    {{--                    {{ trans('apimio_dashboard.overview') }}--}}
    {{--                </h3>--}}
    {{--                <div class="row">--}}
    {{--                    <div class="col-12 pr-0 ">--}}
    {{--                        <div class="d-flex align-items-center justify-content-around">--}}
    {{--                            <div class="py-5">--}}
    {{--                                <a href="{{route('products.index')}}" class="only-disabled">--}}
    {{--                                    <h5 id="vendor_counter"--}}
    {{--                                        class="m-0 text-primary text-center">--}}
    {{--                                        {{__($data['product_count'])}}--}}
    {{--                                    </h5>--}}
    {{--                                    <p class="card-text mt-2 Roboto text-center text-dark">{{ trans('apimio_dashboard.products') }}</p>--}}
    {{--                                </a>--}}
    {{--                            </div>--}}
    {{--                            <div class="py-5">--}}
    {{--                                <a href="{{route('products.index')}}" class="only-disabled">--}}
    {{--                                    <h5 id="collection_count"--}}
    {{--                                        class="m-0 font-32 text-primary text-center">{{__($data['sku_count'])}}</h5>--}}
    {{--                                    <p class="card-text mt-2 text-dark text-center">{{ trans('apimio_dashboard.sku') }} </p>--}}
    {{--                                </a>--}}
    {{--                            </div>--}}
    {{--                            <div class="py-5">--}}
    {{--                                <div id="product-count"--}}
    {{--                                     style="font-size: 16px;font-weight: normal;cursor: pointer">--}}
    {{--                                    <a href="{{route('brands.index')}}" class="only-disabled">--}}
    {{--                                        <h5 id="product_count"--}}
    {{--                                            class="m-0 font-32 text-primary text-center">{{__($data['brand_count'])}}</h5>--}}
    {{--                                        <p class="card-text mt-2 Roboto text-dark text-center">{{ trans('apimio_dashboard.brands') }} </p>--}}
    {{--                                    </a>--}}
    {{--                                </div>--}}
    {{--                            </div>--}}
    {{--                        </div>--}}
    {{--                    </div>--}}
    {{--                </div>--}}

    {{--            </div>--}}
    {{--            --}}{{--Invited retailer list is displayed                 --}}
    {{--            @if(!$invites_list->isEmpty())--}}
    {{--                <x-Invite.invitations/>--}}
    {{--            @else--}}

    {{--            @endif--}}
    {{--        </div>--}}
    {{--        <div class="card border-radius shadow-none mt-4">--}}
    {{--            <div class="card-body p-0">--}}

    {{--                <h3 class="Roboto mt-1 mb-4 semibold text-dark" style="font-size: 20px">--}}
    {{--                    {{ trans('apimio_dashboard.guide_heading') }}--}}
    {{--                </h3>--}}

    {{--                <div class="row">--}}
    {{--                    <div class="col-3 pr-0">--}}
    {{--                        <div class="list-group" id="list-tab" role="tablist">--}}
    {{--                            <a class="list-group-item list-group-item-action border-left-show bold p-3 active"--}}
    {{--                               id="list-product-view-list"--}}
    {{--                               data-toggle="list"--}}
    {{--                               href="#list-product-view"--}}
    {{--                               role="tab"--}}
    {{--                               aria-controls="product-view">--}}
    {{--                                {{ trans('apimio_dashboard.product_status') }}--}}
    {{--                            </a>--}}
    {{--                            <a class="list-group-item list-group-item-action border-left-show bold p-3"--}}
    {{--                               id="list-channel-list"--}}
    {{--                               data-toggle="list"--}}
    {{--                               href="#list-channel"--}}
    {{--                               role="tab"--}}
    {{--                               aria-controls="channel">--}}
    {{--                                {{ trans('apimio_dashboard.catalog_partners') }}--}}
    {{--                            </a>--}}
    {{--                            <a class="list-group-item list-group-item-action border-left-show bold p-3"--}}
    {{--                               id="list-sync-list"--}}
    {{--                               data-toggle="list"--}}
    {{--                               href="#list-sync"--}}
    {{--                               role="tab"--}}
    {{--                               aria-controls="sync">--}}
    {{--                                {{ trans('apimio_dashboard.import_products') }}--}}
    {{--                            </a>--}}
    {{--                            <a class="list-group-item list-group-item-action border-left-show bold p-3"--}}
    {{--                               id="list-export-list"--}}
    {{--                               data-toggle="list"--}}
    {{--                               href="#list-export"--}}
    {{--                               role="tab"--}}
    {{--                               aria-controls="export">--}}
    {{--                                {{ trans('apimio_dashboard.export_products') }}--}}
    {{--                            </a>--}}
    {{--                        </div>--}}
    {{--                    </div>--}}
    {{--                    <div class="col-9 pl-0">--}}
    {{--                        <div class="tab-content"--}}
    {{--                             id="nav-tabContent"--}}
    {{--                             style="background-color: #F2F8FF;border: 1px solid #B7D8FF">--}}
    {{--                            <div class="tab-pane px-3 fade show active" id="list-product-view" role="tabpanel"--}}
    {{--                                 aria-labelledby="list-product-view-list">--}}
    {{--                                <x-guide.view-product-status--}}
    {{--                                    btnname="{{trans('apimio_dashboard.view_all_products_btn')}}"--}}
    {{--                                    :hrefroutes="route('products.index')"/>--}}
    {{--                            </div>--}}
    {{--                            <div class="tab-pane px-3 fade" id="list-channel" role="tabpanel"--}}
    {{--                                 aria-labelledby="list-channel-list">--}}
    {{--                                <x-guide.connect-with-channel/>--}}
    {{--                            </div>--}}
    {{--                            <div class="tab-pane px-3 fade" id="list-sync" role="tabpanel"--}}
    {{--                                 aria-labelledby="list-sync-list">--}}
    {{--                                <x-guide.sync-products/>--}}
    {{--                            </div>--}}
    {{--                            <div class="tab-pane px-3 fade" id="list-export" role="tabpanel"--}}
    {{--                                 aria-labelledby="list-export-list">--}}
    {{--                                <x-guide.export-products/>--}}
    {{--                            </div>--}}
    {{--                        </div>--}}
    {{--                    </div>--}}
    {{--                </div>--}}
    {{--            </div>--}}
    {{--        </div>--}}

    {{--        <div class="card border-radius shadow-none border-color position-relative product-section">--}}
    {{--            <div class="card-body">--}}
    {{--                <div class="row">--}}
    {{--                    <div class="col-12 col-lg-9">--}}
    {{--                        <div class="mt-2">--}}
    {{--                            <h3 class="mb-2 mt-0">--}}
    {{--                                <a class="Poppins semibold text-dark" style="text-decoration: none;"--}}
    {{--                                   href="#">--}}
    {{--                                    {{ trans('apimio_dashboard.product_heading') }}</a>--}}
    {{--                            </h3>--}}
    {{--                        </div>--}}
    {{--                    </div>--}}
    {{--                    @if($data['product_count'] > 0)--}}
    {{--                        <div class="col-12">--}}
    {{--                            <x-products.product-listing-table :filter="false" :limit="8"/>--}}
    {{--                        </div>--}}
    {{--                        <div class="divider mt-0 mb-4 mx-4"></div>--}}

    {{--                        <div class="col-12 text-center pb-2">--}}
    {{--                            <a href="{{route('products.index')}}"--}}
    {{--                               class="justify-content-center btn-primary-tertiary bold ripplelink disabled-with-text"--}}
    {{--                               id="view-all-product-btn" style="">--}}
    {{--                                {{ trans('apimio_dashboard.view_all_products') }}--}}
    {{--                                <img class="ml-3"--}}
    {{--                                     src="{{asset('./media/retailer-dashboard/Icon awesome-long-arrow-alt-right.png')}}"--}}
    {{--                                     alt=""/>--}}
    {{--                            </a>--}}
    {{--                        </div>--}}
    {{--                    @else--}}
    {{--                        <div class="col-12">--}}
    {{--                            <x-general.empty-table descriptions="{{trans('apimio_dashboard.empty_table_description')}}"--}}
    {{--                                                   btnName="{{ trans('apimio_dashboard.add_product_btn') }}"/>--}}
    {{--                        </div>--}}
    {{--                    @endif--}}
    {{--                </div>--}}
    {{--            </div>--}}
    {{--        </div>--}}
    {{--        <!-- Modal -->--}}
    {{--        --}}{{--        <x-products.add-product/>--}}
    {{--    </div>--}}







    {{-- /***************************   new code start    *************************/--}}
    {{--          topbar of dashborad start          --}}
    <div class="row mx-2 mx-lg-0 mx-xxl-0 position-relative">
        <div class="col-12 ms-1">
            <div class="row bg-primary rounded px-4 py-3 dashboard-title">
                <div class="col-12 col-sm-12 col-md-7 col-xl-8 p-0">
                    <div class="mt-2">
                        <h1 class="text-capitalize mb-0 text-white">
                            <span
                                class="greetings-js">Hi</span>! {{ /*trans('apimio_dashboard.heading')*/ \Illuminate\Support\Facades\Auth::user()->fname .' '.  \Illuminate\Support\Facades\Auth::user()->lname}}
                        </h1>
                        <p class="text-white mb-0">
                            {{ trans('apimio_dashboard.subheading') }}
                        </p>
                    </div>
                </div>
                <div class="col-12 col-md-5 col-xl-4  mt-3 pt-1 d-flex justify-content-end p-0">
                    @can('SubscriptionAccess', 'product')
                        @can('add_and_edit_product' , [\App\Models\Organization\OrganizationUserPermission::class , auth()->user()->organization_id])
                            <a href="#"
                            class="btn btn-light float-md-right text-decoration-none"
                            id="dashboard-add-vendor-top" data-bs-toggle="modal" data-bs-target="#create_product">
                                {{ trans('apimio_dashboard.add_product_btn') }}
                            </a>
                        @endcan
                    @endcan
                    @can('SubscriptionAccess', 'import')
                        <a href="{{ route("import.csv.step1") }}"
                        class="btn btn-outline-light float-right ml-2 disabled-with-text text-decoration-none ms-2"
                        id="dashboard-bulk-import-top">
                            {{ trans('apimio_dashboard.bulk_import_btn') }}
                        </a>
                    @endcan


                </div>
            </div>
        </div>
        {{-- cards score start--}}
        <div class="col-12 px-1">
            <div class="row gx-4 d-flex">
                {{-- overview--}}
                <div class="col-12 col-xxl-6 mt-4 d-inline-flex align-self-stretch">
                    <div class="border rounded-3 shadow-sm px-4 py-4 w-100">
                        <h3 class="fw-700 text-uppercase mb-0">Overview</h3>
                        <p class="mb-0 fs-14"><p class="mb-0 fs-14">Total number of SKUs, Categories and Brands in your organization.
                        </p>
                        <div class="d-flex justify-content-around mt-5 pt-3 m-auto w-75">
                            <div class="d-flex flex-column align-items-center justify-content-center">
                                <a href="{{route('products.index')}}" class="text-decoration-none mb-2 fs-32 font-weight-bold text-primary">{{$data['sku_count']}}</a>
                                <p>SKU's</p>
                            </div>
                            <div class="d-flex flex-column align-items-center justify-content-center">
                                <a href="{{route('products.index')}}" class="text-decoration-none mb-2 fs-32 font-weight-bold text-primary">{{ $data['product_count'] }}</a>
                                <p>Products</p>
                            </div>
                            <div class="d-flex flex-column align-items-center justify-content-center">
                                <a href="{{route('brands.index')}}" class="text-decoration-none mb-2 fs-32 font-weight-bold text-primary">{{__($data['brand_count'])}}</a>
                                <p>Brands</p>
                            </div>
                            <div class="d-flex flex-column align-items-center justify-content-center">
                                <a href="{{route('gallery.index')}}" class="text-decoration-none mb-2 fs-32 font-weight-bold text-primary">{{__($data['images'])}}</a>
                                <p>Images</p>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Product Completion score start--}}
                <x-completeness-dashboard-product-scoring></x-completeness-dashboard-product-scoring>
                {{--     Product Completion score end--}}

                {{-- Images Quality Score start --}}
                <x-gallery-dashboard-gallery-image-scoring></x-gallery-dashboard-gallery-image-scoring>
                {{-- Images Quality Score end--}}

                {{-- storage usage start--}}
                <x-gallery-dashboard-gallery-storage></x-gallery-dashboard-gallery-storage>
                {{-- storage usage end--}}

            </div>
        </div>
        {{--  cards end--}}
        {{--    tabs start --}}
        {{--        <h2 class="mt-4 mb-0">--}}
        {{--            {{ trans('apimio_dashboard.guide_heading') }}--}}
        {{--        </h2>--}}
        {{--        <div class="col-12 px-1 mt-4">--}}
        {{--            <div class="d-flex align-items-start rounded shadow-sm border bg-light-blue tabs-style-dashboard">--}}
        {{--                <div class="nav flex-column nav-pills bg-white" id="v-pills-tab-dashboard" role="tablist"--}}
        {{--                     aria-orientation="vertical">--}}
        {{--                    <button class="nav-link active border py-3 px-5" id="v-pills-home-tab" data-bs-toggle="pill"--}}
        {{--                            data-bs-target="#v-pills-home" type="button" role="tab" aria-controls="v-pills-home"--}}
        {{--                            aria-selected="true">View Product Status--}}
        {{--                    </button>--}}
        {{--                    <button class="nav-link border py-3 px-5" id="v-pills-profile-tab" data-bs-toggle="pill"--}}
        {{--                            data-bs-target="#v-pills-profile" type="button" role="tab" aria-controls="v-pills-profile"--}}
        {{--                            aria-selected="false">Catalog partners--}}
        {{--                    </button>--}}
        {{--                    <button class="nav-link border py-3 px-5" id="v-pills-messages-tab" data-bs-toggle="pill"--}}
        {{--                            data-bs-target="#v-pills-messages" type="button" role="tab" aria-controls="v-pills-messages"--}}
        {{--                            aria-selected="false">Import Products to Channel--}}
        {{--                    </button>--}}
        {{--                    <button class="nav-link border py-3 px-5" id="v-pills-settings-tab" data-bs-toggle="pill"--}}
        {{--                            data-bs-target="#v-pills-settings" type="button" role="tab" aria-controls="v-pills-settings"--}}
        {{--                            aria-selected="false">Export Products--}}
        {{--                    </button>--}}
        {{--                </div>--}}
        {{--                <div class="tab-content py-3 px-3 w-100" id="v-pills-tabContent">--}}
        {{--                    --}}{{-- view product status--}}
        {{--                    <div class="tab-pane fade show active w-75" id="v-pills-home" role="tabpanel"--}}
        {{--                         aria-labelledby="v-pills-home-tab">--}}
        {{--                        <div class="d-flex flex-column flex-sm-row justify-content-between mt-4 ms-xxl-4">--}}
        {{--                            <div class="field-score-chart-tab d-inline-flex align-self-center">--}}
        {{--                                <canvas id="viewProductStatus"></canvas>--}}
        {{--                            </div>--}}
        {{--                            <div class="d-flex flex-column align-items-start align-items-md-center d-none d-lg-block  justify-content-around me-4 ms-4">--}}
        {{--                                <span class="score-label score-label-publish mt-4 mt-md-0"></span>--}}
        {{--                                <span class="score-label score-label-success mt-4 mt-md-0"></span>--}}
        {{--                                <span class="score-label score-label-draft mt-4 mt-md-0"></span>--}}
        {{--                            </div>--}}
        {{--                            <div--}}
        {{--                                class="d-flex flex-column justify-content-sm-start justify-content-md-center d-none d-lg-block mt-4 mt-md-0 w-md-50 w-lg-75">--}}
        {{--                                <h4 class="mb-0 fw-700">Publish</h4>--}}
        {{--                                <p class="mb-0 w-300">Products are updated.</p>--}}
        {{--                                <h4 class=" mb-0 fw-700 mt-5">Draft</h4>--}}
        {{--                                <p class="mb-0 w-300">No products are assigned.</p>--}}
        {{--                            </div>--}}
        {{--                            <div--}}
        {{--                                class="d-flex flex-column align-items-start justify-content-around mt-md-0">--}}
        {{--                                <span class="status status-publish mt-4 mt-md-0">435</span>--}}
        {{--                                <span class="status status-draft mt-4 mt-md-0">03</span>--}}
        {{--                            </div>--}}
        {{--                        </div>--}}
        {{--                        <p class="mb-0 text-center mt-3">(You have <span class="fw-700">500 SKUs remaining</span> in--}}
        {{--                            your current plan)</p>--}}
        {{--                    </div>--}}
        {{--                    <div class="tab-pane fade px-0 mx-0 px-xl-2 mx-xl-2 px-xxl-5 mx-xxl-5" id="v-pills-profile"--}}
        {{--                         role="tabpanel"--}}
        {{--                         aria-labelledby="v-pills-profile-tab">--}}
        {{--                        <div class="row">--}}
        {{--                            <div class="col-12 col-xxl-6">--}}
        {{--                                <h2 class="mb-0">Vendor</h2>--}}
        {{--                                <p class="mb-0 ms-1">Invite your vendors and share product catalogs with them.</p>--}}
        {{--                                <div class="text-center mt-3">--}}
        {{--                                    <div class="mb-2"><img src="{{asset('assets/images/add-vendors.svg')}}"--}}
        {{--                                                           class="add-vendor" alt="add vendors"></div>--}}
        {{--                                    <a href="" class="btn btn-primary add-vendors-btn"><img--}}
        {{--                                            src="{{asset('assets/images/invite-vendors.svg')}}" alt=""> Add a Vendor</a>--}}
        {{--                                </div>--}}
        {{--                            </div>--}}
        {{--                            <div class="col-12 col-xxl-6">--}}
        {{--                                <h2 class="mb-0">Retailer</h2>--}}
        {{--                                <p class="mb-0">Invite your retailers and share product Stores with them.</p>--}}
        {{--                                <div class="text-center mt-3">--}}
        {{--                                    <div class="mb-2">--}}
        {{--                                        <img src="{{asset('assets/images/add-retailer.svg')}}" class="add-vendor"--}}
        {{--                                             alt="add vendors">--}}
        {{--                                    </div>--}}
        {{--                                    <a href="" class="btn btn-primary add-vendors-btn"><img--}}
        {{--                                            src="{{asset('assets/images/invite-vendors.svg')}}" alt=""> Add a--}}
        {{--                                        Retailer</a>--}}
        {{--                                </div>--}}
        {{--                            </div>--}}
        {{--                        </div>--}}
        {{--                    </div>--}}
        {{--                    <div class="tab-pane fade" id="v-pills-messages" role="tabpanel"--}}
        {{--                         aria-labelledby="v-pills-messages-tab">--}}
        {{--                        <h2 class="mb-0">Manage Stores</h2>--}}
        {{--                        <p class="mb-0">Connect your stores with Apimio. You can start publishing products to stores--}}
        {{--                            once it is connected</p>--}}
        {{--                        <div class="d-flex justify-content-center mt-5 pt-3">--}}
        {{--                            <div class="d-flex flex-column align-items-center justify-content-center me-2">--}}
        {{--                                <a href="" class="text-decoration-none mb-2 fs-24 font-weight-bold mb-2">1</a>--}}
        {{--                                <p>Catalogs Connected</p>--}}
        {{--                            </div>--}}
        {{--                            <div class="d-flex flex-column align-items-center justify-content-center ms-5">--}}
        {{--                                <a href="" class="text-decoration-none mb-2 fs-24 font-weight-bold mb-2">0</a>--}}
        {{--                                <p>Catalogs Shared</p>--}}
        {{--                            </div>--}}
        {{--                        </div>--}}
        {{--                    </div>--}}
        {{--                    <div class="tab-pane fade" id="v-pills-settings" role="tabpanel"--}}
        {{--                         aria-labelledby="v-pills-settings-tab">--}}
        {{--                        <h2 class="mb-0">Export product</h2>--}}
        {{--                        <p class="mb-0">You have 85 products added. Click button below to see options in which you can--}}
        {{--                            export your products.</p>--}}

        {{--                        <div class="d-flex align-items-center justify-content-center flex-column">--}}
        {{--                            <img src="{{asset('assets/images/export-product.svg')}}" class="add-vendor"--}}
        {{--                                 alt="add vendors">--}}
        {{--                            <a href="" class="btn btn-primary w-25 add-vendors-btn mt-4"><img--}}
        {{--                                    src="{{asset('assets/images/invite-vendors.svg')}}" alt=""> Add a Retailer</a>--}}
        {{--                        </div>--}}
        {{--                    </div>--}}
        {{--                </div>--}}
        {{--            </div>--}}
        {{--        </div>--}}
        {{--    tabs end --}}
        {{-- table start--}}
        <h2 class="mb-0 mt-0 mt-4 px-1">
            <a class="text-decoration-none clr-black"
               href="#">
                {{ trans('apimio_dashboard.product_heading') }}</a>
        </h2>

        <div class="col-12 px-0">
        <div id="listingTable-dashboard" data-delete-product-route="{{ route('products.destroy', ':id') }}" data-orgId="{{auth()->user()->organization_id??null }}" data-show-search="false" data-show-pagination="false" data-show-delete="false" data-show-checkbox="false"></div>
                     @vite("resources/js/components/productListing/ListingTable.jsx")
            {{-- <x-products.product-listing-table :filter="false" :limit="8" isbulkable="true"/> --}}
        </div>
        @if ($totalProducts > 8)
        <div class="d-flex justify-content-center">
        <a href="/products">See more</a>
        </div>

        @endif
        {{-- table end--}}

        {{--        scroll button --}}
        <span id="scrollBtn" class="scroll-btn fa-sharp fa-solid fa-arrow-right fs-20"></span>
    </div>

    {{-- MODAL CODE START--}}
    <div class="modal fade" id="dashboardModal" data-bs-backdrop="static" data-bs-keyboard="false"  aria-hidden="true" aria-labelledby="exampleModalToggleLabel" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content p-5 onBoardingModal-js" style="border-radius: 10px">
                <h1 class="text-center">Start Onboarding your Products</h1>
                <p class="text-center clr-grey">Yay! You’re just one more step away</p>

                <div class="cards-section pt-3">
                    <div class="card dashboardCard">
                        <div class="d-flex align-items-center">
                            <img src="{{asset('assets/images/csv.png')}}" class="dashboardCard_image" alt="csv image">
                            <h2 class="ms-4 mb-0 flex-grow-1">Import Your Existing CSV</h2>
                            <a href="{{route('import.csv.step1')}}" class="btn btn-primary pt-2 ms-2 arrow-btn">
                                <i class="icon icon-arrow-right"></i>
                            </a>
                        </div>
                    </div>

                    <div class="card dashboardCard mt-3">
                        <div class="d-flex align-items-center">
                            <img src="{{asset('assets/images/shopify.png')}}" class="dashboardCard_image" alt="csv image">
                            <h2 class="ms-4 mb-0 flex-grow-1">Connect Your Shopify Store To Import Products </h2>
                            <button data-bs-toggle="modal" data-bs-target="#shopifyModal" class="btn btn-primary pt-2 ms-2 arrow-btn shopifyButton-js">
                                <i class="icon icon-arrow-right"></i>
                            </button>
                        </div>
                    </div>

                    <div class="card dashboardCard mt-3">
                        <div class="d-flex align-items-center">
                            <img src="{{asset('assets/images/addproduct.png')}}" class="dashboardCard_image" alt="csv image">
                            <h2 class="ms-4 mb-0 flex-grow-1">Add Product Manually</h2>
                            <button data-bs-toggle="modal" data-bs-target="#create_product" class="btn btn-primary pt-2 ms-2 arrow-btn shopifyButton-js">
                                <i class="icon icon-arrow-right"></i>
                            </button>
                        </div>
                    </div>
                    <div class="text-center mt-4">
                        <a href="#" data-bs-dismiss="modal" class="text-decoration-none position-relative"> <span>Continue to dashboard</span> <i class="icon icon-arrow-right ms-2 position-absolute" style="top:-1px"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- MODAL CODE END--}}

    {{--SHOPIFY MODAL START--}}
    <div class="modal fade" id="shopifyModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg" style="width: 40em;">
            <div class="modal-content">
                <form id="org_cp_con_shop_form" method="GET" action="{{ route("channel.shopify.install") }}" class="formStyle">
                    <div class="modal-header pb-3">
                        <h3 class="modal-title" id="exampleModalLabel">{{ trans('organization_checkpoint.modal_title') }}</h3>
                        <button type="button" class="btn-sm border-0 bg-white fs-24 px-0 pt-0 shopify-btn-js" data-bs-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body border-top border-bottom">
                        <input name="should_not_billed" type="hidden" value="1">
                        <label for="shop">{{ trans('organization_checkpoint.shop_url') }}</label>
                        <input name="shop" type="text"
                               class="form-control @error('name') is-invalid @enderror"
                               placeholder="your-shop-url.myshopify.com" required />
                        <br>
                        <div class="form-group">
                            <label >{{ trans('organization_checkpoint.sync_shopify_products') }}</label>
                            <select name="sync_product" class="form-control"
                                    required >
                                <option value="">Select ...</option>
                                <option value="yes">Yes</option>
                                <option value="no">No</option>
                            </select>
                        </div>
                        <div class="pl-1 py-2">
                            <small style="color: #6b7177">
                                {{ trans('organization_checkpoint.no_store') }}
                                <a href="https://www.shopify.com/" target="_blank" style="color: #008060!important;"
                                   class="inline-block text-decoration-none Roboto forgot-text">
                                    {{ trans('organization_checkpoint.shopify_url') }}
                                </a>
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer justify-content-end py-3">
                        <button type="button" class="mr-3 btn btn-outline-danger btn-shopify-secondary shopify-btn-js" data-bs-dismiss="modal">
                            {{ trans('organization_checkpoint.cancel_btn') }}</button>
                        <button id="org_cp_con_btn" type="submit" class="btn btn-primary btn-shopify-primary">{{ trans('organization_checkpoint.connect_btn') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {{--SHOPIFY MODAL END--}}

    {{--SHOPIFY Restart MODAL Start--}}
    <div class="modal fade" id="shopifyRestartModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg" style="width: 40em;">
            <div class="modal-content">
                <form id="org_cp_con_shop_form" method="GET" action="{{ route("channel.shopify.install") }}" class="formStyle">
                    <input type="hidden" name="is_shopify_update" value="1">
                    <input type="hidden" name="channel_id" value="{{$shopify_channel->channel_id ?? ''}}">
                    <input name="shop" type="hidden" value="{{$shopify_channel->shop ?? ''}}" />
                    <div class="modal-header pb-3">
                        <h3 class="modal-title" id="exampleModalLabel"> 🌟 Exciting News! 🌟</h3>
                    </div>
                    <div class="modal-body border-top border-bottom">
                       <div class="row">
                        <div class="col-12">
                        <p><strong>New Features Await You!</strong></p>
                <ul>
                    <li>🔹 Improved navigation</li>
                    <li>🔹 Faster checkout process</li>
                    <li>🔹 Enhanced product recommendations</li>
                    <li>🔹 ... and more!</li>
                </ul>
                <p>Update now to ensure a smoother, faster, and more enjoyable shopping spree!</p>
            </div>
                        </div>

                        <div class="pl-1 py-2">
                            <small style="color: #6b7177">
                                {{ trans('organization_checkpoint.no_store') }}
                                <a href="https://www.shopify.com/" target="_blank" style="color: #008060!important;"
                                   class="inline-block text-decoration-none Roboto forgot-text">
                                    {{ trans('organization_checkpoint.shopify_url') }}
                                </a>
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer justify-content-end py-3">
                        <button type="submit" class="btn btn-primary btn-shopify-primary">
                            Update</button>

                    </div>
                </form>
            </div>
        </div>
    </div>
    {{--SHOPIFY Restart MODAL END--}}
    <x-products.add-product/>
    {{--           topbar of dashborad end            --}}
    {{--    new code end--}}

@endsection
@push('footer_scripts')
    {{--    scroll script--}}
    <script>
    // var url = '/api/user';
        var url = '/api/product/1/versions/1/variants';
fetch(url, {
  method: 'GET',
  headers: {
    'Accept': 'application/json'
  }
})
  .then(response => response.json())
  .then(data => {
    // Handle the response here
    console.log(data);
  })
  .catch(error => {
    // Handle errors here
    console.error('Error:', error);
  });
        $( document ).ready(function() {
            window.addEventListener('scroll', function() {
                var scrollBtn = document.getElementById('scrollBtn');
                if (window.pageYOffset >70) {
                    scrollBtn.style.display = 'block';
                } else {
                    scrollBtn.style.display = 'none';
                }
            });
            // Scroll to top when the scroll button is clicked
            document.getElementById('scrollBtn').addEventListener('click', function() {
                window.scrollTo({top: 0, behavior: 'smooth'});
            });
            $(window).on('load', function() {
              var boolval =  "{{ $show_popup }}";
              if(boolval){
                $("#shopifyRestartModal").modal("show");
              }
            });
        });

        document.addEventListener("DOMContentLoaded", function() {
            var scrollBtn = document.getElementById('scrollBtn');
            if (window.pageYOffset >70) {
                scrollBtn.style.display = 'block';
            } else {
                scrollBtn.style.display = 'none';
            }
        });
    </script>
    <script>
        $(document).ready(function () {
            @error("sku")
            $("#create_product").modal("show");
            @enderror
            @error("name")
            $("#create_product").modal("show");
            @enderror

            // This method is used to show the greetings messages on dashboard.
            var myDate = new Date();
            var hrs = myDate.getHours();
            var greet;
            if (hrs < 12)
                greet = 'Good Morning';
            else if (hrs >= 12 && hrs <= 17)
                greet = 'Good Afternoon';
            else if (hrs >= 17 && hrs <= 24)
                greet = 'Good Evening';
            $('.greetings-js').text(greet)

        });


    </script>
    <script>

        const viewImageStatus = document.getElementById('viewProductStatus');
        new Chart(viewImageStatus, {
            type: 'doughnut',
            data: {
                labels: [
                    'Publish',
                    'Draft',

                ],
                datasets: [{
                    data: [100, 300],
                    backgroundColor: [
                        '#2C4BFF',
                        '#6C757D',
                    ],
                    hoverOffset: 4
                }]
            },
            options: {
                cutout: 50,
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            },
        });

    </script>
    {{-- modal script start--}}
    <script>
        $(document).ready(function() {
            @if (request('onBoarding'))
            $('#dashboardModal').modal('show');
            $('.shopifyButton-js').on('click',function () {
                $('.onBoardingModal-js').css('opacity', '0');
            })
            $('.shopify-btn-js').on('click',function () {
                $('.onBoardingModal-js').css('opacity', '1');
            })
            $('.cancel-product-modal-js').on('click',function () {
                $('.onBoardingModal-js').css('opacity', '1');
            })
            @endif


        });
    </script>
@endpush
