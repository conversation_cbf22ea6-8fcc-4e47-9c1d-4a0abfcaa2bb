<?php

namespace Apimio\MappingConnectorPackage\components;

use Illuminate\View\Component;

class VariantsOption extends Component
{
    /**
     * The heading attributes and selection template option value.
     *
     * @var array
     */

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct()
    {

    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|string
     */
    public function render()
    {
        return view('mapping::components.variants-option');
    }
}
