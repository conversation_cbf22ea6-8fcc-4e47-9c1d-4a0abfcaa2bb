@extends('mapping::layouts.app')
@section('titles','Mapping Fields')
@section('content')
<style>
.modal:after {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100vw;
    height: 100vh;
    background-color: #000;
    opacity: 0.7;
}
</style>
<div id="importmapping" ></div>

@if (App::environment('local') && file_exists(base_path('packages/apimio/mapping-fields-package/resources/js/components/MappingModuleRedux.jsx')))
    @vite("packages/apimio/mapping-fields-package/resources/js/components/MappingModuleRedux.jsx")
@else
    @vite("vendor/apimio/mapping-connector-package/resources/js/components/MappingModuleRedux.jsx")
@endif

@push('footer_scripts')
        <script src="{{ asset('mapping-fields/js/jquery.insert-at-cursor.min.js') }}"></script>

        <script>
            $(document).ready(function () {
                $(document).scroll(function () {
                    var scrollPosition = $(this).scrollTop(); // Define scrollPosition inside the scroll function
                    var productHeader = $(".product-header");

                    if (scrollPosition >= 10) {
                        var width = $('#add_row').width() + 47;
                        productHeader.addClass("fixed-header shadow-md");
                        productHeader.css("width", width);
                    } else {
                        productHeader.removeClass("fixed-header shadow-md");
                        productHeader.removeAttr("style");
                    }

                });

            })

        </script>
@endpush
@endsection

    <script>
        var data = @json($data);
        console.log(data);
        window.mappingData = data ;
    </script>

<script src="https://cdn.tailwindcss.com"></script>
