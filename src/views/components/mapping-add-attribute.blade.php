@push('header_scripts')
    <style>

        datalist {
            background-color: white;
            border: 1px solid #000;
            border-top: none;
            width: 93.5%;
            position: absolute;
            padding: 5px;
            box-shadow: 0px 0px 5px 1px rgb(0 0 0 / 20%);
            max-height: 100px;
            overflow-y: auto;
        }
        datalist > option {
            padding: 0px 15px;
            color: #000 !important;
            background: #fff;
            font-size: 13px;
            cursor: pointer;
        }

        datalist > option:hover {
            background-color: #418EFF;
            color:#fff !important;
        }
    </style>
@endpush

<div class="modal fade" id="create_column" data-backdrop="static" data-keyboard="false" tabindex="-1"
     aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-static modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Create Apimio Attribute</h3>
                <button type="button" class="btn-sm border-0 bg-white fs-24 px-0 pt-0" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="mapping_create_new_attribute_main">
                    <form action="javascript:void(0)" method="post" id="mapping_create_new_attribute_form" class="formStyle">
                        <input type="hidden" name="organization_id" value="{{ $session_data['data_required']['organization_id'] ?? null }}">
                        <input type="hidden" name="method_type" value="import">
                        @csrf
                        <div class="form-group">
                            <label>{{__("Attribute Family")}}&nbsp;<span class="text-danger">*</span></label>

                            <input autocomplete="off" role="combobox" data-list-id="attribute_family_list"  class="data_list_input form-control" name="attribute_family_name" required>
                            <datalist id="attribute_family_list" role="listbox">
                                @foreach($session_data['apimio_attributes_required']['all_families'] as $family)
                                    <option value="{{ $family['name'] }}">{{ $family['name'] }}</option>
                                @endforeach
                            </datalist>
                        </div>
                        <div class="form-group mt-3">
                            <label>{{__("Attribute Title")}}&nbsp;<span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="name" placeholder="" required>
                            <p id="mapping_create_attribute_exist" class="text-danger small" style="display: none;"></p>
                        </div>
                        <div class="form-group mt-3">
                            <label>{{__("Attribute Type")}}&nbsp;<span class="text-danger">*</span></label>
                            <select class="form-control bg-white-smoke" name="attribute_type_id" required>
                                <option value="">Choose</option>
                                @foreach($session_data['apimio_attributes_required']['all_attributes'] as $attribute_type)
                                    <option value="{{ $attribute_type['id'] }}"> {{ $attribute_type['name'] }} </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group mt-40">
                            <button type="button" class="btn btn-primary btn-block" id="mapping_create_new_attribute_btn">{{__("Create")}}</button>
                        </div>
                    </form>
                </div>
                <div class="text-center" id="mapping_create_new_attribute_success" style="display: none">

                    <img
                        src="https://cdn.dribbble.com/users/2185205/screenshots/7886140/media/90211520c82920dcaf6aea7604aeb029.gif"
                        alt="" class="img-fluid">
                </div>
            </div>
        </div>
    </div>
</div>

@push('footer_scripts')

<script>
    var data_list_input = document.querySelector('.data_list_input');
    var datalist_parent = document.getElementById(data_list_input.getAttribute('data-list-id'));

    $(document).ready(function () {
        $(data_list_input).on('focus',function () {
            if($(datalist_parent).find('option').length > 0){
                $(datalist_parent).show();
                console.log($(datalist_parent));
            }
        })

        $(data_list_input).on('focusout',function () {
            setTimeout(function(){
                $(datalist_parent).hide();
            }, 100);
        })

        $(datalist_parent).on('click', 'option',function () {
            $(data_list_input).val($(this).val());
            $(datalist_parent).hide();
        })

    })

    data_list_input.oninput = function() {
        currentFocus = -1;
        var text = data_list_input.value.toUpperCase();
        var matched_text = false;
        for (let option of datalist_parent.options) {
            if (option.value.toUpperCase().indexOf(text) > -1) {
                matched_text = true;
                option.style.display = "block";
            } else {
                option.style.display = "none";
            }
        };

        if(!matched_text){
            datalist_parent.style.display = "none";
        }
        else{
            datalist_parent.style.display = "block";
        }
    }
    var currentFocus = -1;
    data_list_input.onkeydown = function(e) {
        if (e.keyCode == 40) {
            currentFocus++
            addActive(datalist_parent.options);
        } else if (e.keyCode == 38) {
            currentFocus--
            addActive(datalist_parent.options);
        } else if (e.keyCode == 13) {
            e.preventDefault();
            if (currentFocus > -1) {
                /*and simulate a click on the "active" item:*/
                if (datalist_parent.options) datalist_parent.options[currentFocus].click();
            }
        }
    }
    function addActive(x) {
        if (!x) return false;
        removeActive(x);
        if (currentFocus >= x.length) currentFocus = 0;
        if (currentFocus < 0) currentFocus = (x.length - 1);
        x[currentFocus].classList.add("active");
    }
    function removeActive(x) {
        for (var i = 0; i < x.length; i++) {
            x[i].classList.remove("active");
        }
    }
</script>

@endpush



