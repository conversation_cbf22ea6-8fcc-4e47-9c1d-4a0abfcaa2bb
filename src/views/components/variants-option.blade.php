<div class="col-lg-3">
    <div class="row">
        <div class="col-lg-12 d-flex align-items-center mb-3">
            <div class="form-group flex-grow-1">
                <select name="nodes[variant][variant_options][]"
                        class="form-control">
                    <option value="" class="Poppins regular text-color">Select Option
                    </option>
                    @foreach($input_array['nodes'] as $input_key => $input_vals)
                        @if($input_key != 'Default')
                            <optgroup label='{{(isset($input_key)) ?  $input_key : 'Others'}}'>
                                @endif
                                @foreach($input_vals as $input_val)
                                    @php
                                        if(isset($input_key)){
                                            $family_input_value = $input_key.','.$input_val;
                                        }
                                        else{
                                           $family_input_value = $input_val;
                                        }
                                    @endphp
                                    @if(isset($template_option) && $template_option != null)
                                        <option value="{{$family_input_value}}" {{($template_option == $family_input_value) ? 'selected' : null}} >{{$input_val}}</option>
                                    @else
                                        <option value="{{$family_input_value}}">{{$input_val}}</option>
                                    @endif
                                @endforeach
                                @if($input_key != 'Default')
                            </optgroup>
                        @endif
                    @endforeach
                </select>
            </div>
            <a href="javascript:void(0)" onclick="delete_opt(this)" class="ms-2 pe-auto">
                <i class="fa-regular fa-trash-can fs-20 text-danger"></i>
            </a>
        </div>

    </div>
</div>
