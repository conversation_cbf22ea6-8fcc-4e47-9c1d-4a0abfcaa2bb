<?php

namespace Apimio\MappingConnectorPackage\classes\formulas;

use Apimio\MappingConnectorPackage\classes\MappingConversion;
use Apimio\MappingConnectorPackage\traits\Formulas;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Psr\Log\InvalidArgumentException;

class ApplyTemplateOnItem
{
    use Formulas;


    protected $variant_parent_attr_array = [
        'SKU' => "sku",
        'Product Name' => "name",
        'Price' => 'price',
        'Compare at Price' => 'compare_at_price',
        'Quantity' => 'quantity',
        'UPC/EAN/ISBN' => 'barcode',
        'Weight' => 'weight',
        'Weight Unit' => 'weight_unit',
        'Media' => 'media',
    ];


    /**
     * Convert item with formula.
     *
     * @param  Collection  $item
     * @param  array  $formulas
     * @param  bool  $variant_flag
     * @param  array  $families
     * @param  string|null  $mapping_type
     * @return array
     */
    // public function ConvertItemWithFormula(Collection $item, array $formulas, bool $variant_flag = false, array $families = [], string $mapping_type = null)
    // {
    //     try {
    //         $result = [];
    //         $conversion_flag = true;
    //         $mapping_conversion = new MappingConversion();

    //         foreach ($formulas as $formula) {
    //             $formula["from"] = isset($formula["from"]) ? array_filter($formula["from"]) : [];
    //             $formula["to"] = isset($formula["to"]) ? array_filter($formula["to"]) : [];

    //             if (!empty($formula["from"]) && !empty($formula["to"])) {

    //                 // if variant record true
    //                 if ($variant_flag) {
    //                     if (isset($mapping_type) && $mapping_type === "export") {
    //                         $conversion_flag = false;
    //                         foreach ($formula['from'] as $var_key_to => $var_to) {
    //                             $attribute_name = $mapping_conversion->get_explode_attribute_name($var_to);
    //                             if (in_array($attribute_name, array_keys($this->variant_parent_attr_array))) {
    //                                 $formula['from'][$var_key_to] = $this->variant_parent_attr_array[$attribute_name];
    //                                 $conversion_flag = true;
    //                             }
    //                         }
    //                     } else {
    //                         foreach ($formula['to'] as $var_to) {
    //                             if ($mapping_conversion->get_explode_family_name($var_to) != "Variants") {
    //                                 $conversion_flag = false;
    //                                 break;
    //                             } else {
    //                                 $conversion_flag = true;
    //                             }
    //                         }
    //                     }
    //                 }


    //                 if ($conversion_flag) {
    //                     $result = array_replace_recursive($result, $this->createItem($item, $formula, !empty($families) ? $families : []));
    //                 }
    //             }
    //         }
    //         return $result;
    //     } catch (InvalidArgumentException $e) {
    //         Log::channel('mapping')->error($e);
    //         return [];
    //     }
    // }

    /**
     * Convert item with formula.
     *
     * @param  Collection  $item
     * @param  array  $formulas
     * @param  bool  $variant_flag
     * @param  array  $families
     * @param  string|null  $mapping_type
     * @return array
     */
    public function ConvertItemWithFormula(Collection $item, array $formulas, bool $variant_flag = false,Collection $families, string $mapping_type = null)
    {
        try {
            $result = [];
            $conversion_flag = true;
            $mapping_conversion = new MappingConversion();

            $this->mapping_type($mapping_type);

            foreach ($formulas as $formula) {
                $formula["from"] = isset($formula["from"]) ? array_filter($formula["from"]) : [];
                $formula["to"] = isset($formula["to"]) ? array_filter($formula["to"]) : [];

                if (!empty($formula["from"]) && !empty($formula["to"])) {

                    // if variant record true
//                    if ($variant_flag) {
//                        if (isset($mapping_type) && $mapping_type === "export") {
//                            $conversion_flag = false;
//                            foreach ($formula['from'] as $var_key_to => $var_to) {
//                                $attribute_name = $mapping_conversion->get_explode_attribute_name($var_to);
//                                if (in_array($attribute_name, array_keys($this->variant_parent_attr_array))) {
//                                    $formula['from'][$var_key_to] = $this->variant_parent_attr_array[$attribute_name];
//                                    $conversion_flag = true;
//                                }
//                            }
//                        } else {
//                            foreach ($formula['to'] as $var_to) {
//                                if ($mapping_conversion->get_explode_family_name($var_to) != "Variants") {
//                                    $conversion_flag = false;
//                                    break;
//                                } else {
//                                    $conversion_flag = true;
//                                }
//                            }
//                        }
//                    }


                    if ($conversion_flag) {
                        $result = array_replace_recursive($result, $this->createItem($item, $formula, !empty($families) ? $families : []));
                    }
                }
            }
            return $result;
        } catch (InvalidArgumentException $e) {
            Log::channel('mapping')->error($e);
            return [];
        }
    }

    /**
     * Create item with formula.
     *
     * @param  Collection  $item
     * @param  array  $formula
     * @param  array  $families
     * @return array
     */
    public function createItem(Collection $item, array $formula, $families = []): array
    {
        $hidden_units = array();
        $hidden_units_values = array();
        if (isset($families['hidden_units'])) {
            $hidden_units = $families['hidden_units'];
            $families = $families['parent'] ?? [];
        }

        if (!isset($formula["from"]) && !isset($formula["with_formula"]) && !isset($formula["to"])) {
            throw new InvalidArgumentException("Arguments not supplied to perform formula.");
        }

        $result = [];
        $from_input = [];
        $to_input = [];
        $custom_output = [];

        // trim all Collection keys
        $item = $item->has('attributes') ? $item : collect(['attributes' => $item]);

        // Using Collection methods to handle 'attributes'
        if ($item->get('attributes')->isNotEmpty()) {
            $attributesCollection = $item->get('attributes');

            // Get keys and values from the 'attributes' collection
            $item_attribute_keys = $attributesCollection->keys()->map(function ($q) {
                return trim($q);
            });

            $item_attribute_values = $attributesCollection->values();

            // Combine keys and values into a new collection
            $item['attributes'] = $item_attribute_keys->combine($item_attribute_values);
        }


        foreach ($formula["from"] as $family_from) {
            if ($formula['with_formula'] == 'short_code') {
                $result[] = $family_from;
            } else {
                $from = explode(',', $family_from);
                $from_attribute = '';
                $from_family = '';
                if (isset($from[1])) {
                    // below check is used for only choosen family related attribute
                    if (isset($item['family_name'])) {
                        if ($from[0] != $item['family_name']) {
                            if (!str_contains($from[0], ' -> ')) {
                                continue;
                            }
                        }
                    }

                    $from_attribute = $from[1];
                    $from_family = $from[0];


                    // this is for attributes units column
                    if (isset($hidden_units[$from_family])) {
                        if (isset($hidden_units[$from_family][$from_attribute])) {
                            if (isset($formula['to'][0])) {
                                if ($hidden_units[$from_family][$from_attribute] instanceof Collection){
                                    $hidden_units[$from_family][$from_attribute] = $hidden_units[$from_family][$from_attribute]->toArray();
                                }
                                if ($this->mapping_type == 'export'){
                                    if (!is_array($hidden_units[$from_family][$from_attribute])){
                                        $hidden_units[$from_family][$from_attribute] = [$hidden_units[$from_family][$from_attribute]];
                                    }
                                    $hidden_units[$from_family][$from_attribute] = implode(' , ', $hidden_units[$from_family][$from_attribute]);
                                }
                                if ($from_family == 'Variant' && in_array($from_attribute, ['price', 'compare_at_price','cost_price'])) {
                                    $hidden_units_values[$formula['to'][0] . ' Currency'] = $hidden_units[$from_family][$from_attribute];
                                }else{
                                    $hidden_units_values[$formula['to'][0] . ' Unit'] = $hidden_units[$from_family][$from_attribute];
                                }
                            }
                        }
                    }
                } else {
                    $from_attribute = $from[0];
                }


                // this code is for inventory attributes
                if ($from_family != '') {
                    if (str_contains($from_family, ' -> ')) {
                        $from_attribute = 'inventory,'.$from_attribute;
                    }
                }

                if (!isset($item['attributes'][$from_attribute])) {
                    // $item['attributes'][$from[1]] = '';
                    continue;
                }

                if ($item['attributes'][$from_attribute] instanceof Collection) {
                    $item['attributes'][$from_attribute] = $item['attributes'][$from_attribute]->toArray();
                }

                $result[] = [
                    "key" => $from_attribute,
                    "value" => $item['attributes'][$from_attribute]
                ];


                if ($from_family != '') {

                    // this code is for inventory attributes
                    if (str_contains($from_family, ' -> ')) {
                        if (isset($item['attributes'][$from_attribute])){
                            $from_input[$from_family][$from_attribute] = $item['attributes'][$from_attribute];
                        }
                    }
                    else{
                        $from_input[$from_family][$from_attribute] = $item['attributes'][$from_attribute];
                    }
                } else {
                    $from_input[$from_attribute] = $item['attributes'][$from_attribute];
                }
            }
        }

        // $custom_output['from'] = $from_input;
        // $custom_output['with_formula'] = $formula["with_formula"];

        $formula_obj = $this
            ->from($result)
            ->withFormula($formula["with_formula"])
            ->item($families->isEmpty() ? $item : $families);

        if (isset($formula["with"])) {
            if (!empty($formula["with"])) {
                $formula_obj = $formula_obj->with($formula["with"]);
            } else {
                $formula_obj = $formula_obj->with("");
            }
            //            $custom_output['with'] = $formula["with"];
        }
        if (isset($formula["replace"])) {
            if (!empty($formula["replace"])) {
                $formula_obj = $formula_obj->remove($formula["replace"]);
            } else {
                $formula_obj = $formula_obj->remove("");
            }
            //            $custom_output['replace'] = $formula["replace"];
        }
        foreach ($formula['to'] as $formula_to) {
            $formula_to = explode(',', $formula_to);
            if (isset($formula_to[1])) {
                $to_input[$formula_to[0]][] = $formula_to[1];
            } else {
                $to_input[] = $formula_to[0];
            }
        }
        if (!empty($to_input)) {
            $formula_to_converted = $formula_obj->to($to_input);
        }

        $custom_output = $formula_to_converted;
        return array_merge($custom_output, $hidden_units_values);
    }
}
