<?php

namespace Apimio\MappingConnectorPackage\traits;

use Apimio\MappingConnectorPackage\models\Vlookup;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Psr\Log\InvalidArgumentException;


/**
 * Apply formulas to different models.
 *
 * @test php vendor/bin/phpunit tests/Unit/FormulasTraitTest.php
 * */
trait Formulas
{

    private $from       = [];
    private $to       = [];
    private $with       = null;
    private $remove     = null;
    private $formula    = null;
    private $item    = null;
    private $mapping_type = null;

    /**
     * Sets csv keys and values.
     *
     * @param array $from
     *
     * @return self
     * */
    public function from(array $from): self
    {

        $this->from = $from;
        return $this;
    }


    /**
     * Sets csv all keys with values.
     *
     * @param array $item
     *
     * @return self
     * */
    public function item(Collection $item): self
    {

        $this->item = $item;
        return $this;
    }

    /**
     * Pass the formula name to apply that formula.
     *
     * @param string $formula possible values assign, split, merge, replace, slug
     *
     * @return self
     */
    public function withFormula(string $formula): self
    {
        $this->formula = $formula;

        return $this;
    }

    /**
     * Helping elements pass to apply formula.
     *
     * @param string $remove
     *
     * @return self
     * */
    public function remove(string $remove): self
    {
        $this->remove = $remove;

        return $this;
    }

    /**
     * Helping elements pass to apply formula.
     *
     * @param string $with
     *
     * @return self
     * */
    public function with(string $with): self
    {
        $this->with = $with ?? "";

        return $this;
    }

    /**
     * Apply assign formula.
     *
     * @return array
     * */
    private function assign(): array
    {
        try {
            $res = [];

            foreach ($this->from as $from) {
                if (isset($from["value"])) {
                    $res[] = $from["value"];
                }
            }

            return $res;
        } catch (\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }

    /**
     * Apply split formula.
     *
     * @return array
     * */
    private function split(): array
    {
        try {
            $res = [];

            foreach ($this->from as $from) {
                if (isset($from["value"]) && !empty($this->with)) {
                    //                    throw new InvalidArgumentException($this->VALUE_NOT_DEFINED);
                    $res = explode($this->with ?? "", $from["value"]);
                }

                //                if(empty($this->with)) {
                //                    throw new InvalidArgumentException("Empty delimiter, Can't use explode here.");
                //                }

            }

            return $res;
        } catch (\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }

    /**
     * Apply merge formula.
     *
     * @return array
     * */
    private function merge(): array
    {
        try {

            $res = "";
            $res .= $this->from[0]["value"] ?? "";
            $res .= $this->with ?? "";
            $res .= $this->from[1]["value"] ?? "";
            return [$res];
        } catch (\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }



    function fetch_matched_values($attributes, $value)
    {
        $fetch_value = null;
        if (is_iterable($attributes)) {
            foreach ($attributes as $key_attr => $val_attr) {
                if ($key_attr == $value) {
                    $fetch_value = $val_attr;
                    break;
                }
            }
        }
        return $fetch_value;
    }



    /**
     * Apply merge formula.
     *
     * @return array
     * */
    private function short_code(): array
    {
        try {

            $from_tags_string = isset($this->from[0]) ? base64_decode($this->from[0]) : '';
            //            $from_untags_string = strip_tags(html_entity_decode($from_tags_string));
            $converted_values = $from_tags_string;
            $pattern = "/{{([^}]*)}}/";
            $matches = [];

            if (preg_match_all($pattern, $from_tags_string, $matches)) {
                foreach ($matches[0] as $match_key => $match) {
                    $converted_match = "";
                    $explode_inner_value =  isset($matches[1][$match_key]) ? explode(",", $matches[1][$match_key]) : '';
                    if (isset($this->item['attributes'])) {
                        $converted_match = $this->fetch_matched_values($this->item['attributes'], $explode_inner_value[1] ?? '');
                    } else {
                        foreach ($this->item as $family) {
                            if (isset($family['family_name']) && $family['family_name'] == $explode_inner_value[0]) {
                                $converted_match = $this->fetch_matched_values($family['attributes'], $explode_inner_value[1] ?? '');
                                break;
                            }
                        }
                    }

                    $converted_values = str_replace($match, $converted_match, $converted_values);
                }
            }

            $is_description = false;
            $attribute_obj = new \App\Models\Product\Attribute();

            if ($this->to && is_iterable($this->to)) {
                foreach ($this->to as $value) {
                    if ($value && is_iterable($value)) {
                        foreach ($value as $val) {
                            $attribute_obj = $attribute_obj->where('handle', $val)->first();
                            $is_description = $attribute_obj && $attribute_obj->attribute_type_id == 3;
                        }
                    }
                    else{
                        $attribute_obj = $attribute_obj->where('handle', $value)->first();
                        $is_description = $attribute_obj && $attribute_obj->attribute_type_id == 3;
                    }
                }
            }

            if ($is_description) {
                return [html_entity_decode($converted_values)];
            }
            else{
                return [html_entity_decode(strip_tags($converted_values))];
            }

        } catch (\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }


    /**
     * Apply replace formula.
     *
     * @return array
     * */
    private function replace(): array
    {
        try {
            $res = [];

            foreach ($this->from as $from) {
                if (isset($from["value"])) {
                    $res[] = str_replace($this->remove, $this->with ?? "", $from["value"]);
                    //                    throw new InvalidArgumentException($this->VALUE_NOT_DEFINED);
                }
            }

            return $res;
        } catch (\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }

    /**
     * Apply slug formula.
     *
     * @return array
     * */
    private function slug(): array
    {
        try {
            $res = [];

            foreach ($this->from as $from) {
                if (isset($from["value"])) {
                    //                    throw new InvalidArgumentException($this->VALUE_NOT_DEFINED);
                    $res[] =  Str::slug($from["value"]);
                }
            }

            return $res;
        } catch (\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }




    /**
     * Apply vlookup formula.
     *
     * @return array
     * */
    private function vlookup(): array
    {
        try {
            $res = [];

            foreach ($this->from as $from) {
                if (isset($from["value"])) {
                    $res[] = $this->find_vlookup_values($this->with ?? "", $from["value"]);
                }
            }

            return $res;
        } catch (\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }



    /**
     * Apply calculate formula.
     *
     * @return array
     * */
    private function calculate(): array
    {
        try {
            $res = [];

            foreach ($this->from as $from) {
                if(is_array($from["value"])){
                    foreach ($from["value"] as $val_key => $value) {
                        $temp_res[$val_key] = $this->find_calculate_value($this->with ?? "", $value);
                    }
                }
                else{
                    $temp_res = $this->find_calculate_value($this->with ?? "", $from["value"]);
                }
                $res[] = $temp_res;
            }
            return $res;
        } catch (\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }


    /**
     * Apply expand formula.
     *
     * @return array
     * */
    private function expand(): array
    {
        try {
            $res = [];
            foreach ($this->from as $from) {
                if (isset($from["value"])) {
                    $res[] = $this->expanding_value($this->remove, $this->with ?? "", $from["value"]);
                }
            }

            return $res;
        } catch (\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }







    /**
     * Apply formulas.
     *
     * @return array
     * */
    private function applyFormula(): array
    {
        try {
            $result = array();
            if (count($this->from) && !empty($this->formula)) {
                $formula = strtolower($this->formula); // Convert to lowercase
                // throw new InvalidArgumentException("From key and values are empty.");
                switch ($formula) {
                    case ("assign"):
                        $result = $this->assign();
                        break;
                    case ("split"):
                        $result = $this->split();
                        break;
                    case ("merge"):
                        $result = $this->merge();
                        break;
                    case ("short_code"):
                        $result = $this->short_code();
                        break;
                    case ("replace"):
                        $result = $this->replace();
                        break;
                    case ("slug"):
                        $result = $this->slug();
                        break;
                    case ("vlookup"):
                        $result = $this->vlookup();
                        break;
                    case ("calculate"):
                        $result = $this->calculate();
                        break;
                    case ("expand"):
                        $result = $this->expand();
                        break;
                    default:
                        throw new InvalidArgumentException("Invalid formula value provided.");
                }
            }

            //            if(empty($this->formula)) {
            //                throw new InvalidArgumentException("Formula is empty.");
            //            }

            return $result;
        } catch (\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }

    /**
     * The values which should be assigned results.
     *
     * @param array $keys
     *
     * @return array
     * */
    public function to(array $keys): array
    {
        try {
            $final_result = [];
            $this->to = $keys ?? [];
            $result = $this->applyFormula();

            foreach ($keys as $fam_key => $values) {
                $result_value = array_shift($result);

                // implode array values in export
                if ($result_value && is_array($result_value) && $this->mapping_type === "export") {
                    $result_value = array_filter($result_value);
                    $result_value = implode(' , ' , $result_value);
                }

                if (is_array($values)) {
                    foreach ($values as $key => $value) {
                        if (isset($result_value)) {
                            if ($fam_key == 'Variants') {
                                $final_result[$value] = $result_value;
                            } else {
                                $final_result[$fam_key][$value] = $result_value;
                            }
                        }
                    }
                } else {
                    $final_result[$values] = $result_value;
                }
            }

            return $final_result;
        } catch (\InvalidArgumentException $e) {
            Log::error($e);
            return [];
        }
    }




    /**
     * The values which should be assigned results.
     * 
     * @param string $id
     * @param string $value
     * @return string
     */
    public function find_vlookup_values(string $id, string $value): string
    {

        $lookup_val = $value;
        $vlookup_obj = Vlookup::withoutGlobalScopes()->where('id', $id)->get()->first();
        if ($vlookup_obj) {
            $vlookup_values = json_decode($vlookup_obj->payload, true);
            foreach ($vlookup_values as $key_lookup => $value_vlookup) {
                // Check if the key contains double quotes
                if (preg_match('/^".*"$/', $key_lookup)) {

                    // Remove the double quotes from the key for comparison
                    $clean_key = trim($key_lookup, '"');
                    $lookup_val = preg_replace('/(?<![\/\'&\-\_\.\!\?\;\:\(\)0-9])\b' . preg_quote($clean_key, '/') . '\b(?![\/\'&\-\_\.\!\?\;\:\(\)])(?=[\s,]|$)/', $value_vlookup, $lookup_val);

                } else {
                    // Perform normal string replacement if not enclosed in quotes
                    $lookup_val = str_replace($key_lookup, $value_vlookup, $lookup_val);
                }
            }
        }
        return $lookup_val;
    }

    /**
     * The values which should be assigned results.
     *
     * @param string $expression
     * @param string $value
     *
     * @return float|int|mixed|string
     */
    public function find_calculate_value(string $expression, $value): string
    {
        try {
            if (!is_numeric($value)) {
                $value = 0;
            }

            $cal_value = (float)$value; // Ensure the value is a float for accurate calculations

            if (isset($expression)) {
                // Check if the expression includes a percentage
                if (strpos($expression, '%') !== false) {
                    // Extract the numeric part of the percentage and convert to a decimal
                    $percentage = floatval(str_replace('%', '', $expression)) / 100;
                    // Check if the percentage is to be added or subtracted
                    if ($expression[0] == '+') {
                        // Add the percentage of the value to the original value
                        $cal_value += ($cal_value * $percentage);
                    } else if ($expression[0] == '-') {
                        // Subtract the percentage of the value from the original value
                        $cal_value -= abs($cal_value * $percentage);
                    }
                } else {
                    // If no percentage, handle other operations (for completeness)
                    if ($expression[0] == '+' || $expression[0] == '-' || $expression[0] == '*') {
                        $cal_value = eval('return ' . $cal_value . $expression . ';');
                    }
                }
            }

            // Ensure the calculated value is not negative
            if ($cal_value < 0) {
                $cal_value = 0;
            }

            // Round the value to 2 decimal places
            $cal_value = round($cal_value, 2);

        } catch (\Exception $e) {
            $cal_value = $value;
            Log::error($e->getMessage());
        }

        return $cal_value;
    }




    /**
     * The values which should be assigned results.
     *
     * @param string $position
     * @param  $with
     * @param string $value
     *
     * @return string
     * */
    public function expanding_value(string $position, $with, string $value): string
    {
        if ($position == 'start') {
            $expand_val = $with . $value;
        } elseif ($position == 'end') {
            $expand_val = $value . $with;
        } else {
            $expand_val = $value;
        }

        return $expand_val;
    }



    /**
     * Sets csv keys and values.
     *
     * @param array $mapping_type
     *
     * @return self
     * */
    public function  mapping_type(String $mapping_type = null): self
    {

        $this->mapping_type = $mapping_type;
        return $this;
    }
}
